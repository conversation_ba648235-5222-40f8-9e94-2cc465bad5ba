name: Echo CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Get Flutter dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Verify Flutter installation
      working-directory: flutter_echo
      run: flutter doctor -v

    - name: Run Flutter analyze
      working-directory: flutter_echo
      run: flutter analyze --fatal-infos

    - name: Check code formatting
      working-directory: flutter_echo
      run: dart format --set-exit-if-changed .

    - name: Run security audit
      working-directory: flutter_echo
      run: |
        flutter pub deps
        # Add security vulnerability scanning here

    - name: Upload analysis results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: flutter_echo/analysis_results.sarif
      continue-on-error: true

  # Unit and Widget Tests
  test:
    name: Unit & Widget Tests
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Get dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Generate code
      working-directory: flutter_echo
      run: dart run build_runner build --delete-conflicting-outputs

    - name: Run unit tests
      working-directory: flutter_echo
      run: flutter test --coverage --reporter=github

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: flutter_echo/coverage/lcov.info
        flags: unittests
        name: echo-coverage
        fail_ci_if_error: true

    - name: Check coverage threshold
      working-directory: flutter_echo
      run: |
        # Check if coverage meets minimum threshold (90%)
        COVERAGE=$(lcov --summary coverage/lcov.info | grep -E 'lines\.*: [0-9.]+%' | grep -oE '[0-9.]+' | head -1)
        echo "Coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 90" | bc -l) )); then
          echo "Coverage $COVERAGE% is below minimum threshold of 90%"
          exit 1
        fi

  # Integration Tests
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test
    strategy:
      matrix:
        api-level: [29, 33]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Get dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: AVD cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}

    - name: Create AVD and generate snapshot for caching
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."

    - name: Run integration tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        script: |
          cd flutter_echo
          flutter test integration_test/app_test.dart

  # Build Android
  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: [test, integration-test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Get dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Generate code
      working-directory: flutter_echo
      run: dart run build_runner build --delete-conflicting-outputs

    - name: Build APK
      working-directory: flutter_echo
      run: flutter build apk --release

    - name: Build App Bundle
      working-directory: flutter_echo
      run: flutter build appbundle --release

    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: echo-android-apk
        path: flutter_echo/build/app/outputs/flutter-apk/app-release.apk

    - name: Upload App Bundle artifact
      uses: actions/upload-artifact@v3
      with:
        name: echo-android-aab
        path: flutter_echo/build/app/outputs/bundle/release/app-release.aab

  # Build iOS
  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: [test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Get dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Generate code
      working-directory: flutter_echo
      run: dart run build_runner build --delete-conflicting-outputs

    - name: Build iOS (no codesign)
      working-directory: flutter_echo
      run: flutter build ios --release --no-codesign

    - name: Upload iOS build artifact
      uses: actions/upload-artifact@v3
      with:
        name: echo-ios-build
        path: flutter_echo/build/ios/iphoneos/Runner.app

  # Performance Tests
  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: build-android
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Get dependencies
      working-directory: flutter_echo
      run: flutter pub get

    - name: Run performance tests
      working-directory: flutter_echo
      run: flutter test test/performance/

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: flutter_echo/performance_results.json

  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: 'flutter_echo'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-android, build-ios]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
    - name: Download Android artifacts
      uses: actions/download-artifact@v3
      with:
        name: echo-android-aab

    - name: Deploy to Google Play Internal Testing
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.echo.app
        releaseFiles: app-release.aab
        track: internal
        status: completed

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-android, build-ios, performance-test, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: Download Android artifacts
      uses: actions/download-artifact@v3
      with:
        name: echo-android-aab

    - name: Deploy to Google Play Production
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.echo.app
        releaseFiles: app-release.aab
        track: production
        status: completed

  # Notify on Success/Failure
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
    - name: Notify Slack on Success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'Echo CI/CD Pipeline completed successfully! 🎉'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on Failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: 'Echo CI/CD Pipeline failed! 🚨'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
