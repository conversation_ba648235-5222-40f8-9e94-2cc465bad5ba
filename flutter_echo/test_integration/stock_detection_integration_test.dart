/// Stock Detection Integration Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Integration tests for end-to-end stock detection in conversation flow.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'package:flutter_echo/main.dart' as app;
import 'package:flutter_echo/features/stocks/services/stock_detection_service.dart';
import 'package:flutter_echo/features/stocks/data/repositories/stock_repository.dart';
import 'package:flutter_echo/features/chat/data/repositories/conversation_repository.dart';
import 'package:flutter_echo/features/chat/data/models/message_entity.dart';
import 'package:flutter_echo/features/stocks/data/models/stock_mention.dart';
import 'package:flutter_echo/core/storage/hive_setup.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Stock Detection Integration Tests', () {
    late StockDetectionService stockDetectionService;
    late StockRepository stockRepository;
    late ConversationRepository conversationRepository;

    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      await HiveSetup.initialize();
      
      // Initialize repositories
      stockRepository = StockRepository();
      conversationRepository = ConversationRepository();
      stockDetectionService = StockDetectionService(stockRepository);
      
      // Seed test data
      await _seedTestData();
    });

    tearDownAll(() async {
      await Hive.close();
    });

    group('End-to-End Stock Detection', () {
      testWidgets('should detect stocks in conversation and display preview cards', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat screen
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Type a message with stock mentions
        const messageText = 'I think \$AAPL and GOOGL are great investments. Tesla is also interesting.';
        await tester.enterText(find.byType(TextField), messageText);
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle();

        // Wait for stock detection to complete
        await tester.pump(const Duration(seconds: 2));

        // Verify stock mentions are detected and displayed
        expect(find.text('AAPL'), findsWidgets);
        expect(find.text('GOOGL'), findsWidgets);
        expect(find.text('TSLA'), findsWidgets);

        // Verify stock preview cards are shown
        expect(find.byType(StockPreviewCard), findsNWidgets(3));

        // Verify confidence indicators
        expect(find.byIcon(Icons.verified), findsWidgets); // High confidence indicators
      });

      testWidgets('should handle real-time stock detection during typing', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat screen
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Start typing a message with stock mention
        await tester.enterText(find.byType(TextField), 'I want to buy ');
        await tester.pump(const Duration(milliseconds: 500));

        // Continue typing to complete stock mention
        await tester.enterText(find.byType(TextField), 'I want to buy \$AAPL');
        await tester.pump(const Duration(milliseconds: 500));

        // Verify real-time suggestion appears
        expect(find.text('Apple Inc.'), findsOneWidget);
        expect(find.byType(StockSuggestionChip), findsOneWidget);

        // Tap the suggestion to insert it
        await tester.tap(find.byType(StockSuggestionChip));
        await tester.pumpAndSettle();

        // Verify the stock mention is properly formatted in the text field
        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.controller?.text, contains('Apple Inc. (AAPL)'));
      });

      testWidgets('should filter false positives in conversation context', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat screen
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Type a message with potential false positives
        const messageText = 'THE quick brown fox AND lazy dog CAT jumped over.';
        await tester.enterText(find.byType(TextField), messageText);
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle();

        // Wait for processing
        await tester.pump(const Duration(seconds: 1));

        // Verify no stock cards are shown for false positives
        expect(find.byType(StockPreviewCard), findsNothing);
        expect(find.text('No stocks detected'), findsOneWidget);
      });

      testWidgets('should handle mixed stock mention types in single message', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat screen
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Type message with different mention types
        const messageText = '\$AAPL stock, Apple Inc. earnings, and iPhone sales are all related.';
        await tester.enterText(find.byType(TextField), messageText);
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle();

        // Wait for detection
        await tester.pump(const Duration(seconds: 2));

        // Verify only one stock card is shown (deduplication)
        expect(find.byType(StockPreviewCard), findsOneWidget);
        
        // Verify it shows the highest confidence mention
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('Apple Inc.'), findsOneWidget);
        
        // Verify confidence level is displayed
        expect(find.text('Very High'), findsOneWidget); // Confidence level
      });
    });

    group('Stock Preview Card Interactions', () {
      testWidgets('should flip stock card to show detailed metrics', (tester) async {
        // Launch the app and navigate to a conversation with stock mentions
        app.main();
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();
        
        await tester.enterText(find.byType(TextField), '\$AAPL is a great stock');
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Tap the stock card to flip it
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Verify back side content is displayed
        expect(find.text('Market Cap'), findsOneWidget);
        expect(find.text('P/E Ratio'), findsOneWidget);
        expect(find.text('Dividend Yield'), findsOneWidget);
        expect(find.text('Price Target'), findsOneWidget);
        expect(find.textContaining('investment thesis'), findsOneWidget);
      });

      testWidgets('should navigate to stock detail screen when card is tapped', (tester) async {
        // Launch the app and set up stock card
        app.main();
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();
        
        await tester.enterText(find.byType(TextField), '\$AAPL analysis');
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Long press or double tap to navigate to detail screen
        await tester.longPress(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Verify navigation to stock detail screen
        expect(find.text('AAPL Stock Details'), findsOneWidget);
        expect(find.byType(TabBar), findsOneWidget);
        expect(find.text('Overview'), findsOneWidget);
        expect(find.text('Chart'), findsOneWidget);
        expect(find.text('Metrics'), findsOneWidget);
        expect(find.text('News'), findsOneWidget);
      });

      testWidgets('should add stock to favorites from conversation', (tester) async {
        // Launch the app and set up stock card
        app.main();
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();
        
        await tester.enterText(find.byType(TextField), 'GOOGL stock analysis');
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Tap the favorite button on the stock card
        await tester.tap(find.byIcon(Icons.favorite_border));
        await tester.pumpAndSettle();

        // Verify favorite state changed
        expect(find.byIcon(Icons.favorite), findsOneWidget);
        expect(find.byIcon(Icons.favorite_border), findsNothing);

        // Navigate to favorites screen to verify persistence
        await tester.tap(find.byIcon(Icons.favorite));
        await tester.pumpAndSettle();

        // Verify stock appears in favorites list
        expect(find.text('GOOGL'), findsOneWidget);
        expect(find.text('Alphabet Inc.'), findsOneWidget);
      });
    });

    group('Stock Search and Filtering', () {
      testWidgets('should search for stocks and display results', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to stocks screen
        await tester.tap(find.byIcon(Icons.trending_up));
        await tester.pumpAndSettle();

        // Tap search button
        await tester.tap(find.byIcon(Icons.search));
        await tester.pumpAndSettle();

        // Enter search query
        await tester.enterText(find.byType(TextField), 'Apple');
        await tester.pump(const Duration(milliseconds: 500));

        // Verify search results
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('Apple Inc.'), findsOneWidget);
        expect(find.byType(StockPreviewCard), findsOneWidget);

        // Clear search and verify all stocks return
        await tester.tap(find.byIcon(Icons.clear));
        await tester.pumpAndSettle();

        expect(find.byType(StockPreviewCard), findsNWidgets(greaterThan(1)));
      });

      testWidgets('should filter stocks by sector', (tester) async {
        // Launch the app and navigate to stocks screen
        app.main();
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.trending_up));
        await tester.pumpAndSettle();

        // Tap filter button
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();

        // Select Technology sector
        await tester.tap(find.text('Technology'));
        await tester.tap(find.text('Apply'));
        await tester.pumpAndSettle();

        // Verify only technology stocks are shown
        expect(find.text('Technology'), findsWidgets);
        expect(find.text('Healthcare'), findsNothing);
        expect(find.text('Finance'), findsNothing);

        // Verify specific tech stocks
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('GOOGL'), findsOneWidget);
      });
    });

    group('Performance and Caching', () {
      testWidgets('should cache stock data for fast subsequent access', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat and send message with stock mention
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();
        
        await tester.enterText(find.byType(TextField), '\$AAPL stock');
        await tester.tap(find.byIcon(Icons.send));
        
        // Measure first load time
        final stopwatch = Stopwatch()..start();
        await tester.pumpAndSettle(const Duration(seconds: 3));
        final firstLoadTime = stopwatch.elapsedMilliseconds;
        stopwatch.reset();

        // Send another message with same stock
        await tester.enterText(find.byType(TextField), 'More AAPL analysis');
        await tester.tap(find.byIcon(Icons.send));
        
        // Measure cached load time
        stopwatch.start();
        await tester.pumpAndSettle(const Duration(seconds: 1));
        final cachedLoadTime = stopwatch.elapsedMilliseconds;

        // Verify cached load is significantly faster
        expect(cachedLoadTime, lessThan(firstLoadTime * 0.5));
        
        // Verify stock card still displays correctly
        expect(find.byType(StockPreviewCard), findsWidgets);
        expect(find.text('AAPL'), findsWidgets);
      });

      testWidgets('should handle large numbers of stock mentions efficiently', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Send message with many stock mentions
        const largeMessage = '\$AAPL \$GOOGL \$TSLA \$MSFT \$AMZN \$META \$NVDA \$NFLX \$CRM \$ORCL';
        await tester.enterText(find.byType(TextField), largeMessage);
        
        final stopwatch = Stopwatch()..start();
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle(const Duration(seconds: 5));
        stopwatch.stop();

        // Verify reasonable performance (under 5 seconds)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // Verify all stocks are detected and displayed
        expect(find.byType(StockPreviewCard), findsNWidgets(10));
        
        // Verify UI remains responsive
        await tester.tap(find.byType(StockPreviewCard).first);
        await tester.pumpAndSettle();
        expect(find.text('Market Cap'), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle network errors gracefully', (tester) async {
        // This would require mocking network failures
        // For now, we'll test offline behavior
        
        // Launch the app in offline mode
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Send message with stock mention
        await tester.enterText(find.byType(TextField), '\$AAPL stock');
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle();

        // Verify offline indicator or cached data is used
        expect(find.byIcon(Icons.cloud_off), findsOneWidget);
        expect(find.text('Using cached data'), findsOneWidget);
        
        // Verify stock card still displays with available data
        expect(find.byType(StockPreviewCard), findsOneWidget);
      });

      testWidgets('should handle malformed stock data', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to chat
        await tester.tap(find.byIcon(Icons.chat));
        await tester.pumpAndSettle();

        // Send message with invalid ticker
        await tester.enterText(find.byType(TextField), '\$INVALID stock');
        await tester.tap(find.byIcon(Icons.send));
        await tester.pumpAndSettle();

        // Verify graceful handling
        expect(find.text('Stock not found'), findsOneWidget);
        expect(find.byType(StockPreviewCard), findsNothing);
        
        // Verify app doesn't crash
        expect(tester.takeException(), isNull);
      });
    });
  });
}

/// Seed test data for integration tests
Future<void> _seedTestData() async {
  // This would populate the test database with sample stock data
  // Implementation depends on your data seeding strategy
}

/// Mock widgets for testing
class StockPreviewCard extends StatelessWidget {
  const StockPreviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(); // Placeholder
  }
}

class StockSuggestionChip extends StatelessWidget {
  const StockSuggestionChip({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(); // Placeholder
  }
}
