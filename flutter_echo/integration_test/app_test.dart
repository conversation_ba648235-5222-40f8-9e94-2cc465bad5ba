/// Echo Integration Tests - End-to-End User Flows
/// Copyright (c) 2025 Echo Inc.
/// 
/// Integration tests for critical user journeys and app functionality.
/// Tests complete user flows from authentication to conversation completion.

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:patrol/patrol.dart';

import 'package:echo/main.dart' as app;
import 'helpers/integration_test_helpers.dart';
import 'mocks/mock_backend_server.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Echo App Integration Tests', () {
    late MockBackendServer mockServer;
    late IntegrationTestHelpers testHelpers;
    
    setUpAll(() async {
      // Start mock backend server
      mockServer = MockBackendServer();
      await mockServer.start();
      
      // Initialize test helpers
      testHelpers = IntegrationTestHelpers();
      await testHelpers.initialize();
    });
    
    tearDownAll(() async {
      await mockServer.stop();
      await testHelpers.cleanup();
    });
    
    setUp(() async {
      // Reset app state before each test
      await testHelpers.resetAppState();
    });
    
    group('Authentication Flow', () {
      patrolTest('should complete full authentication flow', (PatrolTester $) async {
        // Arrange
        await mockServer.setupAuthenticationMocks();
        
        // Act - Launch app
        await app.main();
        await $.pumpAndSettle();
        
        // Assert - Should show access control screen
        expect($(#accessControlScreen), findsOneWidget);
        
        // Act - Enter access code
        await $(#accessCodeInput).enterText('ECHO2025');
        await $(#submitAccessCodeButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Should navigate to main chat screen
        expect($(#chatScreen), findsOneWidget);
        expect($(#voiceRecordButton), findsOneWidget);
        expect($(#messageInput), findsOneWidget);
      });
      
      patrolTest('should handle invalid access code gracefully', (PatrolTester $) async {
        // Arrange
        await mockServer.setupAuthenticationMocks(validCode: false);
        
        // Act
        await app.main();
        await $.pumpAndSettle();
        
        await $(#accessCodeInput).enterText('INVALID');
        await $(#submitAccessCodeButton).tap();
        await $.pumpAndSettle();
        
        // Assert
        expect($(#errorMessage), findsOneWidget);
        expect($('Invalid access code'), findsOneWidget);
        expect($(#accessControlScreen), findsOneWidget);
      });
    });
    
    group('Voice Conversation Flow', () {
      patrolTest('should complete voice recording and transcription', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await mockServer.setupVoiceMocks();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Start voice recording
        await $(#voiceRecordButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Recording state
        expect($(#recordingIndicator), findsOneWidget);
        expect($('Recording...'), findsOneWidget);
        
        // Act - Simulate recording duration
        await Future.delayed(const Duration(seconds: 2));
        
        // Act - Stop recording
        await $(#voiceRecordButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Processing state
        expect($(#processingIndicator), findsOneWidget);
        expect($('Processing voice...'), findsOneWidget);
        
        // Wait for transcription and AI response
        await $.pumpAndSettle(timeout: const Duration(seconds: 10));
        
        // Assert - Conversation displayed
        expect($(#userMessage), findsOneWidget);
        expect($(#aiResponse), findsOneWidget);
        expect($('Tell me about Apple stock'), findsOneWidget);
      });
      
      patrolTest('should handle microphone permission denial', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await testHelpers.denyMicrophonePermission();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act
        await $(#voiceRecordButton).tap();
        await $.pumpAndSettle();
        
        // Assert
        expect($(#permissionDialog), findsOneWidget);
        expect($('Microphone Permission Required'), findsOneWidget);
        
        // Act - Grant permission
        await $(#grantPermissionButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Should retry recording
        expect($(#recordingIndicator), findsOneWidget);
      });
    });
    
    group('Text Conversation Flow', () {
      patrolTest('should send text message and receive AI response', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await mockServer.setupChatMocks();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Type message
        await $(#messageInput).enterText('What is the current price of Tesla?');
        await $(#sendMessageButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Message sent
        expect($(#userMessage), findsOneWidget);
        expect($('What is the current price of Tesla?'), findsOneWidget);
        
        // Assert - AI response received
        await $.pumpAndSettle(timeout: const Duration(seconds: 5));
        expect($(#aiResponse), findsOneWidget);
        expect($(#stockPreviewCard), findsOneWidget);
      });
      
      patrolTest('should handle network connectivity issues', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await testHelpers.simulateNetworkDisconnection();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act
        await $(#messageInput).enterText('Test message');
        await $(#sendMessageButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Offline mode
        expect($(#offlineIndicator), findsOneWidget);
        expect($('Offline Mode'), findsOneWidget);
        expect($(#retryButton), findsOneWidget);
        
        // Act - Restore connection
        await testHelpers.restoreNetworkConnection();
        await $(#retryButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Message sent successfully
        expect($(#aiResponse), findsOneWidget);
      });
    });
    
    group('Stock Detection and Preview', () {
      patrolTest('should detect stocks and show preview cards', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await mockServer.setupStockMocks();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act
        await $(#messageInput).enterText('Tell me about AAPL and TSLA performance');
        await $(#sendMessageButton).tap();
        await $.pumpAndSettle();
        
        // Wait for AI response with stock detection
        await $.pumpAndSettle(timeout: const Duration(seconds: 5));
        
        // Assert - Stock preview cards displayed
        expect($(#stockPreviewCard), findsNWidgets(2));
        expect($('Apple Inc.'), findsOneWidget);
        expect($('Tesla, Inc.'), findsOneWidget);
        
        // Act - Tap on stock card
        await $(#stockPreviewCard).first.tap();
        await $.pumpAndSettle();
        
        // Assert - Stock details expanded
        expect($(#stockDetailsModal), findsOneWidget);
        expect($(#stockPrice), findsOneWidget);
        expect($(#stockChart), findsOneWidget);
      });
    });
    
    group('Conversation History', () {
      patrolTest('should save and retrieve conversation history', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await mockServer.setupChatMocks();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Send multiple messages
        await $(#messageInput).enterText('First message');
        await $(#sendMessageButton).tap();
        await $.pumpAndSettle();
        
        await $(#messageInput).enterText('Second message');
        await $(#sendMessageButton).tap();
        await $.pumpAndSettle();
        
        // Navigate to history
        await $(#historyButton).tap();
        await $.pumpAndSettle();
        
        // Assert - History displayed
        expect($(#conversationHistoryScreen), findsOneWidget);
        expect($(#conversationItem), findsAtLeastNWidgets(1));
        
        // Act - Resume conversation
        await $(#conversationItem).first.tap();
        await $.pumpAndSettle();
        
        // Assert - Conversation resumed
        expect($(#chatScreen), findsOneWidget);
        expect($('First message'), findsOneWidget);
        expect($('Second message'), findsOneWidget);
      });
    });
    
    group('Performance Tests', () {
      patrolTest('should maintain 60 FPS during voice recording', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await mockServer.setupVoiceMocks();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Start performance monitoring
        final frameMonitor = testHelpers.startFrameMonitoring();
        
        await $(#voiceRecordButton).tap();
        await Future.delayed(const Duration(seconds: 3));
        await $(#voiceRecordButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Performance metrics
        final metrics = frameMonitor.stop();
        expect(metrics.averageFps, greaterThan(55)); // Allow some variance
        expect(metrics.droppedFrames, lessThan(5));
      });
      
      patrolTest('should handle large conversation history efficiently', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        await testHelpers.createLargeConversationHistory(100);
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Navigate to history
        final stopwatch = Stopwatch()..start();
        await $(#historyButton).tap();
        await $.pumpAndSettle();
        stopwatch.stop();
        
        // Assert - Performance threshold
        expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 2 second threshold
        expect($(#conversationItem), findsWidgets);
        
        // Act - Scroll through history
        await $(#conversationHistoryList).scroll(
          delta: const Offset(0, -1000),
          duration: const Duration(milliseconds: 500),
        );
        await $.pumpAndSettle();
        
        // Assert - Smooth scrolling
        expect($(#conversationItem), findsWidgets);
      });
    });
    
    group('Error Handling', () {
      patrolTest('should recover from app crash gracefully', (PatrolTester $) async {
        // Arrange
        await testHelpers.authenticateUser();
        
        await app.main();
        await $.pumpAndSettle();
        
        // Act - Simulate crash scenario
        await testHelpers.simulateAppCrash();
        
        // Restart app
        await app.main();
        await $.pumpAndSettle();
        
        // Assert - App recovers
        expect($(#chatScreen), findsOneWidget);
        expect($(#errorRecoveryMessage), findsOneWidget);
        
        // Act - Dismiss recovery message
        await $(#dismissErrorButton).tap();
        await $.pumpAndSettle();
        
        // Assert - Normal operation resumed
        expect($(#voiceRecordButton), findsOneWidget);
        expect($(#messageInput), findsOneWidget);
      });
    });
  });
}
