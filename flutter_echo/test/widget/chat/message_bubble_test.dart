/// Echo Testing Framework - Message Bubble Widget Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget tests for MessageBubble component with golden file testing.
/// Tests UI rendering, interactions, and visual regression.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';

import '../../helpers/test_helpers.dart';
import '../../../lib/features/chat/domain/entities/message.dart';
import '../../../lib/features/chat/presentation/widgets/message_bubble.dart';

void main() {
  group('MessageBubble Widget Tests', () {
    late Message userMessage;
    late Message aiMessage;
    late Message systemMessage;
    
    setUpAll(() async {
      // Load fonts for golden tests
      await loadAppFonts();
    });
    
    setUp(() {
      userMessage = Message(
        id: 'msg-1',
        content: 'Tell me about Apple stock',
        type: MessageType.user,
        timestamp: DateTime(2025, 1, 15, 10, 30),
        encrypted: false,
      );
      
      aiMessage = Message(
        id: 'msg-2',
        content: 'Apple Inc. (AAPL) is a leading technology company...',
        type: MessageType.ai,
        timestamp: DateTime(2025, 1, 15, 10, 31),
        encrypted: false,
      );
      
      systemMessage = Message(
        id: 'msg-3',
        content: 'Conversation started',
        type: MessageType.system,
        timestamp: DateTime(2025, 1, 15, 10, 29),
        encrypted: false,
      );
    });
    
    group('User Message Bubble', () {
      testWidgets('should display user message correctly', (tester) async {
        // Arrange & Act
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: userMessage),
        );
        
        // Assert
        expect(find.text('Tell me about Apple stock'), findsOneWidget);
        expect(find.byType(MessageBubble), findsOneWidget);
        
        // Verify message bubble styling
        final messageBubble = tester.widget<Container>(
          find.descendant(
            of: find.byType(MessageBubble),
            matching: find.byType(Container),
          ),
        );
        
        expect(messageBubble.decoration, isA<BoxDecoration>());
      });
      
      testWidgets('should show timestamp when tapped', (tester) async {
        // Arrange
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: userMessage),
        );
        
        // Act
        await tester.tap(find.byType(MessageBubble));
        await tester.pumpAndSettle();
        
        // Assert
        expect(find.text('10:30 AM'), findsOneWidget);
      });
      
      testWidgets('should handle long message text wrapping', (tester) async {
        // Arrange
        final longMessage = userMessage.copyWith(
          content: 'This is a very long message that should wrap to multiple lines '
              'when displayed in the message bubble widget to test text overflow '
              'and wrapping behavior in the UI component.',
        );
        
        await TestHelpers.pumpTestWidget(
          tester,
          child: SizedBox(
            width: 300,
            child: MessageBubble(message: longMessage),
          ),
        );
        
        // Assert
        expect(find.byType(MessageBubble), findsOneWidget);
        
        // Verify text doesn't overflow
        final textWidget = tester.widget<Text>(
          find.descendant(
            of: find.byType(MessageBubble),
            matching: find.byType(Text),
          ),
        );
        expect(textWidget.overflow, isNull);
      });
    });
    
    group('AI Message Bubble', () {
      testWidgets('should display AI message with typing animation', (tester) async {
        // Arrange & Act
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(
            message: aiMessage,
            showTypingAnimation: true,
          ),
        );
        
        // Assert
        expect(find.byType(MessageBubble), findsOneWidget);
        expect(find.byType(AnimatedBuilder), findsOneWidget);
      });
      
      testWidgets('should show stock mentions with highlighting', (tester) async {
        // Arrange
        final messageWithStocks = aiMessage.copyWith(
          content: 'Apple (AAPL) and Tesla (TSLA) are both technology stocks.',
          stockMentions: [
            StockMention(
              ticker: 'AAPL',
              companyName: 'Apple Inc.',
              confidence: 0.95,
              startIndex: 7,
              endIndex: 11,
            ),
            StockMention(
              ticker: 'TSLA',
              companyName: 'Tesla Inc.',
              confidence: 0.90,
              startIndex: 23,
              endIndex: 27,
            ),
          ],
        );
        
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: messageWithStocks),
        );
        
        // Assert
        expect(find.byType(RichText), findsOneWidget);
        
        // Verify stock mentions are highlighted
        final richText = tester.widget<RichText>(find.byType(RichText));
        expect(richText.text, isA<TextSpan>());
      });
    });
    
    group('System Message Bubble', () {
      testWidgets('should display system message with different styling', (tester) async {
        // Arrange & Act
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: systemMessage),
        );
        
        // Assert
        expect(find.text('Conversation started'), findsOneWidget);
        expect(find.byType(MessageBubble), findsOneWidget);
      });
    });
    
    group('Golden Tests', () {
      testGoldens('user message bubble golden test', (tester) async {
        // Arrange
        final widget = TestHelpers.createTestWidget(
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: MessageBubble(message: userMessage),
            ),
          ),
        );
        
        // Act
        await tester.pumpWidgetBuilder(
          widget,
          surfaceSize: const Size(400, 200),
        );
        
        // Assert
        await expectLater(
          find.byType(Scaffold),
          matchesGoldenFile('goldens/message_bubble_user.png'),
        );
      });
      
      testGoldens('ai message bubble golden test', (tester) async {
        // Arrange
        final widget = TestHelpers.createTestWidget(
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: MessageBubble(message: aiMessage),
            ),
          ),
        );
        
        // Act
        await tester.pumpWidgetBuilder(
          widget,
          surfaceSize: const Size(400, 200),
        );
        
        // Assert
        await expectLater(
          find.byType(Scaffold),
          matchesGoldenFile('goldens/message_bubble_ai.png'),
        );
      });
      
      testGoldens('system message bubble golden test', (tester) async {
        // Arrange
        final widget = TestHelpers.createTestWidget(
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: MessageBubble(message: systemMessage),
            ),
          ),
        );
        
        // Act
        await tester.pumpWidgetBuilder(
          widget,
          surfaceSize: const Size(400, 200),
        );
        
        // Assert
        await expectLater(
          find.byType(Scaffold),
          matchesGoldenFile('goldens/message_bubble_system.png'),
        );
      });
      
      testGoldens('message bubble with stock mentions golden test', (tester) async {
        // Arrange
        final messageWithStocks = aiMessage.copyWith(
          content: 'Apple (AAPL) is trading at $189.84',
          stockMentions: [
            StockMention(
              ticker: 'AAPL',
              companyName: 'Apple Inc.',
              confidence: 0.95,
              startIndex: 7,
              endIndex: 11,
            ),
          ],
        );
        
        final widget = TestHelpers.createTestWidget(
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: MessageBubble(message: messageWithStocks),
            ),
          ),
        );
        
        // Act
        await tester.pumpWidgetBuilder(
          widget,
          surfaceSize: const Size(400, 200),
        );
        
        // Assert
        await expectLater(
          find.byType(Scaffold),
          matchesGoldenFile('goldens/message_bubble_with_stocks.png'),
        );
      });
    });
    
    group('Accessibility Tests', () {
      testWidgets('should have proper accessibility labels', (tester) async {
        // Arrange & Act
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: userMessage),
        );
        
        // Assert
        expect(
          find.bySemanticsLabel('User message: Tell me about Apple stock'),
          findsOneWidget,
        );
      });
      
      testWidgets('should support screen reader navigation', (tester) async {
        // Arrange
        await TestHelpers.pumpTestWidget(
          tester,
          child: Column(
            children: [
              MessageBubble(message: userMessage),
              MessageBubble(message: aiMessage),
            ],
          ),
        );
        
        // Act & Assert
        expect(find.byType(Semantics), findsNWidgets(2));
      });
    });
    
    group('Performance Tests', () {
      testWidgets('should render quickly with large message content', (tester) async {
        // Arrange
        final largeMessage = userMessage.copyWith(
          content: 'Large message content ' * 100, // 2000+ characters
        );
        
        final stopwatch = Stopwatch()..start();
        
        // Act
        await TestHelpers.pumpTestWidget(
          tester,
          child: MessageBubble(message: largeMessage),
        );
        
        stopwatch.stop();
        
        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // 100ms threshold
        expect(find.byType(MessageBubble), findsOneWidget);
      });
    });
  });
}
