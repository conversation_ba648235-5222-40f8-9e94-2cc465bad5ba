/// Echo Testing Framework - Test Helpers
/// Copyright (c) 2025 Echo Inc.
/// 
/// Centralized test helpers and utilities for the Echo Flutter application.
/// Provides mock setup, test data, and common testing patterns.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../lib/core/api/api_client.dart';
import '../lib/core/services/analytics_service.dart';
import '../lib/core/services/audio_service.dart';
import '../lib/core/services/encryption_service.dart';
import '../lib/core/services/notification_service.dart';
import '../lib/core/services/permission_service.dart';
import '../lib/core/storage/local_storage.dart';
import '../lib/features/auth/data/services/auth_api_service.dart';
import '../lib/features/chat/data/services/chat_api_service.dart';
import '../lib/features/voice/data/services/voice_api_service.dart';

// Generate mocks for external dependencies
@GenerateMocks([
  ApiClient,
  AudioService,
  LocalStorage,
  PermissionService,
  NotificationService,
  AnalyticsService,
  EncryptionService,
  AuthApiService,
  ChatApiService,
  VoiceApiService,
])
import 'test_helpers.mocks.dart';

/// Test helper class providing common testing utilities
class TestHelpers {
  /// Create a test widget with providers and theme
  static Widget createTestWidget({
    required Widget child,
    List<Override> overrides = const [],
    ThemeData? theme,
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        theme: theme ?? _createTestTheme(),
        home: child,
      ),
    );
  }
  
  /// Create a test theme for consistent widget testing
  static ThemeData _createTestTheme() {
    return ThemeData(
      primarySwatch: Colors.blue,
      fontFamily: 'Inter',
      textTheme: const TextTheme(
        bodyLarge: TextStyle(fontSize: 16),
        bodyMedium: TextStyle(fontSize: 14),
        headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
      ),
    );
  }
  
  /// Pump widget with standard test setup
  static Future<void> pumpTestWidget(
    WidgetTester tester, {
    required Widget child,
    List<Override> overrides = const [],
    Duration? duration,
  }) async {
    await tester.pumpWidget(
      createTestWidget(
        child: child,
        overrides: overrides,
      ),
    );
    
    if (duration != null) {
      await tester.pump(duration);
    } else {
      await tester.pumpAndSettle();
    }
  }
  
  /// Wait for async operations to complete
  static Future<void> waitForAsync(WidgetTester tester) async {
    await tester.pumpAndSettle();
    await tester.pump(const Duration(milliseconds: 100));
  }
  
  /// Find widget by key with type safety
  static Finder findByKey<T extends Widget>(String key) {
    return find.byKey(Key(key)).having((w) => w, 'widget', isA<T>());
  }
  
  /// Verify widget exists and is visible
  static void verifyWidgetVisible(Finder finder) {
    expect(finder, findsOneWidget);
    expect(
      tester.widget(finder),
      predicate<Widget>((w) => w.key != null),
    );
  }
  
  /// Create mock provider overrides
  static List<Override> createMockOverrides({
    MockApiClient? apiClient,
    MockAudioService? audioService,
    MockLocalStorage? localStorage,
    MockPermissionService? permissionService,
    MockNotificationService? notificationService,
    MockAnalyticsService? analyticsService,
    MockEncryptionService? encryptionService,
  }) {
    final overrides = <Override>[];
    
    if (apiClient != null) {
      overrides.add(apiClientProvider.overrideWithValue(apiClient));
    }
    if (audioService != null) {
      overrides.add(audioServiceProvider.overrideWithValue(audioService));
    }
    if (localStorage != null) {
      overrides.add(localStorageProvider.overrideWithValue(localStorage));
    }
    if (permissionService != null) {
      overrides.add(permissionServiceProvider.overrideWithValue(permissionService));
    }
    if (notificationService != null) {
      overrides.add(notificationServiceProvider.overrideWithValue(notificationService));
    }
    if (analyticsService != null) {
      overrides.add(analyticsServiceProvider.overrideWithValue(analyticsService));
    }
    if (encryptionService != null) {
      overrides.add(encryptionServiceProvider.overrideWithValue(encryptionService));
    }
    
    return overrides;
  }
}

/// Mock data factory for creating test data
class MockDataFactory {
  /// Create a mock user for testing
  static Map<String, dynamic> createMockUser({
    String id = 'test-user-1',
    String username = 'testuser',
    String email = '<EMAIL>',
    String role = 'user',
  }) {
    return {
      'id': id,
      'username': username,
      'email': email,
      'role': role,
      'isActive': true,
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
  
  /// Create a mock conversation for testing
  static Map<String, dynamic> createMockConversation({
    String id = 'test-conversation-1',
    String sessionId = 'test-session-1',
    String userMessage = 'Tell me about AAPL',
    String aiResponse = 'Apple Inc. is a technology company...',
  }) {
    return {
      'id': id,
      'sessionId': sessionId,
      'timestamp': DateTime.now().toIso8601String(),
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'leadData': {},
      'userId': 'test-user-1',
    };
  }
  
  /// Create mock stock data for testing
  static Map<String, dynamic> createMockStock({
    String ticker = 'AAPL',
    String companyName = 'Apple Inc.',
    double price = 189.84,
    double changePercent = 2.34,
  }) {
    return {
      'ticker': ticker,
      'companyName': companyName,
      'price': price,
      'changePercent': changePercent,
      'marketCap': '2.89T',
      'priceTarget': 220.00,
      'sector': 'Technology',
      'industry': 'Consumer Electronics',
      'thesis': 'Leading consumer technology company...',
      'keywords': [ticker.toLowerCase(), companyName.toLowerCase()],
      'lastUpdated': DateTime.now().toIso8601String(),
      'analystRating': 'Buy',
      'peRatio': 28.5,
      'dividendYield': 0.52,
    };
  }
  
  /// Create mock lead data for testing
  static Map<String, dynamic> createMockLeadData({
    String? name,
    String? email,
    String? phone,
    String investmentExperience = 'intermediate',
    int portfolioSize = 50000,
  }) {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'investmentExperience': investmentExperience,
      'portfolioSize': portfolioSize,
      'investmentGoals': 'wealth-building',
      'riskTolerance': 'moderate',
      'timeline': 'long-term',
      'hasAdvisor': false,
      'qualificationScore': 75,
    };
  }
}

/// Test matchers for custom assertions
class TestMatchers {
  /// Matcher for checking if a widget has specific text
  static Matcher hasText(String text) {
    return predicate<Widget>(
      (widget) {
        if (widget is Text) {
          return widget.data == text;
        }
        return false;
      },
      'has text "$text"',
    );
  }
  
  /// Matcher for checking if a widget is enabled
  static Matcher isEnabled() {
    return predicate<Widget>(
      (widget) {
        if (widget is ElevatedButton) {
          return widget.onPressed != null;
        }
        if (widget is TextButton) {
          return widget.onPressed != null;
        }
        if (widget is OutlinedButton) {
          return widget.onPressed != null;
        }
        return true;
      },
      'is enabled',
    );
  }
  
  /// Matcher for checking if a widget is disabled
  static Matcher isDisabled() {
    return predicate<Widget>(
      (widget) {
        if (widget is ElevatedButton) {
          return widget.onPressed == null;
        }
        if (widget is TextButton) {
          return widget.onPressed == null;
        }
        if (widget is OutlinedButton) {
          return widget.onPressed == null;
        }
        return false;
      },
      'is disabled',
    );
  }
}

/// Provider declarations for dependency injection
final apiClientProvider = Provider<ApiClient>((ref) => throw UnimplementedError());
final audioServiceProvider = Provider<AudioService>((ref) => throw UnimplementedError());
final localStorageProvider = Provider<LocalStorage>((ref) => throw UnimplementedError());
final permissionServiceProvider = Provider<PermissionService>((ref) => throw UnimplementedError());
final notificationServiceProvider = Provider<NotificationService>((ref) => throw UnimplementedError());
final analyticsServiceProvider = Provider<AnalyticsService>((ref) => throw UnimplementedError());
final encryptionServiceProvider = Provider<EncryptionService>((ref) => throw UnimplementedError());
