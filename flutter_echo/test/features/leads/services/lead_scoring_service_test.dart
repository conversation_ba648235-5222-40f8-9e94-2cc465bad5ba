/// Lead Scoring Service Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Unit tests for the lead scoring service with weighted algorithm verification.

import 'package:flutter_test/flutter_test.dart';

import 'package:flutter_echo/features/leads/services/lead_scoring_service.dart';
import 'package:flutter_echo/features/leads/data/models/lead_entity.dart';
import 'package:flutter_echo/core/utils/result.dart';

void main() {
  group('LeadScoringService', () {
    late LeadEntity testLead;

    setUp(() {
      testLead = LeadEntity(
        id: 'test-lead-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );
    });

    group('Score Calculation', () {
      test('should calculate score for complete lead profile', () {
        // Arrange
        final completeLead = testLead.copyWith(
          investmentExperience: InvestmentExperience.advanced,
          portfolioSize: PortfolioSize.from250kTo1m,
          riskTolerance: RiskTolerance.moderate,
          investmentGoals: [
            InvestmentGoal.wealthBuilding,
            InvestmentGoal.retirement,
            InvestmentGoal.diversification,
          ],
          messageCount: 15,
          firstContactAt: DateTime.now().subtract(const Duration(days: 7)),
          lastContactAt: DateTime.now().subtract(const Duration(hours: 2)),
          averageResponseTime: const Duration(minutes: 30),
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(completeLead);

        // Assert
        expect(result.isSuccess, true);
        final score = result.data!;
        expect(score.totalScore, greaterThan(70.0));
        expect(score.qualificationLevel, LeadQualificationLevel.high);
        expect(score.categoryScores, hasLength(6));
        expect(score.factors, hasLength(6));
        expect(score.recommendations, isNotEmpty);
      });

      test('should handle minimal lead data', () {
        // Arrange - testLead has minimal data

        // Act
        final result = LeadScoringService.calculateLeadScore(testLead);

        // Assert
        expect(result.isSuccess, true);
        final score = result.data!;
        expect(score.totalScore, lessThan(30.0));
        expect(score.qualificationLevel, LeadQualificationLevel.veryLow);
      });

      test('should calculate correct weighted scores', () {
        // Arrange
        final lead = testLead.copyWith(
          investmentExperience: InvestmentExperience.expert, // 100 * 0.25 = 25
          portfolioSize: PortfolioSize.over1m, // 100 * 0.30 = 30
          riskTolerance: RiskTolerance.aggressive, // 100 * 0.15 = 15
          investmentGoals: [InvestmentGoal.wealthBuilding], // ~45 * 0.15 = 6.75
          messageCount: 20, // High engagement * 0.10 = ~10
          averageResponseTime: const Duration(minutes: 15), // Fast response * 0.05 = ~5
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final score = result.data!;
        
        // Total should be approximately 91.75
        expect(score.totalScore, closeTo(91.75, 5.0));
        expect(score.qualificationLevel, LeadQualificationLevel.high);
      });
    });

    group('Investment Experience Scoring', () {
      test('should score experience levels correctly', () {
        // Test all experience levels
        final experiences = [
          (InvestmentExperience.beginner, 20.0),
          (InvestmentExperience.intermediate, 50.0),
          (InvestmentExperience.advanced, 80.0),
          (InvestmentExperience.expert, 100.0),
        ];

        for (final (experience, expectedScore) in experiences) {
          // Arrange
          final lead = testLead.copyWith(investmentExperience: experience);

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          final categoryScore = result.data!.categoryScores['investmentExperience']!;
          expect(categoryScore, equals(expectedScore));
        }
      });

      test('should handle null experience', () {
        // Act
        final result = LeadScoringService.calculateLeadScore(testLead);

        // Assert
        expect(result.isSuccess, true);
        final categoryScore = result.data!.categoryScores['investmentExperience']!;
        expect(categoryScore, equals(0.0));
      });
    });

    group('Portfolio Size Scoring', () {
      test('should score portfolio sizes correctly', () {
        // Test all portfolio sizes
        final portfolioSizes = [
          (PortfolioSize.under10k, 10.0),
          (PortfolioSize.from10kTo50k, 25.0),
          (PortfolioSize.from50kTo250k, 50.0),
          (PortfolioSize.from250kTo1m, 75.0),
          (PortfolioSize.over1m, 100.0),
        ];

        for (final (size, expectedScore) in portfolioSizes) {
          // Arrange
          final lead = testLead.copyWith(portfolioSize: size);

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          final categoryScore = result.data!.categoryScores['portfolioSize']!;
          expect(categoryScore, equals(expectedScore));
        }
      });
    });

    group('Risk Tolerance Scoring', () {
      test('should score risk tolerance correctly', () {
        // Test all risk tolerance levels
        final riskTolerances = [
          (RiskTolerance.conservative, 40.0),
          (RiskTolerance.moderate, 70.0),
          (RiskTolerance.aggressive, 100.0),
        ];

        for (final (tolerance, expectedScore) in riskTolerances) {
          // Arrange
          final lead = testLead.copyWith(riskTolerance: tolerance);

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          final categoryScore = result.data!.categoryScores['riskTolerance']!;
          expect(categoryScore, equals(expectedScore));
        }
      });
    });

    group('Investment Goals Scoring', () {
      test('should score based on number and type of goals', () {
        // Arrange
        final highValueGoals = [
          InvestmentGoal.wealthBuilding,
          InvestmentGoal.retirement,
          InvestmentGoal.diversification,
        ];
        final lead = testLead.copyWith(investmentGoals: highValueGoals);

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final categoryScore = result.data!.categoryScores['investmentGoals']!;
        expect(categoryScore, greaterThan(70.0)); // Should be high for multiple high-value goals
      });

      test('should handle empty goals list', () {
        // Arrange
        final lead = testLead.copyWith(investmentGoals: []);

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final categoryScore = result.data!.categoryScores['investmentGoals']!;
        expect(categoryScore, equals(0.0));
      });

      test('should cap score at 100', () {
        // Arrange - all possible goals
        final allGoals = InvestmentGoal.values;
        final lead = testLead.copyWith(investmentGoals: allGoals);

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final categoryScore = result.data!.categoryScores['investmentGoals']!;
        expect(categoryScore, lessThanOrEqualTo(100.0));
      });
    });

    group('Engagement Level Scoring', () {
      test('should score based on message count', () {
        // Test different message counts
        final messageCounts = [0, 5, 15, 25];
        final scores = <double>[];

        for (final count in messageCounts) {
          // Arrange
          final lead = testLead.copyWith(messageCount: count);

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          scores.add(result.data!.categoryScores['engagementLevel']!);
        }

        // Scores should increase with message count
        for (int i = 0; i < scores.length - 1; i++) {
          expect(scores[i], lessThanOrEqualTo(scores[i + 1]));
        }
      });

      test('should score based on conversation duration', () {
        // Arrange
        final now = DateTime.now();
        final lead = testLead.copyWith(
          firstContactAt: now.subtract(const Duration(days: 10)),
          lastContactAt: now.subtract(const Duration(hours: 1)),
          messageCount: 10,
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final categoryScore = result.data!.categoryScores['engagementLevel']!;
        expect(categoryScore, greaterThan(30.0)); // Should get points for duration
      });

      test('should score based on response time', () {
        // Test different response times
        final responseTimes = [
          const Duration(minutes: 15), // Fast
          const Duration(hours: 2), // Moderate
          const Duration(hours: 12), // Slow
          const Duration(days: 2), // Very slow
        ];

        final scores = <double>[];
        for (final responseTime in responseTimes) {
          // Arrange
          final lead = testLead.copyWith(
            averageResponseTime: responseTime,
            messageCount: 5,
          );

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          scores.add(result.data!.categoryScores['engagementLevel']!);
        }

        // Faster response times should get higher scores
        expect(scores[0], greaterThan(scores[1]));
        expect(scores[1], greaterThan(scores[2]));
        expect(scores[2], greaterThan(scores[3]));
      });
    });

    group('Response Quality Scoring', () {
      test('should score based on data completeness', () {
        // Arrange - complete profile
        final completeLead = testLead.copyWith(
          investmentExperience: InvestmentExperience.advanced,
          portfolioSize: PortfolioSize.from250kTo1m,
          riskTolerance: RiskTolerance.moderate,
          investmentGoals: [InvestmentGoal.wealthBuilding],
        );

        // Act
        final completeResult = LeadScoringService.calculateLeadScore(completeLead);
        final incompleteResult = LeadScoringService.calculateLeadScore(testLead);

        // Assert
        expect(completeResult.isSuccess, true);
        expect(incompleteResult.isSuccess, true);
        
        final completeScore = completeResult.data!.categoryScores['responseQuality']!;
        final incompleteScore = incompleteResult.data!.categoryScores['responseQuality']!;
        
        expect(completeScore, greaterThan(incompleteScore));
      });
    });

    group('Qualification Levels', () {
      test('should assign correct qualification levels', () {
        // Test different score ranges
        final testCases = [
          (95.0, LeadQualificationLevel.high),
          (75.0, LeadQualificationLevel.medium),
          (55.0, LeadQualificationLevel.low),
          (25.0, LeadQualificationLevel.veryLow),
        ];

        for (final (targetScore, expectedLevel) in testCases) {
          // Create a lead that should achieve approximately the target score
          final lead = _createLeadWithTargetScore(targetScore);

          // Act
          final result = LeadScoringService.calculateLeadScore(lead);

          // Assert
          expect(result.isSuccess, true);
          expect(result.data!.qualificationLevel, expectedLevel);
        }
      });
    });

    group('Recommendations', () {
      test('should provide appropriate recommendations for high-score leads', () {
        // Arrange
        final highScoreLead = testLead.copyWith(
          investmentExperience: InvestmentExperience.expert,
          portfolioSize: PortfolioSize.over1m,
          riskTolerance: RiskTolerance.aggressive,
          investmentGoals: [InvestmentGoal.wealthBuilding],
          messageCount: 20,
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(highScoreLead);

        // Assert
        expect(result.isSuccess, true);
        final recommendations = result.data!.recommendations;
        expect(recommendations, contains(contains('High-priority')));
        expect(recommendations, contains(contains('premium')));
      });

      test('should provide nurturing recommendations for low-score leads', () {
        // Arrange - minimal lead data results in low score

        // Act
        final result = LeadScoringService.calculateLeadScore(testLead);

        // Assert
        expect(result.isSuccess, true);
        final recommendations = result.data!.recommendations;
        expect(recommendations, contains(contains('Nurture')));
        expect(recommendations, contains(contains('educational')));
      });

      test('should provide specific recommendations based on weak areas', () {
        // Arrange - lead with small portfolio
        final smallPortfolioLead = testLead.copyWith(
          portfolioSize: PortfolioSize.under10k,
          investmentExperience: InvestmentExperience.advanced,
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(smallPortfolioLead);

        // Assert
        expect(result.isSuccess, true);
        final recommendations = result.data!.recommendations;
        expect(recommendations, contains(contains('portfolio growth')));
      });
    });

    group('Score Factors', () {
      test('should provide detailed factor breakdown', () {
        // Arrange
        final lead = testLead.copyWith(
          investmentExperience: InvestmentExperience.intermediate,
          portfolioSize: PortfolioSize.from50kTo250k,
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final factors = result.data!.factors;
        expect(factors, hasLength(6));
        
        // Check that each factor has required properties
        for (final factor in factors) {
          expect(factor.category, isNotEmpty);
          expect(factor.score, greaterThanOrEqualTo(0.0));
          expect(factor.score, lessThanOrEqualTo(100.0));
          expect(factor.weight, greaterThan(0.0));
          expect(factor.description, isNotEmpty);
        }
      });

      test('should calculate weighted scores correctly', () {
        // Arrange
        final lead = testLead.copyWith(
          investmentExperience: InvestmentExperience.expert, // 100 points
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(lead);

        // Assert
        expect(result.isSuccess, true);
        final factors = result.data!.factors;
        final experienceFactor = factors.firstWhere(
          (f) => f.category == 'Investment Experience',
        );
        
        expect(experienceFactor.score, equals(100.0));
        expect(experienceFactor.weight, equals(0.25));
        expect(experienceFactor.weightedScore, equals(25.0));
      });
    });

    group('Error Handling', () {
      test('should handle null lead gracefully', () {
        // This test would require modifying the service to accept nullable leads
        // For now, we'll test with minimal valid lead
        final result = LeadScoringService.calculateLeadScore(testLead);
        expect(result.isSuccess, true);
      });

      test('should handle extreme values gracefully', () {
        // Arrange - lead with extreme values
        final extremeLead = testLead.copyWith(
          messageCount: 1000000,
          averageResponseTime: const Duration(days: 365),
        );

        // Act
        final result = LeadScoringService.calculateLeadScore(extremeLead);

        // Assert
        expect(result.isSuccess, true);
        final score = result.data!;
        expect(score.totalScore, lessThanOrEqualTo(100.0));
        expect(score.totalScore, greaterThanOrEqualTo(0.0));
      });
    });

    group('Statistics', () {
      test('should provide scoring statistics', () {
        // Act
        final stats = LeadScoringService.getScoringStatistics();

        // Assert
        expect(stats, containsPair('weights', isA<Map<String, double>>()));
        expect(stats, containsPair('thresholds', isA<Map<String, double>>()));
        expect(stats, containsPair('categories', isA<List<String>>()));
        
        final weights = stats['weights'] as Map<String, double>;
        final totalWeight = weights.values.reduce((a, b) => a + b);
        expect(totalWeight, closeTo(1.0, 0.001)); // Should sum to 1.0
      });
    });
  });
}

/// Helper function to create a lead with approximately the target score
LeadEntity _createLeadWithTargetScore(double targetScore) {
  if (targetScore >= 80) {
    return LeadEntity(
      id: 'high-score-lead',
      investmentExperience: InvestmentExperience.expert,
      portfolioSize: PortfolioSize.over1m,
      riskTolerance: RiskTolerance.aggressive,
      investmentGoals: [InvestmentGoal.wealthBuilding, InvestmentGoal.retirement],
      messageCount: 20,
      averageResponseTime: const Duration(minutes: 15),
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  } else if (targetScore >= 60) {
    return LeadEntity(
      id: 'medium-score-lead',
      investmentExperience: InvestmentExperience.intermediate,
      portfolioSize: PortfolioSize.from50kTo250k,
      riskTolerance: RiskTolerance.moderate,
      investmentGoals: [InvestmentGoal.wealthBuilding],
      messageCount: 10,
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  } else if (targetScore >= 40) {
    return LeadEntity(
      id: 'low-score-lead',
      investmentExperience: InvestmentExperience.beginner,
      portfolioSize: PortfolioSize.from10kTo50k,
      messageCount: 3,
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  } else {
    return LeadEntity(
      id: 'very-low-score-lead',
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
    );
  }
}
