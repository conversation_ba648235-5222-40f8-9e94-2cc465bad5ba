/// Stock Preview Card Widget Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget tests for the animated stock preview card with flip animations.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:flutter_echo/features/stocks/presentation/widgets/stock_preview_card.dart';
import 'package:flutter_echo/features/stocks/data/models/stock_entity.dart';

void main() {
  group('StockPreviewCard', () {
    late StockEntity testStock;

    setUp(() {
      testStock = StockEntity(
        ticker: 'AAPL',
        companyName: 'Apple Inc.',
        price: 150.25,
        changePercent: 2.5,
        sector: 'Technology',
        industry: 'Consumer Electronics',
        marketCap: 2500000000000, // $2.5T
        peRatio: 28.5,
        dividendYield: 0.5,
        analystRating: 'Buy',
        priceTarget: 165.0,
        investmentThesis: 'Strong ecosystem and innovation pipeline make Apple a compelling long-term investment.',
        keywords: ['iPhone', 'Mac', 'iOS', 'technology'],
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );
    });

    Widget createTestWidget({
      StockEntity? stock,
      VoidCallback? onTap,
      VoidCallback? onFavoriteToggle,
      bool showFavoriteButton = true,
      bool enableFlipAnimation = true,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: StockPreviewCard(
              stock: stock ?? testStock,
              onTap: onTap,
              onFavoriteToggle: onFavoriteToggle,
              showFavoriteButton: showFavoriteButton,
              enableFlipAnimation: enableFlipAnimation,
            ),
          ),
        ),
      );
    }

    group('Initial Display', () {
      testWidgets('should display stock information on front side', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('Apple Inc.'), findsOneWidget);
        expect(find.text('\$150.25'), findsOneWidget);
        expect(find.text('+2.5%'), findsOneWidget);
        expect(find.text('Technology'), findsOneWidget);
      });

      testWidgets('should show positive change in green', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final changeText = tester.widget<Text>(find.text('+2.5%'));
        final textStyle = changeText.style;
        expect(textStyle?.color, Colors.green);
      });

      testWidgets('should show negative change in red', (tester) async {
        // Arrange
        final negativeStock = testStock.copyWith(changePercent: -1.8);

        // Act
        await tester.pumpWidget(createTestWidget(stock: negativeStock));

        // Assert
        expect(find.text('-1.8%'), findsOneWidget);
        final changeText = tester.widget<Text>(find.text('-1.8%'));
        final textStyle = changeText.style;
        expect(textStyle?.color, Colors.red);
      });

      testWidgets('should display analyst rating badge', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Buy'), findsOneWidget);
        
        // Check that it's displayed as a chip or badge
        expect(find.byType(Chip), findsOneWidget);
      });

      testWidgets('should show favorite button when enabled', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(showFavoriteButton: true));

        // Assert
        expect(find.byIcon(Icons.favorite_border), findsOneWidget);
      });

      testWidgets('should hide favorite button when disabled', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(showFavoriteButton: false));

        // Assert
        expect(find.byIcon(Icons.favorite_border), findsNothing);
        expect(find.byIcon(Icons.favorite), findsNothing);
      });
    });

    group('Flip Animation', () {
      testWidgets('should flip to back side when tapped', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Tap to flip
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle(); // Wait for animation to complete

        // Assert - back side content should be visible
        expect(find.text('Market Cap'), findsOneWidget);
        expect(find.text('P/E Ratio'), findsOneWidget);
        expect(find.text('Dividend Yield'), findsOneWidget);
        expect(find.text('Price Target'), findsOneWidget);
        
        // Investment thesis should be visible (truncated)
        expect(find.textContaining('Strong ecosystem'), findsOneWidget);
      });

      testWidgets('should flip back to front side on second tap', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // First tap - flip to back
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();
        
        // Second tap - flip to front
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert - front side content should be visible again
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('Apple Inc.'), findsOneWidget);
        expect(find.text('\$150.25'), findsOneWidget);
      });

      testWidgets('should not flip when animation is disabled', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(enableFlipAnimation: false));
        
        // Tap the card
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert - should still show front side
        expect(find.text('AAPL'), findsOneWidget);
        expect(find.text('Market Cap'), findsNothing);
      });

      testWidgets('should animate flip transition smoothly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Start flip animation
        await tester.tap(find.byType(StockPreviewCard));
        
        // Check intermediate animation state
        await tester.pump(const Duration(milliseconds: 300)); // Mid-animation
        
        // Should be in transition (Transform widget should be present)
        expect(find.byType(Transform), findsOneWidget);
        
        // Complete animation
        await tester.pumpAndSettle();
        
        // Should show back side
        expect(find.text('Market Cap'), findsOneWidget);
      });
    });

    group('Back Side Content', () {
      testWidgets('should display financial metrics', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('\$2.50T'), findsOneWidget); // Market cap
        expect(find.text('28.5'), findsOneWidget); // P/E ratio
        expect(find.text('0.5%'), findsOneWidget); // Dividend yield
        expect(find.text('\$165.00'), findsOneWidget); // Price target
      });

      testWidgets('should display truncated investment thesis', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(find.textContaining('Strong ecosystem'), findsOneWidget);
        
        // Should have "Read more" or similar indicator for full thesis
        expect(find.text('Read more'), findsOneWidget);
      });

      testWidgets('should handle missing financial data gracefully', (tester) async {
        // Arrange
        final incompleteStock = testStock.copyWith(
          peRatio: null,
          dividendYield: null,
          priceTarget: null,
        );

        // Act
        await tester.pumpWidget(createTestWidget(stock: incompleteStock));
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('N/A'), findsNWidgets(3)); // Should show N/A for missing data
      });
    });

    group('Favorite Functionality', () {
      testWidgets('should toggle favorite state when favorite button is tapped', (tester) async {
        // Arrange
        bool favoriteToggled = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onFavoriteToggle: () => favoriteToggled = true,
        ));
        
        await tester.tap(find.byIcon(Icons.favorite_border));

        // Assert
        expect(favoriteToggled, true);
      });

      testWidgets('should show filled heart for favorited stocks', (tester) async {
        // Arrange
        final favoritedStock = testStock.copyWith(isFavorite: true);

        // Act
        await tester.pumpWidget(createTestWidget(stock: favoritedStock));

        // Assert
        expect(find.byIcon(Icons.favorite), findsOneWidget);
        expect(find.byIcon(Icons.favorite_border), findsNothing);
      });

      testWidgets('should not interfere with flip animation', (tester) async {
        // Arrange
        bool favoriteToggled = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onFavoriteToggle: () => favoriteToggled = true,
        ));
        
        // Tap favorite button
        await tester.tap(find.byIcon(Icons.favorite_border));
        await tester.pump();
        
        // Tap card to flip (should not trigger favorite again)
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(favoriteToggled, true); // Should only be called once
        expect(find.text('Market Cap'), findsOneWidget); // Should flip to back
      });
    });

    group('Hover Effects', () {
      testWidgets('should scale on hover', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Simulate hover
        final gesture = await tester.createGesture(kind: PointerDeviceKind.mouse);
        await gesture.addPointer(location: Offset.zero);
        addTearDown(gesture.removePointer);
        
        await gesture.moveTo(tester.getCenter(find.byType(StockPreviewCard)));
        await tester.pump();

        // Assert
        final transform = tester.widget<Transform>(find.byType(Transform));
        expect(transform.transform.getMaxScaleOnAxis(), greaterThan(1.0));
      });

      testWidgets('should return to normal scale when hover ends', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Simulate hover and then move away
        final gesture = await tester.createGesture(kind: PointerDeviceKind.mouse);
        await gesture.addPointer(location: Offset.zero);
        addTearDown(gesture.removePointer);
        
        await gesture.moveTo(tester.getCenter(find.byType(StockPreviewCard)));
        await tester.pump();
        
        await gesture.moveTo(const Offset(1000, 1000)); // Move away
        await tester.pumpAndSettle();

        // Assert
        final transform = tester.widget<Transform>(find.byType(Transform));
        expect(transform.transform.getMaxScaleOnAxis(), equals(1.0));
      });
    });

    group('Tap Handling', () {
      testWidgets('should call onTap callback when card is tapped', (tester) async {
        // Arrange
        bool cardTapped = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => cardTapped = true,
        ));
        
        await tester.tap(find.byType(StockPreviewCard));

        // Assert
        expect(cardTapped, true);
      });

      testWidgets('should handle tap and flip simultaneously', (tester) async {
        // Arrange
        bool cardTapped = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => cardTapped = true,
        ));
        
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(cardTapped, true);
        expect(find.text('Market Cap'), findsOneWidget); // Should also flip
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.bySemanticsLabel('Stock card for AAPL'), findsOneWidget);
        expect(find.bySemanticsLabel('Add AAPL to favorites'), findsOneWidget);
      });

      testWidgets('should announce flip state changes', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Flip to back
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(find.bySemanticsLabel('Showing detailed metrics for AAPL'), findsOneWidget);
      });

      testWidgets('should support keyboard navigation', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Focus the card
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        // Assert
        final cardFinder = find.byType(StockPreviewCard);
        expect(Focus.of(tester.element(cardFinder)).hasFocus, true);
      });

      testWidgets('should support keyboard activation', (tester) async {
        // Arrange
        bool cardTapped = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => cardTapped = true,
        ));
        
        // Focus and activate with Enter key
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pump();

        // Assert
        expect(cardTapped, true);
      });
    });

    group('Performance', () {
      testWidgets('should not rebuild unnecessarily', (tester) async {
        // Arrange
        int buildCount = 0;
        Widget countingWidget = Builder(
          builder: (context) {
            buildCount++;
            return createTestWidget();
          },
        );

        // Act
        await tester.pumpWidget(countingWidget);
        await tester.pump(); // Additional pump
        await tester.pump(); // Another pump

        // Assert
        expect(buildCount, equals(1)); // Should only build once
      });

      testWidgets('should handle rapid taps gracefully', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Rapid taps
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.byType(StockPreviewCard));
          await tester.pump(const Duration(milliseconds: 50));
        }
        
        await tester.pumpAndSettle();

        // Assert
        // Should handle gracefully without errors
        expect(tester.takeException(), isNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle very long company names', (tester) async {
        // Arrange
        final longNameStock = testStock.copyWith(
          companyName: 'Very Long Company Name That Should Be Truncated Properly',
        );

        // Act
        await tester.pumpWidget(createTestWidget(stock: longNameStock));

        // Assert
        expect(find.textContaining('Very Long Company'), findsOneWidget);
        
        // Should use ellipsis for overflow
        final text = tester.widget<Text>(find.textContaining('Very Long Company'));
        expect(text.overflow, TextOverflow.ellipsis);
      });

      testWidgets('should handle zero or negative prices', (tester) async {
        // Arrange
        final zeroPriceStock = testStock.copyWith(price: 0.0);

        // Act
        await tester.pumpWidget(createTestWidget(stock: zeroPriceStock));

        // Assert
        expect(find.text('\$0.00'), findsOneWidget);
      });

      testWidgets('should handle missing investment thesis', (tester) async {
        // Arrange
        final noThesisStock = testStock.copyWith(investmentThesis: null);

        // Act
        await tester.pumpWidget(createTestWidget(stock: noThesisStock));
        await tester.tap(find.byType(StockPreviewCard));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('No investment thesis available'), findsOneWidget);
      });
    });
  });
}

/// Mock keyboard key for testing
class LogicalKeyboardKey {
  static const tab = LogicalKeyboardKey._('Tab');
  static const enter = LogicalKeyboardKey._('Enter');
  
  final String _name;
  const LogicalKeyboardKey._(this._name);
  
  @override
  String toString() => _name;
}
