/// Stock Detection Service Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Unit tests for the intelligent stock detection service with pattern matching.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:flutter_echo/features/stocks/services/stock_detection_service.dart';
import 'package:flutter_echo/features/stocks/data/repositories/stock_repository.dart';
import 'package:flutter_echo/features/stocks/data/models/stock_entity.dart';
import 'package:flutter_echo/features/stocks/data/models/stock_mention.dart';
import 'package:flutter_echo/core/utils/result.dart';

import 'stock_detection_service_test.mocks.dart';

@GenerateMocks([StockRepository])
void main() {
  group('StockDetectionService', () {
    late MockStockRepository mockRepository;
    late StockDetectionService service;
    late List<StockEntity> mockStocks;

    setUp(() {
      mockRepository = MockStockRepository();
      service = StockDetectionService(mockRepository);

      // Setup mock stock data
      mockStocks = [
        StockEntity(
          ticker: 'AAPL',
          companyName: 'Apple Inc.',
          price: 150.0,
          changePercent: 2.5,
          sector: 'Technology',
          industry: 'Consumer Electronics',
          keywords: ['iPhone', 'Mac', 'iOS', 'technology'],
          createdAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        ),
        StockEntity(
          ticker: 'GOOGL',
          companyName: 'Alphabet Inc.',
          price: 2800.0,
          changePercent: -1.2,
          sector: 'Technology',
          industry: 'Internet Services',
          keywords: ['search', 'advertising', 'cloud', 'AI'],
          createdAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        ),
        StockEntity(
          ticker: 'TSLA',
          companyName: 'Tesla, Inc.',
          price: 800.0,
          changePercent: 5.8,
          sector: 'Consumer Discretionary',
          industry: 'Electric Vehicles',
          keywords: ['electric', 'automotive', 'battery', 'energy'],
          createdAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        ),
      ];

      // Setup repository mock
      when(mockRepository.getAllStocks()).thenAnswer(
        (_) async => Result.success(mockStocks),
      );
    });

    group('Ticker Detection', () {
      test('should detect dollar-prefixed tickers with high confidence', () async {
        // Arrange
        const text = 'I think \$AAPL is a great investment for the long term.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        
        final mention = mentions.first;
        expect(mention.ticker, 'AAPL');
        expect(mention.companyName, 'Apple Inc.');
        expect(mention.matchedText, '\$AAPL');
        expect(mention.matchType, StockMentionType.ticker);
        expect(mention.confidence, greaterThan(0.9));
      });

      test('should detect bare tickers with context validation', () async {
        // Arrange
        const text = 'AAPL stock is performing well in the market today.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        
        final mention = mentions.first;
        expect(mention.ticker, 'AAPL');
        expect(mention.matchedText, 'AAPL');
        expect(mention.confidence, greaterThan(0.6));
      });

      test('should filter out common false positive words', () async {
        // Arrange
        const text = 'THE quick brown fox jumps over AND lazy dog.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!, isEmpty);
      });

      test('should handle multiple ticker mentions', () async {
        // Arrange
        const text = 'I own \$AAPL, GOOGL, and TSLA in my portfolio.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(3));
        
        final tickers = mentions.map((m) => m.ticker).toSet();
        expect(tickers, containsAll(['AAPL', 'GOOGL', 'TSLA']));
      });

      test('should calculate confidence based on context', () async {
        // Arrange
        const highContextText = 'I bought \$AAPL stock for my investment portfolio.';
        const lowContextText = 'AAPL is a fruit company name.';

        // Act
        final highResult = await service.detectStocks(highContextText);
        final lowResult = await service.detectStocks(lowContextText);

        // Assert
        expect(highResult.isSuccess, true);
        expect(lowResult.isSuccess, true);
        
        final highMention = highResult.data!.first;
        final lowMention = lowResult.data!.first;
        
        expect(highMention.confidence, greaterThan(lowMention.confidence));
      });
    });

    group('Company Name Detection', () {
      test('should detect exact company names', () async {
        // Arrange
        const text = 'Apple Inc. reported strong quarterly earnings.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        
        final mention = mentions.first;
        expect(mention.ticker, 'AAPL');
        expect(mention.companyName, 'Apple Inc.');
        expect(mention.matchedText, 'Apple Inc.');
        expect(mention.matchType, StockMentionType.companyName);
        expect(mention.confidence, closeTo(0.85, 0.05));
      });

      test('should detect partial company names', () async {
        // Arrange
        const text = 'Tesla is revolutionizing the automotive industry.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        
        final mention = mentions.first;
        expect(mention.ticker, 'TSLA');
        expect(mention.matchedText, 'Tesla');
      });

      test('should handle case-insensitive matching', () async {
        // Arrange
        const text = 'apple inc is a technology company.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        expect(mentions.first.ticker, 'AAPL');
      });
    });

    group('Keyword Detection', () {
      test('should detect stock-related keywords near tickers', () async {
        // Arrange
        const text = 'The iPhone sales boosted AAPL performance this quarter.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        
        final mention = mentions.first;
        expect(mention.ticker, 'AAPL');
        expect(mention.confidence, greaterThan(0.5));
      });

      test('should detect contextual stock mentions', () async {
        // Arrange
        const text = 'Electric vehicle stocks are trending. TSLA might benefit.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        expect(mentions, hasLength(1));
        expect(mentions.first.ticker, 'TSLA');
      });

      test('should handle keyword-only detection', () async {
        // Arrange
        const text = 'iPhone sales are strong this quarter, boosting technology stocks.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        // Should detect Apple through iPhone keyword
        final appleMentions = result.data!.where((m) => m.ticker == 'AAPL');
        expect(appleMentions, isNotEmpty);
      });
    });

    group('Confidence Scoring', () {
      test('should assign highest confidence to dollar-prefixed tickers', () async {
        // Arrange
        const text = '\$AAPL vs AAPL vs Apple Inc. vs iPhone';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        
        // Find dollar-prefixed mention
        final dollarMention = mentions.firstWhere(
          (m) => m.matchedText == '\$AAPL',
        );
        
        expect(dollarMention.confidence, greaterThan(0.9));
      });

      test('should score company names higher than keywords', () async {
        // Arrange
        const text = 'Apple Inc. iPhone technology stock investment';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        
        final companyMention = mentions.firstWhere(
          (m) => m.matchType == StockMentionType.companyName,
        );
        final keywordMention = mentions.firstWhere(
          (m) => m.matchType == StockMentionType.keyword,
        );
        
        expect(companyMention.confidence, greaterThan(keywordMention.confidence));
      });

      test('should adjust confidence based on surrounding context', () async {
        // Arrange
        const stockContext = 'I want to buy AAPL stock for my portfolio.';
        const nonStockContext = 'AAPL is just a random word here.';

        // Act
        final stockResult = await service.detectStocks(stockContext);
        final nonStockResult = await service.detectStocks(nonStockContext);

        // Assert
        expect(stockResult.isSuccess, true);
        expect(nonStockResult.isSuccess, true);
        
        final stockMention = stockResult.data!.first;
        final nonStockMention = nonStockResult.data!.first;
        
        expect(stockMention.confidence, greaterThan(nonStockMention.confidence));
      });
    });

    group('Deduplication and Sorting', () {
      test('should remove duplicate mentions', () async {
        // Arrange
        const text = '\$AAPL AAPL Apple Inc. iPhone - all refer to the same company.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        
        // Should have multiple mentions but all for AAPL
        final appleMentions = mentions.where((m) => m.ticker == 'AAPL');
        expect(appleMentions, isNotEmpty);
        
        // Should be sorted by confidence (highest first)
        for (int i = 0; i < mentions.length - 1; i++) {
          expect(mentions[i].confidence, 
                 greaterThanOrEqualTo(mentions[i + 1].confidence));
        }
      });

      test('should sort mentions by confidence descending', () async {
        // Arrange
        const text = 'AAPL Apple Inc. \$AAPL iPhone stock investment';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mentions = result.data!;
        
        // Verify descending confidence order
        for (int i = 0; i < mentions.length - 1; i++) {
          expect(mentions[i].confidence, 
                 greaterThanOrEqualTo(mentions[i + 1].confidence));
        }
      });
    });

    group('Context Extraction', () {
      test('should extract relevant context around mentions', () async {
        // Arrange
        const text = 'The technology sector is performing well. AAPL stock has gained 5% today due to strong iPhone sales.';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mention = result.data!.first;
        
        expect(mention.context, contains('AAPL'));
        expect(mention.context, contains('stock'));
        expect(mention.context.length, lessThanOrEqualTo(100)); // Default context length
      });

      test('should handle mentions at text boundaries', () async {
        // Arrange
        const text = 'AAPL';

        // Act
        final result = await service.detectStocks(text);

        // Assert
        expect(result.isSuccess, true);
        final mention = result.data!.first;
        expect(mention.context, equals('AAPL'));
      });
    });

    group('Cache Management', () {
      test('should cache stock data for performance', () async {
        // Arrange
        const text = 'AAPL is a good stock.';

        // Act
        await service.detectStocks(text);
        await service.detectStocks(text); // Second call should use cache

        // Assert
        // Repository should only be called once
        verify(mockRepository.getAllStocks()).called(1);
      });

      test('should refresh cache after expiry', () async {
        // Arrange
        const text = 'AAPL stock analysis.';
        
        // Act
        await service.detectStocks(text);
        service.clearCache(); // Force cache clear
        await service.detectStocks(text);

        // Assert
        // Repository should be called twice
        verify(mockRepository.getAllStocks()).called(2);
      });

      test('should handle cache loading errors gracefully', () async {
        // Arrange
        when(mockRepository.getAllStocks()).thenAnswer(
          (_) async => Result.error('Database error'),
        );

        // Act
        final result = await service.detectStocks('AAPL stock');

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Database error'));
      });
    });

    group('Error Handling', () {
      test('should handle empty text input', () async {
        // Act
        final result = await service.detectStocks('');

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!, isEmpty);
      });

      test('should handle null or whitespace input', () async {
        // Act
        final result1 = await service.detectStocks('   ');
        final result2 = await service.detectStocks('\n\t  ');

        // Assert
        expect(result1.isSuccess, true);
        expect(result1.data!, isEmpty);
        expect(result2.isSuccess, true);
        expect(result2.data!, isEmpty);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(mockRepository.getAllStocks()).thenAnswer(
          (_) async => Result.error('Network error'),
        );

        // Act
        final result = await service.detectStocks('AAPL stock');

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Network error'));
      });

      test('should handle malformed stock data', () async {
        // Arrange
        final malformedStocks = [
          StockEntity(
            ticker: '', // Empty ticker
            companyName: 'Invalid Company',
            price: 0.0,
            changePercent: 0.0,
            sector: 'Unknown',
            industry: 'Unknown',
            createdAt: DateTime.now(),
            lastUpdated: DateTime.now(),
          ),
        ];
        
        when(mockRepository.getAllStocks()).thenAnswer(
          (_) async => Result.success(malformedStocks),
        );

        // Act
        final result = await service.detectStocks('Some text');

        // Assert
        expect(result.isSuccess, true);
        // Should handle gracefully and return empty results
        expect(result.data!, isEmpty);
      });
    });

    group('Statistics and Metrics', () {
      test('should provide detection statistics', () {
        // Act
        final stats = service.getDetectionStatistics();

        // Assert
        expect(stats, containsPair('cacheSize', isA<int>()));
        expect(stats, containsPair('keywordCount', isA<int>()));
        expect(stats, containsPair('cacheExpiry', isA<int>()));
      });

      test('should track cache performance', () async {
        // Arrange
        const text = 'AAPL GOOGL TSLA stocks';

        // Act
        await service.detectStocks(text);
        final stats = service.getDetectionStatistics();

        // Assert
        expect(stats['cacheSize'], greaterThan(0));
        expect(stats['cacheLastUpdated'], isNotNull);
      });
    });
  });
}
