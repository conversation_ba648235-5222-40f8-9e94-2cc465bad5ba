/// Speech-to-Text Service Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Unit tests for the OpenAI Whisper API integration service.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'dart:io';

import 'package:flutter_echo/features/voice/services/speech_to_text_service.dart';
import 'package:flutter_echo/features/voice/data/models/audio_recording_entity.dart';
import 'package:flutter_echo/core/utils/result.dart';

import 'speech_to_text_service_test.mocks.dart';

@GenerateMocks([Dio, File])
void main() {
  group('SpeechToTextService', () {
    late MockDio mockDio;
    late MockFile mockFile;
    late AudioRecordingEntity testRecording;

    setUp(() {
      mockDio = MockDio();
      mockFile = MockFile();
      SpeechToTextService.setDioForTesting(mockDio);

      testRecording = AudioRecordingEntity(
        id: 'test-recording-1',
        filePath: '/path/to/test.aac',
        duration: const Duration(seconds: 30),
        fileSize: 1024000,
        sampleRate: 16000,
        bitRate: 128000,
        codec: 'aac',
        quality: AudioQuality.high,
        createdAt: DateTime.now(),
      );
    });

    tearDown(() {
      SpeechToTextService.resetDioForTesting();
    });

    group('Transcription', () {
      test('should transcribe audio successfully with JSON format', () async {
        // Arrange
        final mockResponse = {
          'text': 'Hello, this is a test transcription.',
          'segments': [
            {
              'id': 0,
              'seek': 0,
              'start': 0.0,
              'end': 3.5,
              'text': 'Hello, this is a test transcription.',
              'tokens': [15496, 11, 428, 318, 257, 1332, 28535, 13],
              'temperature': 0.0,
              'avg_logprob': -0.3,
              'compression_ratio': 1.2,
              'no_speech_prob': 0.1,
              'words': [
                {'word': 'Hello', 'start': 0.0, 'end': 0.5},
                {'word': 'this', 'start': 0.8, 'end': 1.0},
                {'word': 'is', 'start': 1.0, 'end': 1.2},
                {'word': 'a', 'start': 1.5, 'end': 1.6},
                {'word': 'test', 'start': 1.8, 'end': 2.2},
                {'word': 'transcription', 'start': 2.5, 'end': 3.5},
              ]
            }
          ]
        };

        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: mockResponse,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          format: TranscriptionFormat.json,
          includeTimestamps: true,
        );

        // Assert
        expect(result.isSuccess, true);
        final transcription = result.data!;
        expect(transcription.text, 'Hello, this is a test transcription.');
        expect(transcription.confidence, 0.7); // Calculated from avg_logprob
        expect(transcription.segments, hasLength(1));
        expect(transcription.segments!.first.words, hasLength(6));
        
        verify(mockDio.post(
          'https://api.openai.com/v1/audio/transcriptions',
          data: any,
          options: any,
        )).called(1);
      });

      test('should handle text-only format', () async {
        // Arrange
        const expectedText = 'This is a simple text transcription.';
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: expectedText,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          format: TranscriptionFormat.text,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.text, expectedText);
        expect(result.data!.segments, isNull);
      });

      test('should handle SRT format', () async {
        // Arrange
        const srtContent = '''1
00:00:00,000 --> 00:00:03,500
Hello, this is a test transcription.

''';
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: srtContent,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          format: TranscriptionFormat.srt,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.text, contains('Hello, this is a test transcription.'));
        expect(result.data!.format, TranscriptionFormat.srt);
      });

      test('should handle VTT format', () async {
        // Arrange
        const vttContent = '''WEBVTT

00:00:00.000 --> 00:00:03.500
Hello, this is a test transcription.

''';
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: vttContent,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          format: TranscriptionFormat.vtt,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.text, contains('Hello, this is a test transcription.'));
        expect(result.data!.format, TranscriptionFormat.vtt);
      });
    });

    group('Language Detection', () {
      test('should detect language automatically', () async {
        // Arrange
        final mockResponse = {
          'text': 'Bonjour, ceci est un test.',
          'language': 'fr',
        };

        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: mockResponse,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.language, 'fr');
        expect(result.data!.text, 'Bonjour, ceci est un test.');
      });

      test('should use specified language', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: {'text': 'Hello world'},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          language: 'en',
        );

        // Assert
        expect(result.isSuccess, true);
        
        // Verify language was included in request
        final capturedData = verify(mockDio.post(
          any,
          data: captureAny,
          options: any,
        )).captured.first as FormData;
        
        expect(capturedData.fields.any((field) => 
          field.key == 'language' && field.value == 'en'), true);
      });
    });

    group('Error Handling', () {
      test('should handle API timeout', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenThrow(DioException(
          type: DioExceptionType.connectionTimeout,
          requestOptions: RequestOptions(path: ''),
          message: 'Connection timeout',
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('timeout'));
      });

      test('should handle rate limiting', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenThrow(DioException(
          type: DioExceptionType.badResponse,
          response: Response(
            statusCode: 429,
            statusMessage: 'Too Many Requests',
            requestOptions: RequestOptions(path: ''),
          ),
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('rate limit'));
      });

      test('should handle file size limit', () async {
        // Arrange
        final largeRecording = testRecording.copyWith(
          fileSize: 26 * 1024 * 1024, // 26MB - over limit
        );

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: largeRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('file size'));
      });

      test('should handle invalid audio format', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenThrow(DioException(
          type: DioExceptionType.badResponse,
          response: Response(
            statusCode: 400,
            data: {'error': {'message': 'Invalid audio format'}},
            requestOptions: RequestOptions(path: ''),
          ),
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Invalid audio format'));
      });

      test('should handle network errors', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenThrow(DioException(
          type: DioExceptionType.connectionError,
          requestOptions: RequestOptions(path: ''),
          message: 'Network error',
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Network error'));
      });
    });

    group('Local Fallback', () {
      test('should attempt local transcription on API failure', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenThrow(DioException(
          type: DioExceptionType.connectionError,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          useLocalFallback: true,
        );

        // Assert
        // Local fallback would return a basic transcription
        expect(result.isSuccess, true);
        expect(result.data!.text, isNotEmpty);
        expect(result.data!.isLocalFallback, true);
      });

      test('should prefer API over local when available', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: {'text': 'API transcription'},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          useLocalFallback: true,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.text, 'API transcription');
        expect(result.data!.isLocalFallback, false);
      });
    });

    group('Confidence Scoring', () {
      test('should calculate confidence from avg_logprob', () {
        // Act
        final confidence1 = SpeechToTextService.calculateConfidence(-0.1);
        final confidence2 = SpeechToTextService.calculateConfidence(-0.5);
        final confidence3 = SpeechToTextService.calculateConfidence(-1.0);

        // Assert
        expect(confidence1, greaterThan(confidence2));
        expect(confidence2, greaterThan(confidence3));
        expect(confidence1, lessThanOrEqualTo(1.0));
        expect(confidence3, greaterThanOrEqualTo(0.0));
      });

      test('should handle missing confidence data', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: {'text': 'Test without confidence'},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data!.confidence, isNull);
      });
    });

    group('Request Validation', () {
      test('should validate file exists', () async {
        // Arrange
        final invalidRecording = testRecording.copyWith(
          filePath: '/nonexistent/file.aac',
        );

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: invalidRecording,
        );

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('file not found'));
      });

      test('should validate supported models', () async {
        // Arrange
        when(mockDio.post(
          any,
          data: any,
          options: any,
        )).thenAnswer((_) async => Response(
          data: {'text': 'Test'},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        ));

        // Act
        final result = await SpeechToTextService.transcribeAudio(
          recording: testRecording,
          model: 'whisper-1',
        );

        // Assert
        expect(result.isSuccess, true);
        
        // Verify model was included in request
        final capturedData = verify(mockDio.post(
          any,
          data: captureAny,
          options: any,
        )).captured.first as FormData;
        
        expect(capturedData.fields.any((field) => 
          field.key == 'model' && field.value == 'whisper-1'), true);
      });
    });
  });
}

/// Transcription format enumeration for testing
enum TranscriptionFormat {
  json,
  text,
  srt,
  vtt,
}

/// Extension for transcription format
extension TranscriptionFormatExtension on TranscriptionFormat {
  String get value {
    switch (this) {
      case TranscriptionFormat.json:
        return 'json';
      case TranscriptionFormat.text:
        return 'text';
      case TranscriptionFormat.srt:
        return 'srt';
      case TranscriptionFormat.vtt:
        return 'vtt';
    }
  }
}
