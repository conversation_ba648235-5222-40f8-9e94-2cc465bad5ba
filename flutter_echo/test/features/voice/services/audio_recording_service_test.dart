/// Audio Recording Service Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Unit tests for the audio recording service with platform-specific optimizations.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:flutter_echo/features/voice/services/audio_recording_service.dart';
import 'package:flutter_echo/core/utils/result.dart';
import 'package:flutter_echo/core/constants/app_constants.dart';

import 'audio_recording_service_test.mocks.dart';

@GenerateMocks([FlutterSoundRecorder, Permission])
void main() {
  group('AudioRecordingService', () {
    late MockFlutterSoundRecorder mockRecorder;
    late AudioRecordingService service;

    setUp(() {
      mockRecorder = MockFlutterSoundRecorder();
      service = AudioRecordingService();
      // Inject mock recorder for testing
      AudioRecordingService.setRecorderForTesting(mockRecorder);
    });

    tearDown(() {
      AudioRecordingService.resetRecorderForTesting();
    });

    group('Initialization', () {
      test('should initialize recorder successfully', () async {
        // Arrange
        when(mockRecorder.openRecorder()).thenAnswer((_) async => null);
        when(mockRecorder.isRecorderInitialised()).thenReturn(true);

        // Act
        final result = await AudioRecordingService.initialize();

        // Assert
        expect(result.isSuccess, true);
        verify(mockRecorder.openRecorder()).called(1);
      });

      test('should handle initialization failure', () async {
        // Arrange
        when(mockRecorder.openRecorder()).thenThrow(Exception('Init failed'));

        // Act
        final result = await AudioRecordingService.initialize();

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Failed to initialize'));
      });

      test('should dispose recorder properly', () async {
        // Arrange
        when(mockRecorder.closeRecorder()).thenAnswer((_) async => null);

        // Act
        await AudioRecordingService.dispose();

        // Assert
        verify(mockRecorder.closeRecorder()).called(1);
      });
    });

    group('Permission Handling', () {
      test('should request microphone permission', () async {
        // This would require mocking Permission.microphone
        // For now, we'll test the permission check logic
        expect(AudioRecordingService.hasPermission, isFalse);
      });

      test('should handle permission denied', () async {
        // Arrange - simulate permission denied
        AudioRecordingService.setPermissionForTesting(false);

        // Act
        final result = await AudioRecordingService.startRecording();

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Microphone permission'));
      });
    });

    group('Recording Operations', () {
      setUp(() {
        // Setup common mocks for recording operations
        when(mockRecorder.isRecording).thenReturn(false);
        when(mockRecorder.isPaused).thenReturn(false);
        AudioRecordingService.setPermissionForTesting(true);
      });

      test('should start recording with correct parameters', () async {
        // Arrange
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenAnswer((_) async => null);

        // Act
        final result = await AudioRecordingService.startRecording();

        // Assert
        expect(result.isSuccess, true);
        verify(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: AppConstants.voiceRecordingBitRate,
          sampleRate: AppConstants.voiceRecordingSampleRate,
          numChannels: 1,
        )).called(1);
      });

      test('should generate unique file names', () async {
        // Arrange
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenAnswer((_) async => null);

        // Act
        final result1 = await AudioRecordingService.startRecording();
        await AudioRecordingService.stopRecording();
        final result2 = await AudioRecordingService.startRecording();

        // Assert
        expect(result1.isSuccess, true);
        expect(result2.isSuccess, true);
        // File names should be different
        expect(result1.data, isNot(equals(result2.data)));
      });

      test('should pause recording', () async {
        // Arrange
        when(mockRecorder.isRecording).thenReturn(true);
        when(mockRecorder.pauseRecorder()).thenAnswer((_) async => null);

        // Act
        final result = await AudioRecordingService.pauseRecording();

        // Assert
        expect(result.isSuccess, true);
        verify(mockRecorder.pauseRecorder()).called(1);
      });

      test('should resume recording', () async {
        // Arrange
        when(mockRecorder.isPaused).thenReturn(true);
        when(mockRecorder.resumeRecorder()).thenAnswer((_) async => null);

        // Act
        final result = await AudioRecordingService.resumeRecording();

        // Assert
        expect(result.isSuccess, true);
        verify(mockRecorder.resumeRecorder()).called(1);
      });

      test('should stop recording and return file path', () async {
        // Arrange
        const expectedPath = '/path/to/recording.aac';
        when(mockRecorder.isRecording).thenReturn(true);
        when(mockRecorder.stopRecorder()).thenAnswer((_) async => expectedPath);

        // Act
        final result = await AudioRecordingService.stopRecording();

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, equals(expectedPath));
        verify(mockRecorder.stopRecorder()).called(1);
      });

      test('should cancel recording and clean up', () async {
        // Arrange
        when(mockRecorder.isRecording).thenReturn(true);
        when(mockRecorder.stopRecorder()).thenAnswer((_) async => '/temp/file.aac');

        // Act
        final result = await AudioRecordingService.cancelRecording();

        // Assert
        expect(result.isSuccess, true);
        verify(mockRecorder.stopRecorder()).called(1);
      });

      test('should handle recording errors', () async {
        // Arrange
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenThrow(Exception('Recording failed'));

        // Act
        final result = await AudioRecordingService.startRecording();

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('Failed to start recording'));
      });
    });

    group('Real-time Monitoring', () {
      test('should provide amplitude stream', () async {
        // Arrange
        final amplitudeController = StreamController<double>();
        when(mockRecorder.onProgress).thenAnswer((_) => amplitudeController.stream.map(
          (amplitude) => RecordingDisposition(
            duration: const Duration(seconds: 1),
            decibels: amplitude,
          ),
        ));

        // Act
        final stream = AudioRecordingService.getAmplitudeStream();

        // Assert
        expect(stream, isA<Stream<double>>());
        
        // Cleanup
        amplitudeController.close();
      });

      test('should provide duration stream', () async {
        // Arrange
        final durationController = StreamController<Duration>();
        when(mockRecorder.onProgress).thenAnswer((_) => durationController.stream.map(
          (duration) => RecordingDisposition(
            duration: duration,
            decibels: -30.0,
          ),
        ));

        // Act
        final stream = AudioRecordingService.getDurationStream();

        // Assert
        expect(stream, isA<Stream<Duration>>());
        
        // Cleanup
        durationController.close();
      });

      test('should handle max duration limit', () async {
        // Arrange
        const maxDuration = Duration(minutes: 5);
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenAnswer((_) async => null);

        // Act
        final result = await AudioRecordingService.startRecording(
          maxDuration: maxDuration,
        );

        // Assert
        expect(result.isSuccess, true);
        // Verify that max duration is respected
        // This would require additional implementation in the service
      });
    });

    group('File Management', () {
      test('should generate valid file paths', () {
        // Act
        final path1 = AudioRecordingService.generateFilePath();
        final path2 = AudioRecordingService.generateFilePath('custom_name');

        // Assert
        expect(path1, contains('recording_'));
        expect(path1, endsWith('.aac'));
        expect(path2, contains('custom_name'));
        expect(path2, endsWith('.aac'));
      });

      test('should clean up temporary files', () async {
        // This would test file cleanup functionality
        // Implementation depends on file system mocking
        expect(true, true); // Placeholder
      });
    });

    group('State Management', () {
      test('should track recording state correctly', () {
        // Arrange
        when(mockRecorder.isRecording).thenReturn(false);
        when(mockRecorder.isPaused).thenReturn(false);

        // Act & Assert
        expect(AudioRecordingService.isRecording, false);
        expect(AudioRecordingService.isPaused, false);
        expect(AudioRecordingService.currentState, AudioRecordingState.idle);
      });

      test('should update state during recording lifecycle', () async {
        // Arrange
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenAnswer((_) async => null);
        when(mockRecorder.isRecording).thenReturn(true);
        AudioRecordingService.setPermissionForTesting(true);

        // Act
        await AudioRecordingService.startRecording();

        // Assert
        expect(AudioRecordingService.currentState, AudioRecordingState.recording);
      });
    });

    group('Error Handling', () {
      test('should handle recorder not initialized', () async {
        // Arrange
        when(mockRecorder.isRecorderInitialised()).thenReturn(false);

        // Act
        final result = await AudioRecordingService.startRecording();

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('not initialized'));
      });

      test('should handle invalid operations', () async {
        // Arrange
        when(mockRecorder.isRecording).thenReturn(false);

        // Act
        final result = await AudioRecordingService.pauseRecording();

        // Assert
        expect(result.isError, true);
        expect(result.errorMessage, contains('not recording'));
      });

      test('should provide detailed error information', () async {
        // Arrange
        when(mockRecorder.startRecorder(
          toFile: any,
          codec: any,
          bitRate: any,
          sampleRate: any,
          numChannels: any,
        )).thenThrow(PlatformException(
          code: 'PERMISSION_DENIED',
          message: 'Microphone access denied',
        ));

        // Act
        final result = await AudioRecordingService.startRecording();

        // Assert
        expect(result.isError, true);
        expect(result.exception, isA<PlatformException>());
      });
    });
  });
}

/// Mock recording disposition for testing
class RecordingDisposition {
  final Duration duration;
  final double? decibels;

  RecordingDisposition({
    required this.duration,
    this.decibels,
  });
}

/// Audio recording state enumeration for testing
enum AudioRecordingState {
  idle,
  recording,
  paused,
  stopped,
}

/// Platform exception for testing
class PlatformException implements Exception {
  final String code;
  final String? message;

  PlatformException({
    required this.code,
    this.message,
  });

  @override
  String toString() => 'PlatformException($code, $message)';
}
