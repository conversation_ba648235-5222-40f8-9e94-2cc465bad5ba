/// Voice Recording Widget Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget tests for the voice recording interface with visual feedback.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:flutter_echo/features/voice/presentation/widgets/voice_recording_widget.dart';
import 'package:flutter_echo/features/voice/services/audio_recording_service.dart';
import 'package:flutter_echo/features/voice/services/voice_activity_detection_service.dart';
import 'package:flutter_echo/core/utils/result.dart';

import 'voice_recording_widget_test.mocks.dart';

@GenerateMocks([AudioRecordingService, VoiceActivityDetectionService])
void main() {
  group('VoiceRecordingWidget', () {
    late MockAudioRecordingService mockRecordingService;
    late MockVoiceActivityDetectionService mockVADService;

    setUp(() {
      mockRecordingService = MockAudioRecordingService();
      mockVADService = MockVoiceActivityDetectionService();
    });

    Widget createTestWidget({
      VoidCallback? onRecordingComplete,
      VoidCallback? onRecordingError,
      bool showVisualFeedback = true,
      Duration? maxDuration,
    }) {
      return ProviderScope(
        overrides: [
          // Override providers with mocks
          audioRecordingServiceProvider.overrideWithValue(mockRecordingService),
          voiceActivityDetectionServiceProvider.overrideWithValue(mockVADService),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: VoiceRecordingWidget(
              onRecordingComplete: onRecordingComplete,
              onRecordingError: onRecordingError,
              showVisualFeedback: showVisualFeedback,
              maxDuration: maxDuration,
            ),
          ),
        ),
      );
    }

    group('Initial State', () {
      testWidgets('should display microphone button in idle state', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        when(mockRecordingService.isPaused).thenReturn(false);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.mic), findsOneWidget);
        expect(find.byIcon(Icons.stop), findsNothing);
        expect(find.byIcon(Icons.pause), findsNothing);
        
        // Check button is enabled
        final micButton = tester.widget<FloatingActionButton>(
          find.byType(FloatingActionButton),
        );
        expect(micButton.onPressed, isNotNull);
      });

      testWidgets('should show visual feedback when enabled', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);

        // Act
        await tester.pumpWidget(createTestWidget(showVisualFeedback: true));

        // Assert
        expect(find.byType(CustomPaint), findsWidgets);
        expect(find.byType(AnimatedContainer), findsWidgets);
      });

      testWidgets('should hide visual feedback when disabled', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);

        // Act
        await tester.pumpWidget(createTestWidget(showVisualFeedback: false));

        // Assert
        // Should have fewer visual elements
        expect(find.byType(CustomPaint), findsNothing);
      });
    });

    group('Recording Lifecycle', () {
      testWidgets('should start recording when microphone button is tapped', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        when(mockRecordingService.startRecording()).thenAnswer(
          (_) async => Result.success('/path/to/recording.aac'),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 1)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byIcon(Icons.mic));
        await tester.pump();

        // Assert
        verify(mockRecordingService.startRecording()).called(1);
      });

      testWidgets('should show recording state UI during recording', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.isPaused).thenReturn(false);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.7),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 5)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        expect(find.byIcon(Icons.stop), findsOneWidget);
        expect(find.byIcon(Icons.pause), findsOneWidget);
        expect(find.text('00:05'), findsOneWidget); // Duration display
      });

      testWidgets('should stop recording when stop button is tapped', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.stopRecording()).thenAnswer(
          (_) async => Result.success('/path/to/recording.aac'),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 3)),
        );

        bool recordingCompleted = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onRecordingComplete: () => recordingCompleted = true,
        ));
        await tester.tap(find.byIcon(Icons.stop));
        await tester.pump();

        // Assert
        verify(mockRecordingService.stopRecording()).called(1);
        expect(recordingCompleted, true);
      });

      testWidgets('should pause and resume recording', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.isPaused).thenReturn(false);
        when(mockRecordingService.pauseRecording()).thenAnswer(
          (_) async => Result.success(null),
        );
        when(mockRecordingService.resumeRecording()).thenAnswer(
          (_) async => Result.success(null),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.3),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 2)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Pause recording
        await tester.tap(find.byIcon(Icons.pause));
        await tester.pump();

        // Assert pause was called
        verify(mockRecordingService.pauseRecording()).called(1);

        // Setup for resume
        when(mockRecordingService.isPaused).thenReturn(true);
        await tester.pump();

        // Resume recording
        await tester.tap(find.byIcon(Icons.play_arrow));
        await tester.pump();

        // Assert resume was called
        verify(mockRecordingService.resumeRecording()).called(1);
      });
    });

    group('Visual Feedback', () {
      testWidgets('should animate amplitude visualization', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.periodic(
            const Duration(milliseconds: 100),
            (i) => (i % 10) / 10.0, // Varying amplitude
          ),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.periodic(
            const Duration(seconds: 1),
            (i) => Duration(seconds: i),
          ),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Let animation run
        await tester.pump(const Duration(milliseconds: 500));

        // Assert
        expect(find.byType(CustomPaint), findsWidgets);
        
        // Check that amplitude visualization is present
        final customPaint = tester.widget<CustomPaint>(
          find.byType(CustomPaint).first,
        );
        expect(customPaint.painter, isNotNull);
      });

      testWidgets('should show recording duration', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(minutes: 1, seconds: 30)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        expect(find.text('01:30'), findsOneWidget);
      });

      testWidgets('should show voice activity detection feedback', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockVADService.isVoiceDetected).thenReturn(true);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.8),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 5)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Assert
        // Should show visual indication of voice detection
        expect(find.byType(AnimatedContainer), findsWidgets);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle recording start failure', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        when(mockRecordingService.startRecording()).thenAnswer(
          (_) async => Result.error('Permission denied'),
        );

        bool errorOccurred = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onRecordingError: () => errorOccurred = true,
        ));
        await tester.tap(find.byIcon(Icons.mic));
        await tester.pump();

        // Assert
        expect(errorOccurred, true);
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('should handle recording stop failure', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.stopRecording()).thenAnswer(
          (_) async => Result.error('Failed to save recording'),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 3)),
        );

        bool errorOccurred = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          onRecordingError: () => errorOccurred = true,
        ));
        await tester.tap(find.byIcon(Icons.stop));
        await tester.pump();

        // Assert
        expect(errorOccurred, true);
      });

      testWidgets('should handle permission denied gracefully', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        when(mockRecordingService.startRecording()).thenAnswer(
          (_) async => Result.error('Microphone permission denied'),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byIcon(Icons.mic));
        await tester.pump();

        // Assert
        expect(find.text('Microphone permission denied'), findsOneWidget);
      });
    });

    group('Max Duration Handling', () {
      testWidgets('should stop recording at max duration', (tester) async {
        // Arrange
        const maxDuration = Duration(seconds: 5);
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.stopRecording()).thenAnswer(
          (_) async => Result.success('/path/to/recording.aac'),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(maxDuration),
        );

        bool recordingCompleted = false;

        // Act
        await tester.pumpWidget(createTestWidget(
          maxDuration: maxDuration,
          onRecordingComplete: () => recordingCompleted = true,
        ));
        await tester.pump();

        // Simulate reaching max duration
        await tester.pump(maxDuration);

        // Assert
        verify(mockRecordingService.stopRecording()).called(1);
        expect(recordingCompleted, true);
      });

      testWidgets('should show warning near max duration', (tester) async {
        // Arrange
        const maxDuration = Duration(seconds: 10);
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 8)), // Near max
        );

        // Act
        await tester.pumpWidget(createTestWidget(maxDuration: maxDuration));
        await tester.pump();

        // Assert
        // Should show warning indicator (e.g., red color, warning icon)
        expect(find.byIcon(Icons.warning), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.bySemanticsLabel('Start recording'), findsOneWidget);
      });

      testWidgets('should announce recording state changes', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        when(mockRecordingService.startRecording()).thenAnswer(
          (_) async => Result.success('/path/to/recording.aac'),
        );
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 1)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byIcon(Icons.mic));
        await tester.pump();

        // Assert
        expect(find.bySemanticsLabel('Stop recording'), findsOneWidget);
      });

      testWidgets('should support keyboard navigation', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);

        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Focus the microphone button
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        // Assert
        final micButton = find.byIcon(Icons.mic);
        expect(Focus.of(tester.element(micButton)).hasFocus, true);
      });
    });

    group('Performance', () {
      testWidgets('should not rebuild unnecessarily', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(false);
        
        int buildCount = 0;
        Widget countingWidget = Builder(
          builder: (context) {
            buildCount++;
            return createTestWidget();
          },
        );

        // Act
        await tester.pumpWidget(countingWidget);
        await tester.pump(); // Additional pump
        await tester.pump(); // Another pump

        // Assert
        expect(buildCount, equals(1)); // Should only build once
      });

      testWidgets('should dispose streams properly', (tester) async {
        // Arrange
        when(mockRecordingService.isRecording).thenReturn(true);
        when(mockRecordingService.getAmplitudeStream()).thenAnswer(
          (_) => Stream.value(0.5),
        );
        when(mockRecordingService.getDurationStream()).thenAnswer(
          (_) => Stream.value(const Duration(seconds: 1)),
        );

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Remove widget
        await tester.pumpWidget(Container());

        // Assert
        // Streams should be disposed (no memory leaks)
        // This is implicit - if streams aren't disposed, test framework will detect leaks
      });
    });
  });
}

/// Mock providers for testing
final audioRecordingServiceProvider = Provider<AudioRecordingService>((ref) {
  throw UnimplementedError();
});

final voiceActivityDetectionServiceProvider = Provider<VoiceActivityDetectionService>((ref) {
  throw UnimplementedError();
});

/// Keyboard key for testing
class LogicalKeyboardKey {
  static const tab = LogicalKeyboardKey._('Tab');
  
  final String _name;
  const LogicalKeyboardKey._(this._name);
  
  @override
  String toString() => _name;
}
