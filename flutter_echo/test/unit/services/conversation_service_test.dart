/// Echo Testing Framework - Conversation Service Unit Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Unit tests for ConversationService business logic.
/// Tests conversation creation, AI integration, and error handling.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fake_async/fake_async.dart';

import '../../helpers/test_helpers.dart';
import '../../../lib/core/models/result.dart';
import '../../../lib/features/chat/data/models/conversation_model.dart';
import '../../../lib/features/chat/data/models/message_model.dart';
import '../../../lib/features/chat/domain/entities/conversation.dart';
import '../../../lib/features/chat/domain/services/conversation_service.dart';

void main() {
  group('ConversationService', () {
    late ConversationService conversationService;
    late MockChatApiService mockChatApiService;
    late MockAudioService mockAudioService;
    late MockEncryptionService mockEncryptionService;
    late MockAnalyticsService mockAnalyticsService;
    
    setUp(() {
      mockChatApiService = MockChatApiService();
      mockAudioService = MockAudioService();
      mockEncryptionService = MockEncryptionService();
      mockAnalyticsService = MockAnalyticsService();
      
      conversationService = ConversationService(
        chatApiService: mockChatApiService,
        audioService: mockAudioService,
        encryptionService: mockEncryptionService,
        analyticsService: mockAnalyticsService,
      );
    });
    
    group('createConversation', () {
      test('should create conversation successfully with valid message', () async {
        // Arrange
        const message = 'Tell me about Apple stock';
        final expectedConversation = MockDataFactory.createMockConversation(
          userMessage: message,
        );
        
        when(mockChatApiService.createConversation(any))
            .thenAnswer((_) async => ConversationModel.fromJson(expectedConversation));
        
        when(mockEncryptionService.encryptSensitiveData(any))
            .thenReturn('encrypted_data');
        
        when(mockAnalyticsService.trackEvent(any, any))
            .thenAnswer((_) async {});
        
        // Act
        final result = await conversationService.createConversation(message);
        
        // Assert
        expect(result, isA<Success<Conversation>>());
        final conversation = (result as Success<Conversation>).data;
        expect(conversation.userMessage, equals(message));
        
        verify(mockChatApiService.createConversation(any)).called(1);
        verify(mockEncryptionService.encryptSensitiveData(any)).called(1);
        verify(mockAnalyticsService.trackEvent('conversation_created', any)).called(1);
      });
      
      test('should handle empty message error', () async {
        // Arrange
        const emptyMessage = '';
        
        // Act & Assert
        expect(
          () => conversationService.createConversation(emptyMessage),
          throwsA(isA<ArgumentError>()),
        );
        
        verifyNever(mockChatApiService.createConversation(any));
      });
      
      test('should handle API service failure', () async {
        // Arrange
        const message = 'Test message';
        final apiException = Exception('API service unavailable');
        
        when(mockChatApiService.createConversation(any))
            .thenThrow(apiException);
        
        // Act
        final result = await conversationService.createConversation(message);
        
        // Assert
        expect(result, isA<Failure<Conversation>>());
        final failure = result as Failure<Conversation>;
        expect(failure.error.message, contains('API service unavailable'));
        
        verify(mockChatApiService.createConversation(any)).called(1);
        verify(mockAnalyticsService.trackEvent('conversation_creation_failed', any)).called(1);
      });
      
      test('should handle network timeout gracefully', () async {
        // Arrange
        const message = 'Test message';
        
        when(mockChatApiService.createConversation(any))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(seconds: 31)); // Simulate timeout
          throw Exception('Network timeout');
        });
        
        // Act
        final result = await conversationService.createConversation(message);
        
        // Assert
        expect(result, isA<Failure<Conversation>>());
        final failure = result as Failure<Conversation>;
        expect(failure.error.code, equals('NETWORK_TIMEOUT'));
      });
    });
    
    group('getConversationHistory', () {
      test('should retrieve conversation history successfully', () async {
        // Arrange
        final mockConversations = [
          MockDataFactory.createMockConversation(id: 'conv-1'),
          MockDataFactory.createMockConversation(id: 'conv-2'),
        ];
        
        when(mockChatApiService.getConversations(any))
            .thenAnswer((_) async => mockConversations
                .map((c) => ConversationModel.fromJson(c))
                .toList());
        
        // Act
        final result = await conversationService.getConversationHistory(
          limit: 10,
          offset: 0,
        );
        
        // Assert
        expect(result, isA<Success<List<Conversation>>>());
        final conversations = (result as Success<List<Conversation>>).data;
        expect(conversations, hasLength(2));
        expect(conversations[0].id, equals('conv-1'));
        expect(conversations[1].id, equals('conv-2'));
        
        verify(mockChatApiService.getConversations(any)).called(1);
      });
      
      test('should handle empty conversation history', () async {
        // Arrange
        when(mockChatApiService.getConversations(any))
            .thenAnswer((_) async => []);
        
        // Act
        final result = await conversationService.getConversationHistory();
        
        // Assert
        expect(result, isA<Success<List<Conversation>>>());
        final conversations = (result as Success<List<Conversation>>).data;
        expect(conversations, isEmpty);
      });
    });
    
    group('processVoiceMessage', () {
      test('should process voice message with transcription', () async {
        // Arrange
        const audioFilePath = '/path/to/audio.wav';
        const transcription = 'What is the current price of Tesla?';
        final expectedConversation = MockDataFactory.createMockConversation(
          userMessage: transcription,
        );
        
        when(mockAudioService.transcribeAudio(audioFilePath))
            .thenAnswer((_) async => transcription);
        
        when(mockChatApiService.createConversation(any))
            .thenAnswer((_) async => ConversationModel.fromJson(expectedConversation));
        
        when(mockEncryptionService.encryptSensitiveData(any))
            .thenReturn('encrypted_data');
        
        // Act
        final result = await conversationService.processVoiceMessage(audioFilePath);
        
        // Assert
        expect(result, isA<Success<Conversation>>());
        final conversation = (result as Success<Conversation>).data;
        expect(conversation.userMessage, equals(transcription));
        
        verify(mockAudioService.transcribeAudio(audioFilePath)).called(1);
        verify(mockChatApiService.createConversation(any)).called(1);
        verify(mockAnalyticsService.trackEvent('voice_message_processed', any)).called(1);
      });
      
      test('should handle transcription failure', () async {
        // Arrange
        const audioFilePath = '/path/to/audio.wav';
        final transcriptionException = Exception('Transcription service unavailable');
        
        when(mockAudioService.transcribeAudio(audioFilePath))
            .thenThrow(transcriptionException);
        
        // Act
        final result = await conversationService.processVoiceMessage(audioFilePath);
        
        // Assert
        expect(result, isA<Failure<Conversation>>());
        final failure = result as Failure<Conversation>;
        expect(failure.error.code, equals('VOICE_TRANSCRIPTION_FAILED'));
        
        verify(mockAudioService.transcribeAudio(audioFilePath)).called(1);
        verifyNever(mockChatApiService.createConversation(any));
      });
    });
    
    group('performance tests', () {
      test('should handle multiple concurrent conversation requests', () async {
        fakeAsync((async) {
          // Arrange
          const messageCount = 10;
          final futures = <Future<Result<Conversation>>>[];
          
          when(mockChatApiService.createConversation(any))
              .thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 100));
            return ConversationModel.fromJson(
              MockDataFactory.createMockConversation(),
            );
          });
          
          when(mockEncryptionService.encryptSensitiveData(any))
              .thenReturn('encrypted_data');
          
          // Act
          for (int i = 0; i < messageCount; i++) {
            futures.add(conversationService.createConversation('Message $i'));
          }
          
          async.elapse(const Duration(milliseconds: 150));
          
          // Assert
          for (final future in futures) {
            expect(future, completes);
          }
          
          verify(mockChatApiService.createConversation(any)).called(messageCount);
        });
      });
      
      test('should complete conversation creation within performance threshold', () async {
        // Arrange
        const message = 'Performance test message';
        final stopwatch = Stopwatch()..start();
        
        when(mockChatApiService.createConversation(any))
            .thenAnswer((_) async => ConversationModel.fromJson(
              MockDataFactory.createMockConversation(),
            ));
        
        when(mockEncryptionService.encryptSensitiveData(any))
            .thenReturn('encrypted_data');
        
        // Act
        final result = await conversationService.createConversation(message);
        stopwatch.stop();
        
        // Assert
        expect(result, isA<Success<Conversation>>());
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 second threshold
      });
    });
  });
}
