/// Audio Processing Performance Tests
/// Copyright (c) 2025 Echo Inc.
/// 
/// Performance tests for audio recording, processing, and transcription workflows.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_echo/features/voice/services/audio_recording_service.dart';
import 'package:flutter_echo/features/voice/services/speech_to_text_service.dart';
import 'package:flutter_echo/features/voice/services/voice_activity_detection_service.dart';
import 'package:flutter_echo/features/voice/services/voice_streaming_service.dart';
import 'package:flutter_echo/features/voice/data/models/audio_recording_entity.dart';
import 'package:flutter_echo/core/utils/result.dart';

import 'audio_processing_performance_test.mocks.dart';

@GenerateMocks([File])
void main() {
  group('Audio Processing Performance Tests', () {
    late MockFile mockFile;
    late AudioRecordingEntity testRecording;

    setUp(() {
      mockFile = MockFile();
      testRecording = AudioRecordingEntity(
        id: 'perf-test-recording',
        filePath: '/test/audio.aac',
        duration: const Duration(minutes: 2),
        fileSize: 2048000, // 2MB
        sampleRate: 16000,
        bitRate: 128000,
        codec: 'aac',
        quality: AudioQuality.high,
        createdAt: DateTime.now(),
      );
    });

    group('Audio Recording Performance', () {
      test('should start recording within acceptable time limit', () async {
        // Arrange
        const maxStartupTime = Duration(milliseconds: 500);
        
        // Act
        final stopwatch = Stopwatch()..start();
        final result = await AudioRecordingService.startRecording();
        stopwatch.stop();

        // Assert
        expect(result.isSuccess, true);
        expect(stopwatch.elapsed, lessThan(maxStartupTime));
        print('Recording startup time: ${stopwatch.elapsedMilliseconds}ms');
      });

      test('should handle continuous recording without memory leaks', () async {
        // Arrange
        const recordingDuration = Duration(minutes: 5);
        const memoryCheckInterval = Duration(seconds: 30);
        final memoryUsages = <int>[];

        // Act
        await AudioRecordingService.startRecording();
        
        final timer = Stream.periodic(memoryCheckInterval).take(10);
        await for (final _ in timer) {
          // Simulate memory usage check
          final memoryUsage = _getCurrentMemoryUsage();
          memoryUsages.add(memoryUsage);
          
          // Check for memory leaks (increasing trend)
          if (memoryUsages.length > 3) {
            final recentUsages = memoryUsages.skip(memoryUsages.length - 3);
            final isIncreasing = recentUsages.toList()
              ..sort();
            expect(isIncreasing.last - isIncreasing.first, 
                   lessThan(50 * 1024 * 1024)); // Less than 50MB increase
          }
        }

        await AudioRecordingService.stopRecording();

        // Assert
        expect(memoryUsages.length, equals(10));
        print('Memory usage over time: $memoryUsages');
      });

      test('should maintain real-time amplitude streaming performance', () async {
        // Arrange
        const streamDuration = Duration(seconds: 10);
        const expectedUpdatesPerSecond = 10;
        int updateCount = 0;

        // Act
        await AudioRecordingService.startRecording();
        
        final stopwatch = Stopwatch()..start();
        final subscription = AudioRecordingService.getAmplitudeStream().listen((_) {
          updateCount++;
        });

        await Future.delayed(streamDuration);
        await subscription.cancel();
        stopwatch.stop();

        await AudioRecordingService.stopRecording();

        // Assert
        final expectedUpdates = expectedUpdatesPerSecond * streamDuration.inSeconds;
        expect(updateCount, greaterThan(expectedUpdates * 0.8)); // Allow 20% tolerance
        expect(updateCount, lessThan(expectedUpdates * 1.2));
        
        final actualRate = updateCount / stopwatch.elapsed.inSeconds;
        print('Amplitude update rate: ${actualRate.toStringAsFixed(1)} Hz');
      });

      test('should handle multiple concurrent recording sessions', () async {
        // Arrange
        const concurrentSessions = 5;
        final results = <Result<String>>[];

        // Act
        final stopwatch = Stopwatch()..start();
        final futures = List.generate(concurrentSessions, (index) async {
          await Future.delayed(Duration(milliseconds: index * 100));
          return AudioRecordingService.startRecording();
        });

        final sessionResults = await Future.wait(futures);
        results.addAll(sessionResults);
        stopwatch.stop();

        // Clean up
        for (int i = 0; i < concurrentSessions; i++) {
          await AudioRecordingService.stopRecording();
        }

        // Assert
        expect(stopwatch.elapsed, lessThan(const Duration(seconds: 5)));
        expect(results.where((r) => r.isSuccess).length, greaterThan(0));
        print('Concurrent sessions handled: ${results.where((r) => r.isSuccess).length}/$concurrentSessions');
      });
    });

    group('Speech-to-Text Performance', () {
      test('should transcribe short audio files quickly', () async {
        // Arrange
        final shortRecording = testRecording.copyWith(
          duration: const Duration(seconds: 10),
          fileSize: 160000, // ~160KB
        );
        const maxTranscriptionTime = Duration(seconds: 5);

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await SpeechToTextService.transcribeAudio(
          recording: shortRecording,
        );
        stopwatch.stop();

        // Assert
        expect(result.isSuccess, true);
        expect(stopwatch.elapsed, lessThan(maxTranscriptionTime));
        print('Short transcription time: ${stopwatch.elapsedMilliseconds}ms');
      });

      test('should handle large audio files efficiently', () async {
        // Arrange
        final largeRecording = testRecording.copyWith(
          duration: const Duration(minutes: 10),
          fileSize: 10240000, // ~10MB
        );
        const maxTranscriptionTime = Duration(seconds: 30);

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await SpeechToTextService.transcribeAudio(
          recording: largeRecording,
        );
        stopwatch.stop();

        // Assert
        expect(result.isSuccess, true);
        expect(stopwatch.elapsed, lessThan(maxTranscriptionTime));
        
        final transcriptionRate = largeRecording.duration.inSeconds / stopwatch.elapsed.inSeconds;
        expect(transcriptionRate, greaterThan(1.0)); // Faster than real-time
        print('Large file transcription rate: ${transcriptionRate.toStringAsFixed(2)}x real-time');
      });

      test('should batch process multiple transcriptions efficiently', () async {
        // Arrange
        const batchSize = 10;
        final recordings = List.generate(batchSize, (index) => 
          testRecording.copyWith(
            id: 'batch-recording-$index',
            duration: Duration(seconds: 5 + index),
          ),
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final futures = recordings.map((recording) => 
          SpeechToTextService.transcribeAudio(recording: recording),
        );
        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert
        final successCount = results.where((r) => r.isSuccess).length;
        expect(successCount, equals(batchSize));
        
        final avgTimePerTranscription = stopwatch.elapsed.inMilliseconds / batchSize;
        expect(avgTimePerTranscription, lessThan(3000)); // Less than 3s per transcription
        print('Batch transcription avg time: ${avgTimePerTranscription.toStringAsFixed(0)}ms per file');
      });

      test('should maintain accuracy under performance pressure', () async {
        // Arrange
        final testAudio = _generateTestAudioData();
        const expectedText = 'This is a test transcription for performance evaluation';
        
        // Act - Rapid successive transcriptions
        final results = <String>[];
        for (int i = 0; i < 5; i++) {
          final result = await SpeechToTextService.transcribeAudio(
            recording: testRecording,
          );
          if (result.isSuccess) {
            results.add(result.data!.text);
          }
        }

        // Assert
        expect(results.length, equals(5));
        
        // Check consistency across transcriptions
        final uniqueResults = results.toSet();
        expect(uniqueResults.length, lessThanOrEqualTo(2)); // Allow minor variations
        
        // Check accuracy (simplified - would use actual audio in real test)
        final accuracy = _calculateTranscriptionAccuracy(results.first, expectedText);
        expect(accuracy, greaterThan(0.9)); // 90% accuracy
        print('Transcription accuracy under load: ${(accuracy * 100).toStringAsFixed(1)}%');
      });
    });

    group('Voice Activity Detection Performance', () {
      test('should detect voice activity in real-time', () async {
        // Arrange
        const testDuration = Duration(seconds: 5);
        const expectedDetectionLatency = Duration(milliseconds: 100);
        final detectionTimes = <Duration>[];

        // Act
        final vadService = VoiceActivityDetectionService();
        await vadService.initialize();

        final stopwatch = Stopwatch();
        final subscription = vadService.voiceActivityStream.listen((isVoiceDetected) {
          if (isVoiceDetected && !stopwatch.isRunning) {
            stopwatch.start();
          } else if (!isVoiceDetected && stopwatch.isRunning) {
            detectionTimes.add(stopwatch.elapsed);
            stopwatch.reset();
          }
        });

        // Simulate voice activity
        await _simulateVoiceActivity(vadService, testDuration);
        await subscription.cancel();

        // Assert
        expect(detectionTimes.isNotEmpty, true);
        final avgDetectionTime = detectionTimes.fold<Duration>(
          Duration.zero,
          (sum, time) => sum + time,
        ) ~/ detectionTimes.length;
        
        expect(avgDetectionTime, lessThan(expectedDetectionLatency));
        print('Average VAD detection latency: ${avgDetectionTime.inMilliseconds}ms');
      });

      test('should handle continuous audio stream without performance degradation', () async {
        // Arrange
        const streamDuration = Duration(minutes: 2);
        const performanceCheckInterval = Duration(seconds: 10);
        final processingTimes = <int>[];

        // Act
        final vadService = VoiceActivityDetectionService();
        await vadService.initialize();

        final timer = Stream.periodic(performanceCheckInterval).take(12);
        await for (final _ in timer) {
          final stopwatch = Stopwatch()..start();
          
          // Process audio chunk
          final audioChunk = _generateAudioChunk();
          await vadService.processAudioChunk(audioChunk);
          
          stopwatch.stop();
          processingTimes.add(stopwatch.elapsedMicroseconds);
        }

        // Assert
        expect(processingTimes.length, equals(12));
        
        // Check for performance degradation over time
        final firstHalf = processingTimes.take(6).toList();
        final secondHalf = processingTimes.skip(6).toList();
        
        final firstAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
        final secondAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;
        
        // Second half should not be significantly slower
        expect(secondAvg, lessThan(firstAvg * 1.2)); // Allow 20% degradation
        print('VAD processing time stability: ${(secondAvg / firstAvg).toStringAsFixed(2)}x');
      });
    });

    group('Voice Streaming Performance', () {
      test('should stream audio with minimal latency', () async {
        // Arrange
        const maxLatency = Duration(milliseconds: 200);
        const streamDuration = Duration(seconds: 10);
        final latencies = <Duration>[];

        // Act
        final streamingService = VoiceStreamingService();
        await streamingService.initialize();

        final stopwatch = Stopwatch();
        final subscription = streamingService.audioChunkStream.listen((chunk) {
          if (stopwatch.isRunning) {
            latencies.add(stopwatch.elapsed);
            stopwatch.reset();
          }
          stopwatch.start();
        });

        await streamingService.startStreaming();
        await Future.delayed(streamDuration);
        await streamingService.stopStreaming();
        await subscription.cancel();

        // Assert
        expect(latencies.isNotEmpty, true);
        final avgLatency = latencies.fold<Duration>(
          Duration.zero,
          (sum, latency) => sum + latency,
        ) ~/ latencies.length;
        
        expect(avgLatency, lessThan(maxLatency));
        print('Average streaming latency: ${avgLatency.inMilliseconds}ms');
      });

      test('should maintain consistent chunk delivery rate', () async {
        // Arrange
        const expectedChunkRate = 10; // chunks per second
        const testDuration = Duration(seconds: 5);
        int chunkCount = 0;

        // Act
        final streamingService = VoiceStreamingService();
        await streamingService.initialize();

        final stopwatch = Stopwatch()..start();
        final subscription = streamingService.audioChunkStream.listen((_) {
          chunkCount++;
        });

        await streamingService.startStreaming();
        await Future.delayed(testDuration);
        await streamingService.stopStreaming();
        stopwatch.stop();
        await subscription.cancel();

        // Assert
        final actualRate = chunkCount / stopwatch.elapsed.inSeconds;
        expect(actualRate, closeTo(expectedChunkRate, expectedChunkRate * 0.1)); // 10% tolerance
        print('Chunk delivery rate: ${actualRate.toStringAsFixed(1)} chunks/sec');
      });

      test('should handle network interruptions gracefully', () async {
        // Arrange
        const interruptionDuration = Duration(seconds: 2);
        const recoveryTimeout = Duration(seconds: 5);
        bool connectionRestored = false;

        // Act
        final streamingService = VoiceStreamingService();
        await streamingService.initialize();
        await streamingService.startStreaming();

        // Simulate network interruption
        await streamingService.simulateNetworkInterruption();
        await Future.delayed(interruptionDuration);

        // Attempt recovery
        final stopwatch = Stopwatch()..start();
        await streamingService.reconnect();
        
        final subscription = streamingService.connectionStatusStream.listen((isConnected) {
          if (isConnected) {
            connectionRestored = true;
          }
        });

        await Future.delayed(recoveryTimeout);
        stopwatch.stop();
        await subscription.cancel();

        // Assert
        expect(connectionRestored, true);
        expect(stopwatch.elapsed, lessThan(recoveryTimeout));
        print('Network recovery time: ${stopwatch.elapsedMilliseconds}ms');
      });
    });

    group('Memory and Resource Management', () {
      test('should clean up audio buffers efficiently', () async {
        // Arrange
        const bufferCount = 100;
        final initialMemory = _getCurrentMemoryUsage();

        // Act
        final buffers = <Uint8List>[];
        for (int i = 0; i < bufferCount; i++) {
          buffers.add(_generateAudioBuffer(1024 * 1024)); // 1MB each
        }

        final peakMemory = _getCurrentMemoryUsage();
        
        // Clean up buffers
        buffers.clear();
        await Future.delayed(const Duration(seconds: 1)); // Allow GC
        
        final finalMemory = _getCurrentMemoryUsage();

        // Assert
        final memoryIncrease = peakMemory - initialMemory;
        final memoryRecovered = peakMemory - finalMemory;
        final recoveryRate = memoryRecovered / memoryIncrease;
        
        expect(recoveryRate, greaterThan(0.8)); // 80% memory recovery
        print('Memory recovery rate: ${(recoveryRate * 100).toStringAsFixed(1)}%');
      });

      test('should handle file I/O operations efficiently', () async {
        // Arrange
        const fileCount = 50;
        const fileSize = 1024 * 1024; // 1MB each
        final files = <String>[];

        // Act - Write files
        final writeStopwatch = Stopwatch()..start();
        for (int i = 0; i < fileCount; i++) {
          final filePath = '/tmp/test_audio_$i.aac';
          await _writeTestAudioFile(filePath, fileSize);
          files.add(filePath);
        }
        writeStopwatch.stop();

        // Act - Read files
        final readStopwatch = Stopwatch()..start();
        for (final filePath in files) {
          await _readTestAudioFile(filePath);
        }
        readStopwatch.stop();

        // Clean up
        for (final filePath in files) {
          await _deleteTestFile(filePath);
        }

        // Assert
        final writeRate = (fileCount * fileSize) / writeStopwatch.elapsed.inSeconds;
        final readRate = (fileCount * fileSize) / readStopwatch.elapsed.inSeconds;
        
        expect(writeRate, greaterThan(10 * 1024 * 1024)); // 10 MB/s
        expect(readRate, greaterThan(50 * 1024 * 1024)); // 50 MB/s
        
        print('File I/O rates - Write: ${(writeRate / 1024 / 1024).toStringAsFixed(1)} MB/s, '
              'Read: ${(readRate / 1024 / 1024).toStringAsFixed(1)} MB/s');
      });
    });
  });
}

// Helper functions for performance testing

int _getCurrentMemoryUsage() {
  // Simplified memory usage calculation
  // In real implementation, would use platform-specific memory APIs
  return DateTime.now().millisecondsSinceEpoch % 1000000;
}

Uint8List _generateTestAudioData() {
  return Uint8List.fromList(List.generate(1024, (i) => i % 256));
}

double _calculateTranscriptionAccuracy(String actual, String expected) {
  // Simplified accuracy calculation using character-level comparison
  final actualChars = actual.toLowerCase().replaceAll(RegExp(r'\s+'), '');
  final expectedChars = expected.toLowerCase().replaceAll(RegExp(r'\s+'), '');
  
  int matches = 0;
  final minLength = actualChars.length < expectedChars.length 
      ? actualChars.length 
      : expectedChars.length;
  
  for (int i = 0; i < minLength; i++) {
    if (actualChars[i] == expectedChars[i]) {
      matches++;
    }
  }
  
  return matches / expectedChars.length;
}

Future<void> _simulateVoiceActivity(VoiceActivityDetectionService service, Duration duration) async {
  // Simulate voice activity patterns
  final chunks = duration.inMilliseconds ~/ 100; // 100ms chunks
  for (int i = 0; i < chunks; i++) {
    final hasVoice = (i % 10) < 7; // 70% voice activity
    final audioChunk = _generateAudioChunk(hasVoice: hasVoice);
    await service.processAudioChunk(audioChunk);
    await Future.delayed(const Duration(milliseconds: 100));
  }
}

Uint8List _generateAudioChunk({bool hasVoice = false}) {
  final random = DateTime.now().millisecondsSinceEpoch;
  final amplitude = hasVoice ? 0.7 : 0.1;
  return Uint8List.fromList(
    List.generate(1024, (i) => ((amplitude * 127 * (random + i)) % 256).toInt()),
  );
}

Uint8List _generateAudioBuffer(int size) {
  return Uint8List.fromList(List.generate(size, (i) => i % 256));
}

Future<void> _writeTestAudioFile(String filePath, int size) async {
  // Simulate file write operation
  await Future.delayed(Duration(microseconds: size ~/ 1000));
}

Future<void> _readTestAudioFile(String filePath) async {
  // Simulate file read operation
  await Future.delayed(const Duration(microseconds: 100));
}

Future<void> _deleteTestFile(String filePath) async {
  // Simulate file deletion
  await Future.delayed(const Duration(microseconds: 10));
}
