/// Echo Application Root
/// Copyright (c) 2025 Echo Inc.
/// 
/// Main application widget with routing, theming, and global providers.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'core/constants/app_constants.dart';
import 'core/constants/colors.dart';
import 'core/providers/app_providers.dart';
import 'core/routing/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/logger.dart';
import 'features/auth/presentation/screens/access_control_screen.dart';
import 'features/chat/presentation/screens/chat_screen.dart';
import 'features/history/presentation/screens/conversation_history_screen.dart';
import 'shared/widgets/error_boundary.dart';
import 'shared/widgets/loading_overlay.dart';

/// Main Echo application widget
class EchoApp extends ConsumerWidget {
  const EchoApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch app state providers
    final appState = ref.watch(appStateProvider);
    final themeMode = ref.watch(themeModeProvider);
    final router = ref.watch(routerProvider);

    return ErrorBoundary(
      child: MaterialApp.router(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        
        // Theme configuration
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        
        // Routing
        routerConfig: router,
        
        // Localization
        supportedLocales: const [
          Locale('en', 'US'),
        ],
        
        // Builder for global overlays
        builder: (context, child) {
          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: _getSystemUiOverlayStyle(context, themeMode),
            child: Stack(
              children: [
                child ?? const SizedBox.shrink(),
                
                // Global loading overlay
                if (appState.isLoading)
                  const LoadingOverlay(),
                
                // Global error overlay
                if (appState.hasError)
                  _buildErrorOverlay(context, ref, appState.error),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Get system UI overlay style based on theme
  SystemUiOverlayStyle _getSystemUiOverlayStyle(
    BuildContext context,
    ThemeMode themeMode,
  ) {
    final brightness = themeMode == ThemeMode.dark
        ? Brightness.dark
        : themeMode == ThemeMode.light
            ? Brightness.light
            : MediaQuery.of(context).platformBrightness;

    return brightness == Brightness.dark
        ? SystemUiOverlayStyle.light.copyWith(
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: AppColors.backgroundDark,
          )
        : SystemUiOverlayStyle.dark.copyWith(
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: AppColors.backgroundLight,
          );
  }

  /// Build global error overlay
  Widget _buildErrorOverlay(
    BuildContext context,
    WidgetRef ref,
    String? error,
  ) {
    return Material(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Something went wrong',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                error ?? 'An unexpected error occurred',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton(
                    onPressed: () {
                      ref.read(appStateProvider.notifier).clearError();
                    },
                    child: const Text('Dismiss'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(appStateProvider.notifier).clearError();
                      // Restart app or navigate to safe state
                      context.go('/');
                    },
                    child: const Text('Restart'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// App state model
class AppState {
  final bool isLoading;
  final bool hasError;
  final String? error;
  final bool isAuthenticated;
  final bool hasAccessPermission;

  const AppState({
    this.isLoading = false,
    this.hasError = false,
    this.error,
    this.isAuthenticated = false,
    this.hasAccessPermission = false,
  });

  AppState copyWith({
    bool? isLoading,
    bool? hasError,
    String? error,
    bool? isAuthenticated,
    bool? hasAccessPermission,
  }) {
    return AppState(
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      error: error ?? this.error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      hasAccessPermission: hasAccessPermission ?? this.hasAccessPermission,
    );
  }
}

/// App state notifier
class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(const AppState());

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String error) {
    Logger.error('App error: $error');
    state = state.copyWith(hasError: true, error: error, isLoading: false);
  }

  void clearError() {
    state = state.copyWith(hasError: false, error: null);
  }

  void setAuthenticated(bool authenticated) {
    state = state.copyWith(isAuthenticated: authenticated);
  }

  void setAccessPermission(bool hasAccess) {
    state = state.copyWith(hasAccessPermission: hasAccess);
  }

  void reset() {
    state = const AppState();
  }
}

/// Theme mode notifier
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system);

  void setThemeMode(ThemeMode mode) {
    state = mode;
  }

  void toggleTheme() {
    state = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
  }
}

/// App providers
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>(
  (ref) => AppStateNotifier(),
);

final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

/// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  final appState = ref.watch(appStateProvider);
  
  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final location = state.location;
      
      // Check access permission first
      if (!appState.hasAccessPermission && location != '/access') {
        return '/access';
      }
      
      // Check authentication for protected routes
      if (!appState.isAuthenticated && 
          location != '/access' && 
          location != '/auth') {
        return '/auth';
      }
      
      return null;
    },
    routes: [
      GoRoute(
        path: '/access',
        name: 'access',
        builder: (context, state) => const AccessControlScreen(),
      ),
      GoRoute(
        path: '/auth',
        name: 'auth',
        builder: (context, state) => const AccessControlScreen(), // Will be AuthScreen
      ),
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const ChatScreen(),
      ),
      GoRoute(
        path: '/chat',
        name: 'chat',
        builder: (context, state) => const ChatScreen(),
      ),
      GoRoute(
        path: '/history',
        name: 'history',
        builder: (context, state) => const ConversationHistoryScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});
