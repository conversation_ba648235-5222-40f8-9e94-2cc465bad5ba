/// Echo Voice Investment Platform
/// Copyright (c) 2025 Echo Inc.
/// 
/// Main entry point for the Echo Flutter mobile application.
/// Initializes core services, providers, and app configuration.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'app.dart';
import 'core/services/analytics_service.dart';
import 'core/services/error_service.dart';
import 'core/services/logging_service.dart';
import 'core/storage/local_storage.dart';
import 'core/storage/hive_setup.dart';
import 'core/utils/app_initializer.dart';
import 'core/security/encryption_service.dart';
import 'core/security/biometric_auth_service.dart';
import 'core/security/device_security_service.dart';
import 'core/security/security_monitoring_service.dart';
import 'core/accessibility/accessibility_service.dart';
import 'core/haptics/haptic_feedback_service.dart';
import 'core/analytics/analytics_service.dart' as new_analytics;
import 'core/feature_flags/feature_flags_service.dart';
import 'core/performance/performance_monitoring_service.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize error handling
  await _initializeErrorHandling();
  
  // Initialize core services
  await _initializeCoreServices();
  
  // Run the app with error boundary
  runZonedGuarded(
    () => runApp(
      ProviderScope(
        observers: [
          if (kDebugMode) RiverpodLogger(),
        ],
        child: const EchoApp(),
      ),
    ),
    (error, stackTrace) {
      ErrorService.handleError(
        error: error,
        stackTrace: stackTrace,
        context: 'main_zone',
      );
    },
  );
}

/// Initialize global error handling
Future<void> _initializeErrorHandling() async {
  // Handle Flutter framework errors
  FlutterError.onError = (FlutterErrorDetails details) {
    ErrorService.handleFlutterError(details);
  };
  
  // Handle platform errors
  PlatformDispatcher.instance.onError = (error, stack) {
    ErrorService.handleError(
      error: error,
      stackTrace: stack,
      context: 'platform_dispatcher',
    );
    return true;
  };
}

/// Initialize core application services
Future<void> _initializeCoreServices() async {
  try {
    // Initialize logging first
    await LoggingService.initialize();

    // Initialize Hive database with encryption
    await HiveSetup.initialize();

    // Initialize local storage
    await LocalStorage.initialize();

    // Initialize security services
    await EncryptionService.initialize();
    await BiometricAuthService.initialize();
    await DeviceSecurityService.initialize();
    await SecurityMonitoringService.initialize();

    // Start security monitoring
    await SecurityMonitoringService.startMonitoring();

    // Initialize accessibility services
    await AccessibilityService.instance.initialize();

    // Initialize haptic feedback
    await HapticFeedbackService.instance.initialize();

    // Initialize feature flags
    await FeatureFlagsService.instance.initialize();

    // Initialize performance monitoring
    await PerformanceMonitoringService.instance.initialize();

    // Initialize new analytics service
    await new_analytics.AnalyticsService.instance.initialize(
      apiKey: 'your-analytics-api-key', // Would be from environment config
    );

    // Initialize app services
    await AppInitializer.initialize();

    // Initialize legacy analytics
    await AnalyticsService.initialize();

    LoggingService.info('Core services initialized successfully');
  } catch (e, stackTrace) {
    LoggingService.error('Failed to initialize core services', e, stackTrace);
    rethrow;
  }
}

/// Riverpod logger for debugging state changes
class RiverpodLogger extends ProviderObserver {
  @override
  void didUpdateProvider(
    ProviderBase<Object?> provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    if (kDebugMode) {
      LoggingService.debug(
        'Provider updated: ${provider.name ?? provider.runtimeType} '
        'from $previousValue to $newValue',
      );
    }
  }
  
  @override
  void didAddProvider(
    ProviderBase<Object?> provider,
    Object? value,
    ProviderContainer container,
  ) {
    if (kDebugMode) {
      LoggingService.debug(
        'Provider added: ${provider.name ?? provider.runtimeType} '
        'with value $value',
      );
    }
  }
  
  @override
  void didDisposeProvider(
    ProviderBase<Object?> provider,
    ProviderContainer container,
  ) {
    if (kDebugMode) {
      LoggingService.debug(
        'Provider disposed: ${provider.name ?? provider.runtimeType}',
      );
    }
  }
}
