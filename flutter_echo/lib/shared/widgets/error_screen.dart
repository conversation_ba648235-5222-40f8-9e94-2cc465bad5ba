/// Error Screen Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Full-screen error display with retry functionality.

import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/colors.dart';

/// Full-screen error widget
class ErrorScreen extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  final String? customTitle;
  final String? customMessage;
  final Widget? customAction;

  const ErrorScreen({
    super.key,
    required this.error,
    this.onRetry,
    this.customTitle,
    this.customMessage,
    this.customAction,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Error icon
                Icon(
                  Icons.error_outline,
                  size: 80,
                  color: AppColors.error,
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Error title
                Text(
                  customTitle ?? 'Something went wrong',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.smallPadding),
                
                // Error message
                Text(
                  customMessage ?? 'An unexpected error occurred. Please try again.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Action buttons
                if (customAction != null)
                  customAction!
                else if (onRetry != null) ...[
                  ElevatedButton(
                    onPressed: onRetry,
                    child: const Text('Try Again'),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  TextButton(
                    onPressed: () {
                      if (Navigator.of(context).canPop()) {
                        Navigator.of(context).pop();
                      }
                    },
                    child: const Text('Go Back'),
                  ),
                ],
                
                // Debug info (development only)
                if (AppConstants.isDevelopment) ...[
                  const SizedBox(height: AppConstants.largePadding),
                  ExpansionTile(
                    title: const Text('Error Details'),
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        decoration: BoxDecoration(
                          color: AppColors.neutral100,
                          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                        ),
                        child: Text(
                          error,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
