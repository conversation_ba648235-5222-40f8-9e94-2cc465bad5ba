/// Loading Screen Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Full-screen loading display with animated indicators.

import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/colors.dart';
import 'loading_overlay.dart';

/// Full-screen loading widget
class LoadingScreen extends StatelessWidget {
  final String? message;
  final Widget? customIndicator;

  const LoadingScreen({
    super.key,
    this.message,
    this.customIndicator,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Loading indicator
              customIndicator ?? const EchoLoadingIndicator(size: 80),
              
              const SizedBox(height: AppConstants.largePadding),
              
              // Loading message
              if (message != null)
                Text(
                  message!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
