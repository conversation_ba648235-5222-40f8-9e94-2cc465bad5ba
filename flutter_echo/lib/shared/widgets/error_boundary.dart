/// Error Boundary Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget that catches and handles errors in the widget tree.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/colors.dart';
import '../../core/utils/logger.dart';

/// Error boundary widget that catches errors in child widgets
class ErrorBoundary extends ConsumerStatefulWidget {
  final Widget child;
  final Widget? fallback;
  final void Function(Object error, StackTrace stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.fallback,
    this.onError,
  });

  @override
  ConsumerState<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends ConsumerState<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.fallback ?? _buildDefaultErrorWidget(context);
    }

    return ErrorWidget.builder = (FlutterErrorDetails details) {
      // Log the error
      Logger.error('Widget error caught by ErrorBoundary', details.exception, details.stack);
      
      // Call custom error handler if provided
      widget.onError?.call(details.exception, details.stack ?? StackTrace.current);
      
      // Set error state
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _error = details.exception;
            _stackTrace = details.stack;
          });
        }
      });
      
      return _buildDefaultErrorWidget(context);
    };

    return widget.child;
  }

  /// Build default error widget
  Widget _buildDefaultErrorWidget(BuildContext context) {
    return Material(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                'Something went wrong',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: AppColors.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'An unexpected error occurred. Please try again.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _error = null;
                        _stackTrace = null;
                      });
                    },
                    child: const Text('Retry'),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  if (AppConstants.isDevelopment) ...[
                    TextButton(
                      onPressed: () => _showErrorDetails(context),
                      child: const Text('Details'),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show error details dialog (development only)
  void _showErrorDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Error:',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _error.toString(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Stack Trace:',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _stackTrace.toString(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Global error handler for uncaught errors
class GlobalErrorHandler {
  static void initialize() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      Logger.error('Flutter error', details.exception, details.stack);
      
      // In production, you might want to send this to a crash reporting service
      if (AppConstants.enableCrashReporting && AppConstants.isProduction) {
        // Send to crash reporting service
        _sendToCrashReporting(details);
      }
    };

    // Handle errors outside of Flutter framework
    PlatformDispatcher.instance.onError = (error, stack) {
      Logger.error('Platform error', error, stack);
      
      if (AppConstants.enableCrashReporting && AppConstants.isProduction) {
        // Send to crash reporting service
        _sendToCrashReporting(FlutterErrorDetails(
          exception: error,
          stack: stack,
          library: 'platform',
        ));
      }
      
      return true;
    };
  }

  static void _sendToCrashReporting(FlutterErrorDetails details) {
    // Implementation would depend on your crash reporting service
    // e.g., Firebase Crashlytics, Sentry, etc.
    Logger.info('Sending error to crash reporting service');
  }
}

/// Error widget for specific error types
class TypedErrorWidget extends StatelessWidget {
  final Object error;
  final StackTrace? stackTrace;
  final VoidCallback? onRetry;
  final String? customMessage;

  const TypedErrorWidget({
    super.key,
    required this.error,
    this.stackTrace,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    final errorInfo = _getErrorInfo(error);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            errorInfo.icon,
            size: 48,
            color: errorInfo.color,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            errorInfo.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: errorInfo.color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            customMessage ?? errorInfo.message,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }

  _ErrorInfo _getErrorInfo(Object error) {
    if (error.toString().contains('network') || 
        error.toString().contains('connection')) {
      return _ErrorInfo(
        icon: Icons.wifi_off,
        color: AppColors.warning,
        title: 'Connection Error',
        message: AppConstants.networkErrorMessage,
      );
    }
    
    if (error.toString().contains('auth') || 
        error.toString().contains('unauthorized')) {
      return _ErrorInfo(
        icon: Icons.lock_outline,
        color: AppColors.error,
        title: 'Authentication Error',
        message: AppConstants.authErrorMessage,
      );
    }
    
    if (error.toString().contains('permission')) {
      return _ErrorInfo(
        icon: Icons.security,
        color: AppColors.warning,
        title: 'Permission Error',
        message: 'Permission denied. Please check app permissions.',
      );
    }
    
    // Default error
    return _ErrorInfo(
      icon: Icons.error_outline,
      color: AppColors.error,
      title: 'Error',
      message: AppConstants.genericErrorMessage,
    );
  }
}

class _ErrorInfo {
  final IconData icon;
  final Color color;
  final String title;
  final String message;

  const _ErrorInfo({
    required this.icon,
    required this.color,
    required this.title,
    required this.message,
  });
}
