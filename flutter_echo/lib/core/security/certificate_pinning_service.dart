/// Echo Security Framework - Certificate Pinning Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive certificate pinning implementation for API communications
/// to prevent man-in-the-middle attacks and ensure secure connections.

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';

import '../utils/logger.dart';

/// Service for handling certificate pinning and validation
class CertificatePinningService {
  static const String _productionApiHost = 'api.echo.com';
  static const String _stagingApiHost = 'api-staging.echo.com';
  
  // Production certificate pins (SHA-256 hashes)
  static const List<String> _productionPins = [
    // Primary certificate pin
    'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Replace with actual pin
    // Backup certificate pin
    'BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=', // Replace with actual pin
  ];
  
  // Staging certificate pins (SHA-256 hashes)
  static const List<String> _stagingPins = [
    // Staging certificate pin
    'CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=', // Replace with actual pin
  ];
  
  static bool _isInitialized = false;
  static late Dio _dioInstance;
  
  /// Initialize the certificate pinning service
  static Future<void> initialize() async {
    try {
      _dioInstance = Dio();
      _configureCertificatePinning();
      _isInitialized = true;
      Logger.info('CertificatePinningService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize CertificatePinningService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Configure certificate pinning for Dio HTTP client
  static void _configureCertificatePinning() {
    (_dioInstance.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      
      // Configure certificate validation
      client.badCertificateCallback = (cert, host, port) {
        return _validateCertificate(cert, host, port);
      };
      
      // Set connection timeout
      client.connectionTimeout = const Duration(seconds: 30);
      
      // Set idle timeout
      client.idleTimeout = const Duration(seconds: 30);
      
      return client;
    };
  }
  
  /// Validate certificate against pinned certificates
  static bool _validateCertificate(X509Certificate cert, String host, int port) {
    try {
      Logger.debug('Validating certificate for $host:$port');
      
      // Get the appropriate pins for the host
      final pins = _getPinsForHost(host);
      if (pins.isEmpty) {
        Logger.warning('No pins configured for host: $host');
        return kDebugMode; // Allow in debug mode, reject in production
      }
      
      // Extract certificate data
      final certBytes = cert.der;
      final certHash = _calculateSHA256Pin(certBytes);
      
      Logger.debug('Certificate SHA-256 pin: $certHash');
      
      // Check if certificate matches any pinned certificates
      final isValid = pins.contains(certHash);
      
      if (isValid) {
        Logger.info('Certificate validation successful for $host');
      } else {
        Logger.error('Certificate validation failed for $host. Pin: $certHash');
        _logSecurityEvent('certificate_pinning_failure', {
          'host': host,
          'port': port,
          'certificate_pin': certHash,
          'expected_pins': pins,
        });
      }
      
      return isValid;
    } catch (e, stackTrace) {
      Logger.error('Error during certificate validation', e, stackTrace);
      _logSecurityEvent('certificate_validation_error', {
        'host': host,
        'port': port,
        'error': e.toString(),
      });
      return false;
    }
  }
  
  /// Get pinned certificates for a specific host
  static List<String> _getPinsForHost(String host) {
    switch (host) {
      case _productionApiHost:
        return _productionPins;
      case _stagingApiHost:
        return _stagingPins;
      default:
        // Check for subdomain matches
        if (host.endsWith('.echo.com')) {
          return _productionPins;
        }
        return [];
    }
  }
  
  /// Calculate SHA-256 pin for certificate
  static String _calculateSHA256Pin(Uint8List certBytes) {
    final digest = sha256.convert(certBytes);
    return base64Encode(digest.bytes);
  }
  
  /// Get configured Dio instance with certificate pinning
  static Dio getDioInstance() {
    if (!_isInitialized) {
      throw StateError('CertificatePinningService not initialized');
    }
    return _dioInstance;
  }
  
  /// Create a new Dio instance with certificate pinning
  static Dio createSecureDioInstance({
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    Map<String, dynamic>? headers,
  }) {
    final dio = Dio();
    
    // Configure timeouts
    dio.options.connectTimeout = connectTimeout ?? const Duration(seconds: 30);
    dio.options.receiveTimeout = receiveTimeout ?? const Duration(seconds: 30);
    dio.options.sendTimeout = sendTimeout ?? const Duration(seconds: 30);
    
    // Set default headers
    if (headers != null) {
      dio.options.headers.addAll(headers);
    }
    
    // Configure certificate pinning
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      client.badCertificateCallback = _validateCertificate;
      client.connectionTimeout = connectTimeout ?? const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);
      return client;
    };
    
    // Add security interceptors
    dio.interceptors.add(_createSecurityInterceptor());
    
    return dio;
  }
  
  /// Create security interceptor for additional validation
  static Interceptor _createSecurityInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add security headers
        options.headers['X-Requested-With'] = 'XMLHttpRequest';
        options.headers['X-Echo-Client'] = 'Flutter';
        options.headers['X-Echo-Version'] = '1.0.0';
        
        // Validate request URL
        if (!_isSecureUrl(options.uri.toString())) {
          Logger.error('Insecure URL detected: ${options.uri}');
          handler.reject(
            DioException(
              requestOptions: options,
              error: 'Insecure URL not allowed',
              type: DioExceptionType.cancel,
            ),
          );
          return;
        }
        
        Logger.debug('Secure request to: ${options.uri}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        // Validate response headers
        _validateResponseHeaders(response);
        handler.next(response);
      },
      onError: (error, handler) {
        // Log security-related errors
        if (_isSecurityError(error)) {
          _logSecurityEvent('network_security_error', {
            'url': error.requestOptions.uri.toString(),
            'error_type': error.type.toString(),
            'error_message': error.message,
          });
        }
        handler.next(error);
      },
    );
  }
  
  /// Validate if URL is secure
  static bool _isSecureUrl(String url) {
    final uri = Uri.parse(url);
    
    // Must use HTTPS
    if (uri.scheme != 'https') {
      return false;
    }
    
    // Must be from allowed hosts
    final allowedHosts = [
      _productionApiHost,
      _stagingApiHost,
      'openai.com',
      'api.openai.com',
      'api.anthropic.com',
    ];
    
    return allowedHosts.any((host) => 
        uri.host == host || uri.host.endsWith('.$host'));
  }
  
  /// Validate response headers for security
  static void _validateResponseHeaders(Response response) {
    final headers = response.headers;
    
    // Check for security headers
    final securityHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
    ];
    
    for (final header in securityHeaders) {
      if (!headers.map.containsKey(header)) {
        Logger.warning('Missing security header: $header');
      }
    }
    
    // Validate content type
    final contentType = headers.value('content-type');
    if (contentType != null && !_isAllowedContentType(contentType)) {
      Logger.warning('Unexpected content type: $contentType');
    }
  }
  
  /// Check if content type is allowed
  static bool _isAllowedContentType(String contentType) {
    final allowedTypes = [
      'application/json',
      'text/plain',
      'audio/wav',
      'audio/mpeg',
      'audio/mp4',
    ];
    
    return allowedTypes.any((type) => contentType.startsWith(type));
  }
  
  /// Check if error is security-related
  static bool _isSecurityError(DioException error) {
    return error.type == DioExceptionType.connectionError ||
           error.type == DioExceptionType.badCertificate ||
           error.message?.contains('certificate') == true ||
           error.message?.contains('ssl') == true ||
           error.message?.contains('tls') == true;
  }
  
  /// Log security events for monitoring
  static void _logSecurityEvent(String eventType, Map<String, dynamic> details) {
    final event = {
      'event_type': eventType,
      'timestamp': DateTime.now().toIso8601String(),
      'details': details,
    };
    
    Logger.security('Security event: $eventType', event);
    
    // In production, send to security monitoring system
    if (kReleaseMode) {
      _sendSecurityAlert(event);
    }
  }
  
  /// Send security alert to monitoring system
  static void _sendSecurityAlert(Map<String, dynamic> event) {
    // TODO: Implement security alert system integration
    // This could send alerts to:
    // - Security Information and Event Management (SIEM) system
    // - Security Operations Center (SOC)
    // - Incident response team
    Logger.info('Security alert would be sent: ${event['event_type']}');
  }
  
  /// Validate certificate chain
  static bool validateCertificateChain(List<X509Certificate> chain, String host) {
    try {
      if (chain.isEmpty) {
        Logger.error('Empty certificate chain for $host');
        return false;
      }
      
      // Validate leaf certificate
      final leafCert = chain.first;
      final leafPin = _calculateSHA256Pin(leafCert.der);
      final pins = _getPinsForHost(host);
      
      if (pins.contains(leafPin)) {
        Logger.info('Leaf certificate validation successful for $host');
        return true;
      }
      
      // Check intermediate certificates
      for (int i = 1; i < chain.length; i++) {
        final intermediateCert = chain[i];
        final intermediatePin = _calculateSHA256Pin(intermediateCert.der);
        
        if (pins.contains(intermediatePin)) {
          Logger.info('Intermediate certificate validation successful for $host');
          return true;
        }
      }
      
      Logger.error('No matching pins found in certificate chain for $host');
      return false;
    } catch (e, stackTrace) {
      Logger.error('Error validating certificate chain', e, stackTrace);
      return false;
    }
  }
  
  /// Dispose of the certificate pinning service
  static void dispose() {
    if (_isInitialized) {
      _dioInstance.close();
      _isInitialized = false;
      Logger.info('CertificatePinningService disposed');
    }
  }
}
