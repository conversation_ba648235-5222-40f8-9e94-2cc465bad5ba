/// Echo Security Framework - Device Security Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Device security service for detecting compromised devices (rooted/jailbroken)
/// and implementing security measures to protect sensitive data.

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../utils/logger.dart';

/// Service for detecting device security threats
class DeviceSecurityService {
  static const MethodChannel _channel = MethodChannel('echo/device_security');
  static bool _isInitialized = false;
  static DeviceSecurityStatus? _cachedStatus;
  
  /// Initialize the device security service
  static Future<void> initialize() async {
    try {
      _isInitialized = true;
      Logger.info('DeviceSecurityService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize DeviceSecurityService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Perform comprehensive device security check
  static Future<DeviceSecurityStatus> performSecurityCheck() async {
    if (!_isInitialized) {
      throw StateError('DeviceSecurityService not initialized');
    }
    
    try {
      Logger.info('Performing device security check');
      
      final status = DeviceSecurityStatus();
      
      // Check for root/jailbreak
      status.isRooted = await _checkRootStatus();
      status.isJailbroken = await _checkJailbreakStatus();
      
      // Check for debugging
      status.isDebuggingEnabled = await _checkDebuggingStatus();
      
      // Check for emulator
      status.isEmulator = await _checkEmulatorStatus();
      
      // Check for malicious apps
      status.hasMaliciousApps = await _checkMaliciousApps();
      
      // Check device integrity
      status.deviceIntegrityScore = await _calculateDeviceIntegrityScore(status);
      
      // Determine overall security level
      status.securityLevel = _determineSecurityLevel(status);
      
      // Cache the result
      _cachedStatus = status;
      
      Logger.info('Device security check completed. Level: ${status.securityLevel}');
      return status;
    } catch (e, stackTrace) {
      Logger.error('Failed to perform security check', e, stackTrace);
      rethrow;
    }
  }
  
  /// Check if device is rooted (Android)
  static Future<bool> _checkRootStatus() async {
    if (!Platform.isAndroid) {
      return false;
    }
    
    try {
      // Check for common root indicators
      final rootIndicators = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
        '/su/bin/su',
      ];
      
      for (final path in rootIndicators) {
        if (await File(path).exists()) {
          Logger.warning('Root indicator found: $path');
          return true;
        }
      }
      
      // Check for root management apps
      final rootApps = [
        'com.noshufou.android.su',
        'com.noshufou.android.su.elite',
        'eu.chainfire.supersu',
        'com.koushikdutta.superuser',
        'com.thirdparty.superuser',
        'com.yellowes.su',
        'com.topjohnwu.magisk',
      ];
      
      for (final app in rootApps) {
        if (await _isAppInstalled(app)) {
          Logger.warning('Root management app found: $app');
          return true;
        }
      }
      
      // Check build tags
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      
      if (androidInfo.tags.contains('test-keys')) {
        Logger.warning('Device built with test keys');
        return true;
      }
      
      return false;
    } catch (e) {
      Logger.error('Error checking root status', e);
      return false;
    }
  }
  
  /// Check if device is jailbroken (iOS)
  static Future<bool> _checkJailbreakStatus() async {
    if (!Platform.isIOS) {
      return false;
    }
    
    try {
      // Check for jailbreak indicators
      final jailbreakPaths = [
        '/Applications/Cydia.app',
        '/Library/MobileSubstrate/MobileSubstrate.dylib',
        '/bin/bash',
        '/usr/sbin/sshd',
        '/etc/apt',
        '/private/var/lib/apt/',
        '/private/var/lib/cydia',
        '/private/var/mobile/Library/SBSettings/Themes',
        '/Library/MobileSubstrate/DynamicLibraries/Veency.plist',
        '/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist',
        '/System/Library/LaunchDaemons/com.ikey.bbot.plist',
        '/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist',
      ];
      
      for (final path in jailbreakPaths) {
        if (await File(path).exists()) {
          Logger.warning('Jailbreak indicator found: $path');
          return true;
        }
      }
      
      // Check for jailbreak apps
      final jailbreakApps = [
        'cydia://package/com.example.package',
        'sileo://package/com.example.package',
        'zbra://package/com.example.package',
      ];
      
      for (final app in jailbreakApps) {
        if (await _canOpenUrl(app)) {
          Logger.warning('Jailbreak app scheme accessible: $app');
          return true;
        }
      }
      
      return false;
    } catch (e) {
      Logger.error('Error checking jailbreak status', e);
      return false;
    }
  }
  
  /// Check if debugging is enabled
  static Future<bool> _checkDebuggingStatus() async {
    try {
      if (kDebugMode) {
        return true;
      }
      
      if (Platform.isAndroid) {
        // Check ADB debugging
        return await _channel.invokeMethod('isAdbEnabled') ?? false;
      }
      
      return false;
    } catch (e) {
      Logger.error('Error checking debugging status', e);
      return false;
    }
  }
  
  /// Check if running on emulator
  static Future<bool> _checkEmulatorStatus() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        
        // Check for emulator indicators
        final emulatorIndicators = [
          androidInfo.brand.toLowerCase().contains('generic'),
          androidInfo.device.toLowerCase().contains('generic'),
          androidInfo.product.toLowerCase().contains('sdk'),
          androidInfo.hardware.toLowerCase().contains('goldfish'),
          androidInfo.hardware.toLowerCase().contains('ranchu'),
          androidInfo.model.toLowerCase().contains('emulator'),
          androidInfo.manufacturer.toLowerCase().contains('genymotion'),
        ];
        
        return emulatorIndicators.any((indicator) => indicator);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        
        // Check for iOS simulator
        return !iosInfo.isPhysicalDevice;
      }
      
      return false;
    } catch (e) {
      Logger.error('Error checking emulator status', e);
      return false;
    }
  }
  
  /// Check for malicious apps
  static Future<bool> _checkMaliciousApps() async {
    try {
      // List of known malicious app packages
      final maliciousApps = [
        'com.koushikdutta.superuser',
        'com.thirdparty.superuser',
        'com.yellowes.su',
        'com.zachspong.temprootremovejb',
        'com.ramdroid.appquarantine',
        'com.devadvance.rootcloak',
        'com.devadvance.rootcloakplus',
        'de.robv.android.xposed.installer',
        'com.saurik.substrate',
      ];
      
      for (final app in maliciousApps) {
        if (await _isAppInstalled(app)) {
          Logger.warning('Malicious app detected: $app');
          return true;
        }
      }
      
      return false;
    } catch (e) {
      Logger.error('Error checking malicious apps', e);
      return false;
    }
  }
  
  /// Calculate device integrity score (0-100)
  static Future<int> _calculateDeviceIntegrityScore(DeviceSecurityStatus status) async {
    int score = 100;
    
    if (status.isRooted) score -= 40;
    if (status.isJailbroken) score -= 40;
    if (status.isDebuggingEnabled) score -= 20;
    if (status.isEmulator) score -= 30;
    if (status.hasMaliciousApps) score -= 25;
    
    return score.clamp(0, 100);
  }
  
  /// Determine overall security level
  static SecurityLevel _determineSecurityLevel(DeviceSecurityStatus status) {
    if (status.deviceIntegrityScore >= 90) {
      return SecurityLevel.high;
    } else if (status.deviceIntegrityScore >= 70) {
      return SecurityLevel.medium;
    } else if (status.deviceIntegrityScore >= 50) {
      return SecurityLevel.low;
    } else {
      return SecurityLevel.critical;
    }
  }
  
  /// Check if app is installed
  static Future<bool> _isAppInstalled(String packageName) async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('isAppInstalled', packageName) ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Check if URL scheme can be opened (iOS)
  static Future<bool> _canOpenUrl(String url) async {
    try {
      if (Platform.isIOS) {
        return await _channel.invokeMethod('canOpenURL', url) ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Get cached security status
  static DeviceSecurityStatus? getCachedSecurityStatus() {
    return _cachedStatus;
  }
  
  /// Check if device is secure enough for the app
  static bool isDeviceSecure() {
    final status = _cachedStatus;
    if (status == null) {
      return false;
    }
    
    // Define minimum security requirements
    return status.securityLevel != SecurityLevel.critical &&
           !status.isRooted &&
           !status.isJailbroken;
  }
  
  /// Get security recommendations
  static List<String> getSecurityRecommendations() {
    final status = _cachedStatus;
    if (status == null) {
      return ['Please run security check first'];
    }
    
    final recommendations = <String>[];
    
    if (status.isRooted) {
      recommendations.add('Remove root access from your device');
    }
    
    if (status.isJailbroken) {
      recommendations.add('Remove jailbreak from your device');
    }
    
    if (status.isDebuggingEnabled) {
      recommendations.add('Disable developer options and USB debugging');
    }
    
    if (status.hasMaliciousApps) {
      recommendations.add('Remove suspicious applications');
    }
    
    if (status.isEmulator) {
      recommendations.add('Use a physical device for better security');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('Your device security looks good!');
    }
    
    return recommendations;
  }
}

/// Device security status
class DeviceSecurityStatus {
  bool isRooted = false;
  bool isJailbroken = false;
  bool isDebuggingEnabled = false;
  bool isEmulator = false;
  bool hasMaliciousApps = false;
  int deviceIntegrityScore = 0;
  SecurityLevel securityLevel = SecurityLevel.unknown;
  
  bool get isCompromised => isRooted || isJailbroken;
  bool get isSecure => securityLevel == SecurityLevel.high || securityLevel == SecurityLevel.medium;
}

/// Security level enumeration
enum SecurityLevel {
  high,
  medium,
  low,
  critical,
  unknown,
}
