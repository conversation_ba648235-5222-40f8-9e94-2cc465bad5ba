/// Echo Security Framework - Security Monitoring Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive security monitoring service for threat detection,
/// security event logging, and incident response.

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';

import '../utils/logger.dart';
import 'device_security_service.dart';
import 'secure_storage_service.dart';

/// Service for monitoring security events and threats
class SecurityMonitoringService {
  static const String _securityEventsKey = 'security_events';
  static const String _threatDetectionKey = 'threat_detection';
  static const int _maxStoredEvents = 1000;
  static const int _criticalEventThreshold = 5;
  
  static bool _isInitialized = false;
  static bool _isMonitoring = false;
  static final List<SecurityEvent> _eventBuffer = [];
  
  /// Initialize the security monitoring service
  static Future<void> initialize() async {
    try {
      await _loadStoredEvents();
      _isInitialized = true;
      Logger.info('SecurityMonitoringService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize SecurityMonitoringService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Start security monitoring
  static Future<void> startMonitoring() async {
    if (!_isInitialized) {
      throw StateError('SecurityMonitoringService not initialized');
    }
    
    if (_isMonitoring) {
      Logger.warning('Security monitoring already started');
      return;
    }
    
    try {
      _isMonitoring = true;
      
      // Perform initial security check
      await _performInitialSecurityCheck();
      
      // Start periodic monitoring
      _startPeriodicMonitoring();
      
      Logger.info('Security monitoring started');
    } catch (e, stackTrace) {
      Logger.error('Failed to start security monitoring', e, stackTrace);
      _isMonitoring = false;
      rethrow;
    }
  }
  
  /// Stop security monitoring
  static Future<void> stopMonitoring() async {
    if (_isMonitoring) {
      _isMonitoring = false;
      await _flushEventBuffer();
      Logger.info('Security monitoring stopped');
    }
  }
  
  /// Log a security event
  static Future<void> logSecurityEvent(
    SecurityEventType type,
    String description, {
    Map<String, dynamic>? metadata,
    SecuritySeverity severity = SecuritySeverity.medium,
  }) async {
    if (!_isInitialized) {
      Logger.warning('SecurityMonitoringService not initialized, event not logged');
      return;
    }
    
    try {
      final event = SecurityEvent(
        id: _generateEventId(),
        type: type,
        description: description,
        severity: severity,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
        deviceInfo: await _getDeviceInfo(),
      );
      
      _eventBuffer.add(event);
      
      // Log to console
      Logger.security('Security Event: ${type.name}', {
        'description': description,
        'severity': severity.name,
        'metadata': metadata,
      });
      
      // Handle critical events immediately
      if (severity == SecuritySeverity.critical) {
        await _handleCriticalEvent(event);
      }
      
      // Flush buffer if it's getting full
      if (_eventBuffer.length >= 50) {
        await _flushEventBuffer();
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to log security event', e, stackTrace);
    }
  }
  
  /// Detect and log authentication anomalies
  static Future<void> detectAuthenticationAnomaly(
    String userId,
    String authMethod,
    bool success,
  ) async {
    try {
      // Get recent auth events for this user
      final recentEvents = await _getRecentAuthEvents(userId);
      
      // Detect unusual patterns
      if (!success) {
        final failureCount = recentEvents
            .where((e) => !e.metadata['success'] && 
                   e.timestamp.isAfter(DateTime.now().subtract(const Duration(hours: 1))))
            .length;
        
        if (failureCount >= 3) {
          await logSecurityEvent(
            SecurityEventType.authenticationAnomaly,
            'Multiple authentication failures detected',
            metadata: {
              'userId': userId,
              'authMethod': authMethod,
              'failureCount': failureCount,
              'timeWindow': '1 hour',
            },
            severity: SecuritySeverity.high,
          );
        }
      }
      
      // Log the authentication event
      await logSecurityEvent(
        SecurityEventType.authentication,
        'Authentication attempt',
        metadata: {
          'userId': userId,
          'authMethod': authMethod,
          'success': success,
          'userAgent': Platform.operatingSystem,
        },
        severity: success ? SecuritySeverity.low : SecuritySeverity.medium,
      );
    } catch (e, stackTrace) {
      Logger.error('Failed to detect authentication anomaly', e, stackTrace);
    }
  }
  
  /// Detect and log data access anomalies
  static Future<void> detectDataAccessAnomaly(
    String userId,
    String dataType,
    String operation,
  ) async {
    try {
      await logSecurityEvent(
        SecurityEventType.dataAccess,
        'Data access event',
        metadata: {
          'userId': userId,
          'dataType': dataType,
          'operation': operation,
          'timestamp': DateTime.now().toIso8601String(),
        },
        severity: SecuritySeverity.low,
      );
    } catch (e, stackTrace) {
      Logger.error('Failed to detect data access anomaly', e, stackTrace);
    }
  }
  
  /// Detect and log network anomalies
  static Future<void> detectNetworkAnomaly(
    String endpoint,
    int statusCode,
    Duration responseTime,
  ) async {
    try {
      SecuritySeverity severity = SecuritySeverity.low;
      
      // Detect suspicious patterns
      if (statusCode >= 400) {
        severity = SecuritySeverity.medium;
      }
      
      if (responseTime.inSeconds > 30) {
        severity = SecuritySeverity.medium;
      }
      
      await logSecurityEvent(
        SecurityEventType.networkAnomaly,
        'Network request anomaly detected',
        metadata: {
          'endpoint': endpoint,
          'statusCode': statusCode,
          'responseTime': responseTime.inMilliseconds,
          'userAgent': Platform.operatingSystem,
        },
        severity: severity,
      );
    } catch (e, stackTrace) {
      Logger.error('Failed to detect network anomaly', e, stackTrace);
    }
  }
  
  /// Get security event summary
  static Future<SecurityEventSummary> getSecurityEventSummary({
    Duration? timeWindow,
  }) async {
    try {
      final window = timeWindow ?? const Duration(days: 7);
      final cutoff = DateTime.now().subtract(window);
      
      final events = await _getAllStoredEvents();
      final recentEvents = events.where((e) => e.timestamp.isAfter(cutoff)).toList();
      
      final summary = SecurityEventSummary(
        totalEvents: recentEvents.length,
        criticalEvents: recentEvents.where((e) => e.severity == SecuritySeverity.critical).length,
        highSeverityEvents: recentEvents.where((e) => e.severity == SecuritySeverity.high).length,
        mediumSeverityEvents: recentEvents.where((e) => e.severity == SecuritySeverity.medium).length,
        lowSeverityEvents: recentEvents.where((e) => e.severity == SecuritySeverity.low).length,
        timeWindow: window,
        lastEventTime: recentEvents.isNotEmpty 
            ? recentEvents.map((e) => e.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      );
      
      return summary;
    } catch (e, stackTrace) {
      Logger.error('Failed to get security event summary', e, stackTrace);
      return SecurityEventSummary.empty();
    }
  }
  
  /// Perform initial security check
  static Future<void> _performInitialSecurityCheck() async {
    try {
      // Check device security
      final deviceStatus = await DeviceSecurityService.performSecurityCheck();
      
      if (deviceStatus.isCompromised) {
        await logSecurityEvent(
          SecurityEventType.deviceCompromise,
          'Compromised device detected',
          metadata: {
            'isRooted': deviceStatus.isRooted,
            'isJailbroken': deviceStatus.isJailbroken,
            'integrityScore': deviceStatus.deviceIntegrityScore,
          },
          severity: SecuritySeverity.critical,
        );
      }
      
      // Log security check completion
      await logSecurityEvent(
        SecurityEventType.securityCheck,
        'Initial security check completed',
        metadata: {
          'deviceSecure': deviceStatus.isSecure,
          'securityLevel': deviceStatus.securityLevel.name,
        },
        severity: SecuritySeverity.low,
      );
    } catch (e, stackTrace) {
      Logger.error('Failed to perform initial security check', e, stackTrace);
    }
  }
  
  /// Start periodic monitoring
  static void _startPeriodicMonitoring() {
    // Perform security checks every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (!_isMonitoring) {
        timer.cancel();
        return;
      }
      
      try {
        await _performPeriodicSecurityCheck();
      } catch (e) {
        Logger.error('Error in periodic security check', e);
      }
    });
    
    // Flush event buffer every minute
    Timer.periodic(const Duration(minutes: 1), (timer) async {
      if (!_isMonitoring) {
        timer.cancel();
        return;
      }
      
      try {
        await _flushEventBuffer();
      } catch (e) {
        Logger.error('Error flushing event buffer', e);
      }
    });
  }
  
  /// Perform periodic security check
  static Future<void> _performPeriodicSecurityCheck() async {
    try {
      // Check for new security threats
      final deviceStatus = await DeviceSecurityService.performSecurityCheck();
      
      if (!deviceStatus.isSecure) {
        await logSecurityEvent(
          SecurityEventType.threatDetection,
          'Security threat detected during periodic check',
          metadata: {
            'securityLevel': deviceStatus.securityLevel.name,
            'integrityScore': deviceStatus.deviceIntegrityScore,
          },
          severity: SecuritySeverity.high,
        );
      }
    } catch (e) {
      Logger.error('Error in periodic security check', e);
    }
  }
  
  /// Handle critical security events
  static Future<void> _handleCriticalEvent(SecurityEvent event) async {
    try {
      Logger.critical('CRITICAL SECURITY EVENT: ${event.description}');
      
      // Store critical event immediately
      await _storeCriticalEvent(event);
      
      // In production, this would trigger:
      // - Immediate notification to security team
      // - Automatic security measures (e.g., session termination)
      // - Incident response procedures
      
      if (kReleaseMode) {
        // TODO: Implement critical event response
        // - Send alert to security monitoring system
        // - Trigger incident response workflow
        // - Log to external security information and event management (SIEM) system
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to handle critical event', e, stackTrace);
    }
  }
  
  /// Generate unique event ID
  static String _generateEventId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'SEC_${timestamp}_$random';
  }
  
  /// Get device information for events
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'isPhysicalDevice': !kIsWeb,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// Load stored security events
  static Future<void> _loadStoredEvents() async {
    try {
      final eventsJson = await SecureStorageService.getSecureJson(_securityEventsKey);
      if (eventsJson != null) {
        final eventsList = eventsJson['events'] as List<dynamic>? ?? [];
        _eventBuffer.clear();
        _eventBuffer.addAll(
          eventsList.map((e) => SecurityEvent.fromJson(e as Map<String, dynamic>)),
        );
      }
    } catch (e) {
      Logger.error('Failed to load stored security events', e);
    }
  }
  
  /// Flush event buffer to storage
  static Future<void> _flushEventBuffer() async {
    if (_eventBuffer.isEmpty) return;
    
    try {
      final existingEvents = await _getAllStoredEvents();
      final allEvents = [...existingEvents, ..._eventBuffer];
      
      // Keep only the most recent events
      allEvents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      final eventsToStore = allEvents.take(_maxStoredEvents).toList();
      
      await SecureStorageService.storeSecureJson(_securityEventsKey, {
        'events': eventsToStore.map((e) => e.toJson()).toList(),
        'lastUpdated': DateTime.now().toIso8601String(),
      });
      
      _eventBuffer.clear();
      Logger.debug('Flushed ${eventsToStore.length} security events to storage');
    } catch (e, stackTrace) {
      Logger.error('Failed to flush event buffer', e, stackTrace);
    }
  }
  
  /// Get all stored security events
  static Future<List<SecurityEvent>> _getAllStoredEvents() async {
    try {
      final eventsJson = await SecureStorageService.getSecureJson(_securityEventsKey);
      if (eventsJson != null) {
        final eventsList = eventsJson['events'] as List<dynamic>? ?? [];
        return eventsList.map((e) => SecurityEvent.fromJson(e as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      Logger.error('Failed to get all stored events', e);
      return [];
    }
  }
  
  /// Get recent authentication events for a user
  static Future<List<SecurityEvent>> _getRecentAuthEvents(String userId) async {
    try {
      final allEvents = await _getAllStoredEvents();
      return allEvents
          .where((e) => 
              e.type == SecurityEventType.authentication &&
              e.metadata['userId'] == userId &&
              e.timestamp.isAfter(DateTime.now().subtract(const Duration(days: 1))))
          .toList();
    } catch (e) {
      Logger.error('Failed to get recent auth events', e);
      return [];
    }
  }
  
  /// Store critical event immediately
  static Future<void> _storeCriticalEvent(SecurityEvent event) async {
    try {
      final criticalEvents = await SecureStorageService.getSecureJson('critical_security_events') ?? {};
      final eventsList = criticalEvents['events'] as List<dynamic>? ?? [];
      
      eventsList.add(event.toJson());
      
      await SecureStorageService.storeSecureJson('critical_security_events', {
        'events': eventsList,
        'lastUpdated': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      Logger.error('Failed to store critical event', e);
    }
  }
}

/// Security event data class
class SecurityEvent {
  final String id;
  final SecurityEventType type;
  final String description;
  final SecuritySeverity severity;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final Map<String, dynamic> deviceInfo;
  
  SecurityEvent({
    required this.id,
    required this.type,
    required this.description,
    required this.severity,
    required this.timestamp,
    required this.metadata,
    required this.deviceInfo,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'description': description,
      'severity': severity.name,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'deviceInfo': deviceInfo,
    };
  }
  
  factory SecurityEvent.fromJson(Map<String, dynamic> json) {
    return SecurityEvent(
      id: json['id'] as String,
      type: SecurityEventType.values.firstWhere((e) => e.name == json['type']),
      description: json['description'] as String,
      severity: SecuritySeverity.values.firstWhere((e) => e.name == json['severity']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] as Map),
    );
  }
}

/// Security event types
enum SecurityEventType {
  authentication,
  authenticationAnomaly,
  dataAccess,
  networkAnomaly,
  deviceCompromise,
  threatDetection,
  securityCheck,
  configurationChange,
  privilegeEscalation,
  suspiciousActivity,
}

/// Security severity levels
enum SecuritySeverity {
  low,
  medium,
  high,
  critical,
}

/// Security event summary
class SecurityEventSummary {
  final int totalEvents;
  final int criticalEvents;
  final int highSeverityEvents;
  final int mediumSeverityEvents;
  final int lowSeverityEvents;
  final Duration timeWindow;
  final DateTime? lastEventTime;
  
  SecurityEventSummary({
    required this.totalEvents,
    required this.criticalEvents,
    required this.highSeverityEvents,
    required this.mediumSeverityEvents,
    required this.lowSeverityEvents,
    required this.timeWindow,
    this.lastEventTime,
  });
  
  factory SecurityEventSummary.empty() {
    return SecurityEventSummary(
      totalEvents: 0,
      criticalEvents: 0,
      highSeverityEvents: 0,
      mediumSeverityEvents: 0,
      lowSeverityEvents: 0,
      timeWindow: const Duration(days: 7),
    );
  }
}
