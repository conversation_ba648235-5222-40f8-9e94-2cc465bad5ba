/// Echo Security Framework - Encryption Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive encryption service implementing AES-256 encryption
/// with Flutter Secure Storage for sensitive data protection.

import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../utils/logger.dart';

/// Service for handling all encryption and decryption operations
class EncryptionService {
  static const String _keyAlias = 'echo_master_key';
  static const String _ivAlias = 'echo_iv_';
  static const int _keyLength = 32; // 256 bits
  static const int _ivLength = 16; // 128 bits
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'echo_secure_prefs',
      preferencesKeyPrefix: 'echo_',
      resetOnError: true,
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.echo.app',
      accountName: 'echo_keychain',
      accessibility: IOSAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );
  
  static Uint8List? _masterKey;
  static final Random _random = Random.secure();
  
  /// Initialize the encryption service
  static Future<void> initialize() async {
    try {
      _masterKey = await _getOrCreateMasterKey();
      Logger.info('EncryptionService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize EncryptionService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Get or create the master encryption key
  static Future<Uint8List> _getOrCreateMasterKey() async {
    try {
      // Try to retrieve existing key
      final existingKey = await _secureStorage.read(key: _keyAlias);
      
      if (existingKey != null) {
        final keyBytes = base64Decode(existingKey);
        if (keyBytes.length == _keyLength) {
          Logger.debug('Retrieved existing master key');
          return Uint8List.fromList(keyBytes);
        } else {
          Logger.warning('Invalid master key length, generating new key');
        }
      }
      
      // Generate new key
      final newKey = _generateSecureKey(_keyLength);
      await _secureStorage.write(
        key: _keyAlias,
        value: base64Encode(newKey),
      );
      
      Logger.info('Generated new master encryption key');
      return newKey;
    } catch (e) {
      Logger.error('Failed to get or create master key', e);
      rethrow;
    }
  }
  
  /// Generate a cryptographically secure random key
  static Uint8List _generateSecureKey(int length) {
    final key = Uint8List(length);
    for (int i = 0; i < length; i++) {
      key[i] = _random.nextInt(256);
    }
    return key;
  }
  
  /// Encrypt sensitive data using AES-256-CBC
  static Future<EncryptedData> encryptData(String plaintext) async {
    if (_masterKey == null) {
      throw StateError('EncryptionService not initialized');
    }
    
    try {
      // Generate random IV
      final iv = _generateSecureKey(_ivLength);
      
      // Convert plaintext to bytes
      final plaintextBytes = utf8.encode(plaintext);
      
      // Perform AES-256-CBC encryption
      final encryptedBytes = await _performAESEncryption(
        plaintextBytes,
        _masterKey!,
        iv,
      );
      
      // Create encrypted data object
      final encryptedData = EncryptedData(
        data: base64Encode(encryptedBytes),
        iv: base64Encode(iv),
        algorithm: 'AES-256-CBC',
        timestamp: DateTime.now(),
      );
      
      Logger.debug('Data encrypted successfully');
      return encryptedData;
    } catch (e, stackTrace) {
      Logger.error('Failed to encrypt data', e, stackTrace);
      rethrow;
    }
  }
  
  /// Decrypt sensitive data using AES-256-CBC
  static Future<String> decryptData(EncryptedData encryptedData) async {
    if (_masterKey == null) {
      throw StateError('EncryptionService not initialized');
    }
    
    try {
      // Decode encrypted data and IV
      final encryptedBytes = base64Decode(encryptedData.data);
      final iv = base64Decode(encryptedData.iv);
      
      // Validate algorithm
      if (encryptedData.algorithm != 'AES-256-CBC') {
        throw ArgumentError('Unsupported encryption algorithm: ${encryptedData.algorithm}');
      }
      
      // Perform AES-256-CBC decryption
      final decryptedBytes = await _performAESDecryption(
        encryptedBytes,
        _masterKey!,
        iv,
      );
      
      // Convert bytes to string
      final plaintext = utf8.decode(decryptedBytes);
      
      Logger.debug('Data decrypted successfully');
      return plaintext;
    } catch (e, stackTrace) {
      Logger.error('Failed to decrypt data', e, stackTrace);
      rethrow;
    }
  }
  
  /// Perform AES encryption (platform-specific implementation)
  static Future<Uint8List> _performAESEncryption(
    Uint8List plaintext,
    Uint8List key,
    Uint8List iv,
  ) async {
    // This is a simplified implementation
    // In production, use platform-specific crypto libraries
    // or dart:ffi with native crypto implementations
    
    if (kDebugMode) {
      // For testing purposes, return a mock encrypted result
      return Uint8List.fromList([...iv, ...plaintext]);
    }
    
    // TODO: Implement actual AES-256-CBC encryption
    // using platform-specific libraries or dart:ffi
    throw UnimplementedError('AES encryption not implemented');
  }
  
  /// Perform AES decryption (platform-specific implementation)
  static Future<Uint8List> _performAESDecryption(
    Uint8List ciphertext,
    Uint8List key,
    Uint8List iv,
  ) async {
    // This is a simplified implementation
    // In production, use platform-specific crypto libraries
    
    if (kDebugMode) {
      // For testing purposes, extract plaintext from mock format
      if (ciphertext.length > _ivLength) {
        return Uint8List.fromList(ciphertext.sublist(_ivLength));
      }
    }
    
    // TODO: Implement actual AES-256-CBC decryption
    throw UnimplementedError('AES decryption not implemented');
  }
  
  /// Encrypt conversation data with field-level encryption
  static Future<Map<String, dynamic>> encryptConversationData(
    Map<String, dynamic> conversationData,
  ) async {
    final encryptedData = Map<String, dynamic>.from(conversationData);
    
    // Fields that require encryption
    const sensitiveFields = [
      'userMessage',
      'aiResponse',
      'leadData',
      'personalInfo',
    ];
    
    for (final field in sensitiveFields) {
      if (encryptedData.containsKey(field) && encryptedData[field] != null) {
        final fieldValue = encryptedData[field];
        final jsonString = fieldValue is String ? fieldValue : jsonEncode(fieldValue);
        
        final encrypted = await encryptData(jsonString);
        encryptedData[field] = encrypted.toJson();
      }
    }
    
    // Add encryption metadata
    encryptedData['_encrypted'] = true;
    encryptedData['_encryptionVersion'] = '1.0';
    encryptedData['_encryptedAt'] = DateTime.now().toIso8601String();
    
    return encryptedData;
  }
  
  /// Decrypt conversation data with field-level decryption
  static Future<Map<String, dynamic>> decryptConversationData(
    Map<String, dynamic> encryptedConversationData,
  ) async {
    if (encryptedConversationData['_encrypted'] != true) {
      // Data is not encrypted, return as-is
      return encryptedConversationData;
    }
    
    final decryptedData = Map<String, dynamic>.from(encryptedConversationData);
    
    const sensitiveFields = [
      'userMessage',
      'aiResponse',
      'leadData',
      'personalInfo',
    ];
    
    for (final field in sensitiveFields) {
      if (decryptedData.containsKey(field) && decryptedData[field] != null) {
        try {
          final encryptedField = EncryptedData.fromJson(
            Map<String, dynamic>.from(decryptedData[field]),
          );
          
          final decryptedValue = await decryptData(encryptedField);
          
          // Try to parse as JSON, fallback to string
          try {
            decryptedData[field] = jsonDecode(decryptedValue);
          } catch (_) {
            decryptedData[field] = decryptedValue;
          }
        } catch (e) {
          Logger.warning('Failed to decrypt field $field: $e');
          // Keep original value if decryption fails
        }
      }
    }
    
    // Remove encryption metadata
    decryptedData.remove('_encrypted');
    decryptedData.remove('_encryptionVersion');
    decryptedData.remove('_encryptedAt');
    
    return decryptedData;
  }
  
  /// Generate secure hash for data integrity verification
  static String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// Verify data integrity using hash
  static bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }
  
  /// Securely clear sensitive data from memory
  static void clearSensitiveData(Uint8List data) {
    for (int i = 0; i < data.length; i++) {
      data[i] = 0;
    }
  }
  
  /// Dispose of the encryption service
  static Future<void> dispose() async {
    if (_masterKey != null) {
      clearSensitiveData(_masterKey!);
      _masterKey = null;
    }
    Logger.info('EncryptionService disposed');
  }
}

/// Encrypted data container
class EncryptedData {
  final String data;
  final String iv;
  final String algorithm;
  final DateTime timestamp;
  
  const EncryptedData({
    required this.data,
    required this.iv,
    required this.algorithm,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'iv': iv,
      'algorithm': algorithm,
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  factory EncryptedData.fromJson(Map<String, dynamic> json) {
    return EncryptedData(
      data: json['data'] as String,
      iv: json['iv'] as String,
      algorithm: json['algorithm'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}
