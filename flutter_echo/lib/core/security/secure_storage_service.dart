/// Echo Security Framework - Secure Storage Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Secure storage service providing encrypted storage for sensitive data
/// using platform-specific secure storage mechanisms.

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../utils/logger.dart';
import 'encryption_service.dart';

/// Service for secure storage of sensitive data
class SecureStorageService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'echo_secure_prefs',
      preferencesKeyPrefix: 'echo_',
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
      resetOnError: true,
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.echo.app',
      accountName: 'echo_keychain',
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );
  
  static bool _isInitialized = false;
  
  /// Initialize the secure storage service
  static Future<void> initialize() async {
    try {
      // Test storage availability
      await _testStorageAvailability();
      _isInitialized = true;
      Logger.info('SecureStorageService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize SecureStorageService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Test if secure storage is available and working
  static Future<void> _testStorageAvailability() async {
    const testKey = 'echo_storage_test';
    const testValue = 'test_value';
    
    try {
      // Write test value
      await _secureStorage.write(key: testKey, value: testValue);
      
      // Read test value
      final readValue = await _secureStorage.read(key: testKey);
      
      if (readValue != testValue) {
        throw Exception('Secure storage test failed: value mismatch');
      }
      
      // Clean up test value
      await _secureStorage.delete(key: testKey);
      
      Logger.debug('Secure storage test passed');
    } catch (e) {
      Logger.error('Secure storage test failed', e);
      rethrow;
    }
  }
  
  /// Store sensitive data securely
  static Future<void> storeSecureData(String key, String value) async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      // Add additional encryption for highly sensitive data
      if (_isHighlySensitive(key)) {
        final encryptedData = await EncryptionService.encryptData(value);
        final encryptedJson = jsonEncode(encryptedData.toJson());
        await _secureStorage.write(key: key, value: encryptedJson);
        Logger.debug('Stored highly sensitive data with double encryption: $key');
      } else {
        await _secureStorage.write(key: key, value: value);
        Logger.debug('Stored secure data: $key');
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to store secure data for key: $key', e, stackTrace);
      rethrow;
    }
  }
  
  /// Retrieve sensitive data securely
  static Future<String?> getSecureData(String key) async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      final value = await _secureStorage.read(key: key);
      
      if (value == null) {
        return null;
      }
      
      // Decrypt if highly sensitive data
      if (_isHighlySensitive(key)) {
        try {
          final encryptedJson = jsonDecode(value) as Map<String, dynamic>;
          final encryptedData = EncryptedData.fromJson(encryptedJson);
          final decryptedValue = await EncryptionService.decryptData(encryptedData);
          Logger.debug('Retrieved highly sensitive data: $key');
          return decryptedValue;
        } catch (e) {
          Logger.warning('Failed to decrypt highly sensitive data, returning raw value: $key');
          return value;
        }
      }
      
      Logger.debug('Retrieved secure data: $key');
      return value;
    } catch (e, stackTrace) {
      Logger.error('Failed to retrieve secure data for key: $key', e, stackTrace);
      return null;
    }
  }
  
  /// Store JSON data securely
  static Future<void> storeSecureJson(String key, Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    await storeSecureData(key, jsonString);
  }
  
  /// Retrieve JSON data securely
  static Future<Map<String, dynamic>?> getSecureJson(String key) async {
    final jsonString = await getSecureData(key);
    if (jsonString == null) {
      return null;
    }
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      Logger.error('Failed to parse JSON for key: $key', e);
      return null;
    }
  }
  
  /// Delete sensitive data
  static Future<void> deleteSecureData(String key) async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      await _secureStorage.delete(key: key);
      Logger.debug('Deleted secure data: $key');
    } catch (e, stackTrace) {
      Logger.error('Failed to delete secure data for key: $key', e, stackTrace);
      rethrow;
    }
  }
  
  /// Check if a key exists in secure storage
  static Future<bool> containsKey(String key) async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      final value = await _secureStorage.read(key: key);
      return value != null;
    } catch (e) {
      Logger.error('Failed to check key existence: $key', e);
      return false;
    }
  }
  
  /// Get all keys in secure storage
  static Future<Set<String>> getAllKeys() async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.toSet();
    } catch (e, stackTrace) {
      Logger.error('Failed to get all keys', e, stackTrace);
      return <String>{};
    }
  }
  
  /// Clear all secure storage data
  static Future<void> clearAllSecureData() async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      await _secureStorage.deleteAll();
      Logger.info('Cleared all secure storage data');
    } catch (e, stackTrace) {
      Logger.error('Failed to clear all secure data', e, stackTrace);
      rethrow;
    }
  }
  
  /// Store user session data
  static Future<void> storeUserSession(Map<String, dynamic> sessionData) async {
    await storeSecureJson('user_session', sessionData);
  }
  
  /// Retrieve user session data
  static Future<Map<String, dynamic>?> getUserSession() async {
    return await getSecureJson('user_session');
  }
  
  /// Clear user session data
  static Future<void> clearUserSession() async {
    await deleteSecureData('user_session');
  }
  
  /// Store API tokens securely
  static Future<void> storeApiTokens(Map<String, String> tokens) async {
    for (final entry in tokens.entries) {
      await storeSecureData('api_token_${entry.key}', entry.value);
    }
  }
  
  /// Retrieve API token
  static Future<String?> getApiToken(String tokenType) async {
    return await getSecureData('api_token_$tokenType');
  }
  
  /// Clear all API tokens
  static Future<void> clearApiTokens() async {
    final allKeys = await getAllKeys();
    final tokenKeys = allKeys.where((key) => key.startsWith('api_token_'));
    
    for (final key in tokenKeys) {
      await deleteSecureData(key);
    }
  }
  
  /// Store conversation encryption keys
  static Future<void> storeConversationKey(String conversationId, String key) async {
    await storeSecureData('conv_key_$conversationId', key);
  }
  
  /// Retrieve conversation encryption key
  static Future<String?> getConversationKey(String conversationId) async {
    return await getSecureData('conv_key_$conversationId');
  }
  
  /// Store user preferences securely
  static Future<void> storeUserPreferences(Map<String, dynamic> preferences) async {
    await storeSecureJson('user_preferences', preferences);
  }
  
  /// Retrieve user preferences
  static Future<Map<String, dynamic>?> getUserPreferences() async {
    return await getSecureJson('user_preferences');
  }
  
  /// Check if data type requires highly sensitive encryption
  static bool _isHighlySensitive(String key) {
    const highlySensitiveKeys = [
      'api_token_',
      'user_session',
      'conv_key_',
      'biometric_',
      'auth_',
      'encryption_',
    ];
    
    return highlySensitiveKeys.any((sensitiveKey) => key.startsWith(sensitiveKey));
  }
  
  /// Export secure data for backup (encrypted)
  static Future<Map<String, String>> exportSecureData() async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      final allData = await _secureStorage.readAll();
      
      // Filter out temporary or session data
      final exportableData = <String, String>{};
      for (final entry in allData.entries) {
        if (!_isTemporaryData(entry.key)) {
          exportableData[entry.key] = entry.value;
        }
      }
      
      Logger.info('Exported ${exportableData.length} secure data entries');
      return exportableData;
    } catch (e, stackTrace) {
      Logger.error('Failed to export secure data', e, stackTrace);
      rethrow;
    }
  }
  
  /// Import secure data from backup
  static Future<void> importSecureData(Map<String, String> data) async {
    if (!_isInitialized) {
      throw StateError('SecureStorageService not initialized');
    }
    
    try {
      for (final entry in data.entries) {
        await _secureStorage.write(key: entry.key, value: entry.value);
      }
      
      Logger.info('Imported ${data.length} secure data entries');
    } catch (e, stackTrace) {
      Logger.error('Failed to import secure data', e, stackTrace);
      rethrow;
    }
  }
  
  /// Check if data is temporary and should not be exported
  static bool _isTemporaryData(String key) {
    const temporaryKeys = [
      'temp_',
      'cache_',
      'session_',
      'last_auth_',
    ];
    
    return temporaryKeys.any((tempKey) => key.startsWith(tempKey));
  }
}
