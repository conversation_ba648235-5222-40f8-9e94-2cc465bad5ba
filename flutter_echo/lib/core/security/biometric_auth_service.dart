/// Echo Security Framework - Biometric Authentication Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive biometric authentication service supporting Face ID, Touch ID,
/// and fingerprint authentication with fallback mechanisms.

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';

import '../utils/logger.dart';
import 'secure_storage_service.dart';

/// Service for handling biometric authentication
class BiometricAuthService {
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _lastAuthTimestampKey = 'last_auth_timestamp';
  static const String _authFailureCountKey = 'auth_failure_count';
  static const int _maxFailureCount = 5;
  static const int _lockoutDurationMinutes = 30;
  
  static final LocalAuthentication _localAuth = LocalAuthentication();
  static bool _isInitialized = false;
  
  /// Initialize the biometric authentication service
  static Future<void> initialize() async {
    try {
      _isInitialized = true;
      Logger.info('BiometricAuthService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize BiometricAuthService', e, stackTrace);
      rethrow;
    }
  }
  
  /// Check if biometric authentication is available on the device
  static Future<BiometricAvailability> checkBiometricAvailability() async {
    try {
      // Check if device supports biometrics
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return BiometricAvailability.notAvailable;
      }
      
      // Check if biometrics are enrolled
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      if (!isDeviceSupported) {
        return BiometricAvailability.notSupported;
      }
      
      // Get available biometric types
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricAvailability.notEnrolled;
      }
      
      // Determine specific biometric types
      if (Platform.isIOS) {
        if (availableBiometrics.contains(BiometricType.face)) {
          return BiometricAvailability.faceId;
        } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
          return BiometricAvailability.touchId;
        }
      } else if (Platform.isAndroid) {
        if (availableBiometrics.contains(BiometricType.face)) {
          return BiometricAvailability.faceUnlock;
        } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
          return BiometricAvailability.fingerprint;
        }
      }
      
      return BiometricAvailability.available;
    } catch (e, stackTrace) {
      Logger.error('Failed to check biometric availability', e, stackTrace);
      return BiometricAvailability.error;
    }
  }
  
  /// Authenticate user with biometrics
  static Future<BiometricAuthResult> authenticateWithBiometrics({
    String? reason,
    bool sensitiveTransaction = true,
    bool stickyAuth = true,
  }) async {
    if (!_isInitialized) {
      throw StateError('BiometricAuthService not initialized');
    }
    
    try {
      // Check if biometrics are enabled
      final isEnabled = await isBiometricEnabled();
      if (!isEnabled) {
        return BiometricAuthResult.disabled;
      }
      
      // Check for lockout
      final isLockedOut = await _isLockedOut();
      if (isLockedOut) {
        return BiometricAuthResult.lockedOut;
      }
      
      // Check availability
      final availability = await checkBiometricAvailability();
      if (availability == BiometricAvailability.notAvailable ||
          availability == BiometricAvailability.notSupported ||
          availability == BiometricAvailability.notEnrolled) {
        return BiometricAuthResult.notAvailable;
      }
      
      // Perform authentication
      final authReason = reason ?? _getDefaultAuthReason();
      
      final didAuthenticate = await _localAuth.authenticate(
        localizedReason: authReason,
        authMessages: _getAuthMessages(),
        options: AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: stickyAuth,
          sensitiveTransaction: sensitiveTransaction,
        ),
      );
      
      if (didAuthenticate) {
        await _onAuthSuccess();
        return BiometricAuthResult.success;
      } else {
        await _onAuthFailure();
        return BiometricAuthResult.failed;
      }
    } on PlatformException catch (e) {
      Logger.error('Platform exception during biometric auth', e);
      
      switch (e.code) {
        case 'NotAvailable':
          return BiometricAuthResult.notAvailable;
        case 'NotEnrolled':
          return BiometricAuthResult.notEnrolled;
        case 'PasscodeNotSet':
          return BiometricAuthResult.passcodeNotSet;
        case 'LockedOut':
          await _onAuthFailure();
          return BiometricAuthResult.lockedOut;
        case 'PermanentlyLockedOut':
          return BiometricAuthResult.permanentlyLockedOut;
        case 'UserCancel':
          return BiometricAuthResult.userCancel;
        case 'UserFallback':
          return BiometricAuthResult.userFallback;
        case 'SystemCancel':
          return BiometricAuthResult.systemCancel;
        case 'InvalidContext':
          return BiometricAuthResult.invalidContext;
        default:
          await _onAuthFailure();
          return BiometricAuthResult.error;
      }
    } catch (e, stackTrace) {
      Logger.error('Unexpected error during biometric auth', e, stackTrace);
      return BiometricAuthResult.error;
    }
  }
  
  /// Enable biometric authentication
  static Future<bool> enableBiometricAuth() async {
    try {
      final availability = await checkBiometricAvailability();
      if (availability == BiometricAvailability.notAvailable ||
          availability == BiometricAvailability.notSupported ||
          availability == BiometricAvailability.notEnrolled) {
        return false;
      }
      
      // Test authentication before enabling
      final result = await authenticateWithBiometrics(
        reason: 'Enable biometric authentication for Echo',
      );
      
      if (result == BiometricAuthResult.success) {
        await SecureStorageService.storeSecureData(_biometricEnabledKey, 'true');
        Logger.info('Biometric authentication enabled');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      Logger.error('Failed to enable biometric auth', e, stackTrace);
      return false;
    }
  }
  
  /// Disable biometric authentication
  static Future<void> disableBiometricAuth() async {
    try {
      await SecureStorageService.deleteSecureData(_biometricEnabledKey);
      await SecureStorageService.deleteSecureData(_lastAuthTimestampKey);
      await SecureStorageService.deleteSecureData(_authFailureCountKey);
      Logger.info('Biometric authentication disabled');
    } catch (e, stackTrace) {
      Logger.error('Failed to disable biometric auth', e, stackTrace);
    }
  }
  
  /// Check if biometric authentication is enabled
  static Future<bool> isBiometricEnabled() async {
    try {
      final enabled = await SecureStorageService.getSecureData(_biometricEnabledKey);
      return enabled == 'true';
    } catch (e) {
      Logger.error('Failed to check biometric enabled status', e);
      return false;
    }
  }
  
  /// Get available biometric types as user-friendly strings
  static Future<List<String>> getAvailableBiometricTypes() async {
    try {
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      final types = <String>[];
      
      for (final biometric in availableBiometrics) {
        switch (biometric) {
          case BiometricType.face:
            types.add(Platform.isIOS ? 'Face ID' : 'Face Unlock');
            break;
          case BiometricType.fingerprint:
            types.add(Platform.isIOS ? 'Touch ID' : 'Fingerprint');
            break;
          case BiometricType.iris:
            types.add('Iris');
            break;
          case BiometricType.strong:
            types.add('Strong Biometric');
            break;
          case BiometricType.weak:
            types.add('Weak Biometric');
            break;
        }
      }
      
      return types;
    } catch (e) {
      Logger.error('Failed to get available biometric types', e);
      return [];
    }
  }
  
  /// Handle successful authentication
  static Future<void> _onAuthSuccess() async {
    try {
      await SecureStorageService.storeSecureData(
        _lastAuthTimestampKey,
        DateTime.now().millisecondsSinceEpoch.toString(),
      );
      await SecureStorageService.deleteSecureData(_authFailureCountKey);
      Logger.debug('Biometric authentication successful');
    } catch (e) {
      Logger.error('Failed to handle auth success', e);
    }
  }
  
  /// Handle failed authentication
  static Future<void> _onAuthFailure() async {
    try {
      final currentCount = await _getFailureCount();
      final newCount = currentCount + 1;
      
      await SecureStorageService.storeSecureData(
        _authFailureCountKey,
        newCount.toString(),
      );
      
      Logger.warning('Biometric authentication failed. Count: $newCount');
      
      if (newCount >= _maxFailureCount) {
        Logger.warning('Maximum auth failures reached. Locking out user.');
      }
    } catch (e) {
      Logger.error('Failed to handle auth failure', e);
    }
  }
  
  /// Check if user is locked out due to too many failures
  static Future<bool> _isLockedOut() async {
    try {
      final failureCount = await _getFailureCount();
      if (failureCount < _maxFailureCount) {
        return false;
      }
      
      final lastAuthStr = await SecureStorageService.getSecureData(_lastAuthTimestampKey);
      if (lastAuthStr == null) {
        return true; // No successful auth recorded, stay locked
      }
      
      final lastAuth = DateTime.fromMillisecondsSinceEpoch(int.parse(lastAuthStr));
      final lockoutEnd = lastAuth.add(const Duration(minutes: _lockoutDurationMinutes));
      
      return DateTime.now().isBefore(lockoutEnd);
    } catch (e) {
      Logger.error('Failed to check lockout status', e);
      return false;
    }
  }
  
  /// Get current failure count
  static Future<int> _getFailureCount() async {
    try {
      final countStr = await SecureStorageService.getSecureData(_authFailureCountKey);
      return countStr != null ? int.parse(countStr) : 0;
    } catch (e) {
      return 0;
    }
  }
  
  /// Get default authentication reason based on platform
  static String _getDefaultAuthReason() {
    if (Platform.isIOS) {
      return 'Use Face ID or Touch ID to access Echo';
    } else {
      return 'Use fingerprint or face unlock to access Echo';
    }
  }
  
  /// Get platform-specific authentication messages
  static List<AuthMessages> _getAuthMessages() {
    return [
      const AndroidAuthMessages(
        signInTitle: 'Echo Authentication',
        cancelButton: 'Cancel',
        deviceCredentialsRequiredTitle: 'Device Credentials Required',
        deviceCredentialsSetupDescription: 'Please set up device credentials',
        goToSettingsButton: 'Go to Settings',
        goToSettingsDescription: 'Set up biometric authentication in Settings',
        biometricHint: 'Touch sensor',
        biometricNotRecognized: 'Biometric not recognized. Try again.',
        biometricRequiredTitle: 'Biometric Required',
        biometricSuccess: 'Biometric authentication successful',
      ),
      const IOSAuthMessages(
        cancelButton: 'Cancel',
        goToSettingsButton: 'Go to Settings',
        goToSettingsDescription: 'Set up biometric authentication in Settings',
        lockOut: 'Biometric authentication is disabled. Please lock and unlock your screen to enable it.',
      ),
    ];
  }
}

/// Biometric availability status
enum BiometricAvailability {
  available,
  faceId,
  touchId,
  faceUnlock,
  fingerprint,
  notAvailable,
  notSupported,
  notEnrolled,
  error,
}

/// Biometric authentication result
enum BiometricAuthResult {
  success,
  failed,
  disabled,
  notAvailable,
  notEnrolled,
  passcodeNotSet,
  lockedOut,
  permanentlyLockedOut,
  userCancel,
  userFallback,
  systemCancel,
  invalidContext,
  error,
}
