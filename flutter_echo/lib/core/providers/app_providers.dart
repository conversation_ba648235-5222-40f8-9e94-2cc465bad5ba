/// Echo Application Providers
/// Copyright (c) 2025 Echo Inc.
/// 
/// Global Riverpod providers for core application services.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../constants/app_constants.dart';
import '../network/api_client.dart';
import '../network/websocket_client.dart';
import '../services/analytics_service.dart';
import '../services/error_service.dart';
import '../services/logging_service.dart';
import '../storage/local_storage.dart';
import '../security/encryption_service.dart';
import '../security/biometric_auth_service.dart';
import '../security/device_security_service.dart';
import '../security/security_monitoring_service.dart';
import '../storage/storage_optimization_service.dart';
import '../utils/logger.dart';
import '../../features/chat/data/repositories/conversation_repository.dart';
import '../../features/sync/services/offline_sync_service.dart';
import '../../features/voice/data/repositories/voice_repository.dart';
import '../../features/voice/services/audio_recording_service.dart';
import '../../features/voice/services/text_to_speech_service.dart';

/// Core Services Providers

/// Dio HTTP client provider
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio(BaseOptions(
    baseUrl: AppConstants.baseApiUrl,
    connectTimeout: AppConstants.connectionTimeout,
    receiveTimeout: AppConstants.networkTimeout,
    sendTimeout: AppConstants.networkTimeout,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  ));

  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: AppConstants.enableNetworkLogging,
    responseBody: AppConstants.enableNetworkLogging,
    logPrint: (object) => Logger.debug('HTTP: $object'),
  ));

  return dio;
});

/// API client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  final dio = ref.watch(dioProvider);
  return ApiClient(dio);
});

/// WebSocket client provider
final websocketClientProvider = Provider<WebSocketClient>((ref) {
  return WebSocketClient(AppConstants.baseWebsocketUrl);
});

/// Local storage provider
final localStorageProvider = Provider<LocalStorage>((ref) {
  return LocalStorage();
});

/// Hive box providers
final conversationBoxProvider = Provider<Box>((ref) {
  return Hive.box('conversations');
});

final userBoxProvider = Provider<Box>((ref) {
  return Hive.box('user');
});

final settingsBoxProvider = Provider<Box>((ref) {
  return Hive.box('settings');
});

final cacheBoxProvider = Provider<Box>((ref) {
  return Hive.box('cache');
});

/// Security Services Providers

/// Encryption service provider
final encryptionServiceProvider = Provider<EncryptionService>((ref) {
  return EncryptionService();
});

/// Biometric authentication service provider
final biometricAuthServiceProvider = Provider<BiometricAuthService>((ref) {
  return BiometricAuthService();
});

/// Device security service provider
final deviceSecurityServiceProvider = Provider<DeviceSecurityService>((ref) {
  return DeviceSecurityService();
});

/// Security monitoring service provider
final securityMonitoringServiceProvider = Provider<SecurityMonitoringService>((ref) {
  return SecurityMonitoringService();
});

/// Application Services Providers

/// Analytics service provider
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

/// Error service provider
final errorServiceProvider = Provider<ErrorService>((ref) {
  return ErrorService();
});

/// Logging service provider
final loggingServiceProvider = Provider<LoggingService>((ref) {
  return LoggingService();
});

/// State Management Providers

/// App initialization state provider
final appInitializationProvider = FutureProvider<bool>((ref) async {
  try {
    // Initialize core services
    await ref.read(encryptionServiceProvider).initialize();
    await ref.read(biometricAuthServiceProvider).initialize();
    await ref.read(deviceSecurityServiceProvider).initialize();
    await ref.read(securityMonitoringServiceProvider).initialize();
    
    // Start security monitoring
    await ref.read(securityMonitoringServiceProvider).startMonitoring();
    
    // Initialize analytics
    await ref.read(analyticsServiceProvider).initialize();
    
    Logger.info('App initialization completed successfully');
    return true;
  } catch (e, stackTrace) {
    Logger.error('App initialization failed', e, stackTrace);
    return false;
  }
});

/// Network connectivity provider
final connectivityProvider = StreamProvider<bool>((ref) {
  // This would typically use connectivity_plus package
  // For now, return a simple stream
  return Stream.periodic(
    const Duration(seconds: 30),
    (_) => true, // Assume connected for now
  );
});

/// Device info provider
final deviceInfoProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final deviceSecurity = ref.read(deviceSecurityServiceProvider);
  
  return {
    'isRooted': await deviceSecurity.isDeviceRooted(),
    'isJailbroken': await deviceSecurity.isDeviceJailbroken(),
    'isEmulator': await deviceSecurity.isRunningOnEmulator(),
    'securityScore': await deviceSecurity.getDeviceSecurityScore(),
  };
});

/// Session state provider
final sessionStateProvider = StateNotifierProvider<SessionStateNotifier, SessionState>((ref) {
  return SessionStateNotifier();
});

/// Session state model
class SessionState {
  final String? sessionId;
  final String? accessToken;
  final bool isAuthenticated;
  final bool hasAccessPermission;
  final DateTime? lastActivity;
  final Map<String, dynamic> userPreferences;

  const SessionState({
    this.sessionId,
    this.accessToken,
    this.isAuthenticated = false,
    this.hasAccessPermission = false,
    this.lastActivity,
    this.userPreferences = const {},
  });

  SessionState copyWith({
    String? sessionId,
    String? accessToken,
    bool? isAuthenticated,
    bool? hasAccessPermission,
    DateTime? lastActivity,
    Map<String, dynamic>? userPreferences,
  }) {
    return SessionState(
      sessionId: sessionId ?? this.sessionId,
      accessToken: accessToken ?? this.accessToken,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      hasAccessPermission: hasAccessPermission ?? this.hasAccessPermission,
      lastActivity: lastActivity ?? this.lastActivity,
      userPreferences: userPreferences ?? this.userPreferences,
    );
  }
}

/// Session state notifier
class SessionStateNotifier extends StateNotifier<SessionState> {
  SessionStateNotifier() : super(const SessionState());

  void setSession({
    required String sessionId,
    required String accessToken,
  }) {
    state = state.copyWith(
      sessionId: sessionId,
      accessToken: accessToken,
      isAuthenticated: true,
      lastActivity: DateTime.now(),
    );
  }

  void setAccessPermission(bool hasAccess) {
    state = state.copyWith(hasAccessPermission: hasAccess);
  }

  void updateActivity() {
    state = state.copyWith(lastActivity: DateTime.now());
  }

  void updatePreferences(Map<String, dynamic> preferences) {
    state = state.copyWith(userPreferences: preferences);
  }

  void clearSession() {
    state = const SessionState();
  }
}

/// Performance monitoring provider
final performanceMonitoringProvider = Provider<PerformanceMonitoring>((ref) {
  return PerformanceMonitoring();
});

/// Simple performance monitoring class
class PerformanceMonitoring {
  final Map<String, DateTime> _startTimes = {};
  final Map<String, Duration> _durations = {};

  void startTimer(String operation) {
    _startTimes[operation] = DateTime.now();
  }

  void endTimer(String operation) {
    final startTime = _startTimes[operation];
    if (startTime != null) {
      _durations[operation] = DateTime.now().difference(startTime);
      _startTimes.remove(operation);
      
      Logger.debug('Performance: $operation took ${_durations[operation]?.inMilliseconds}ms');
    }
  }

  Duration? getDuration(String operation) {
    return _durations[operation];
  }

  Map<String, Duration> getAllDurations() {
    return Map.from(_durations);
  }

  void clearMetrics() {
    _startTimes.clear();
    _durations.clear();
  }
}

/// Feature flags provider
final featureFlagsProvider = Provider<Map<String, bool>>((ref) {
  return {
    'voiceFeatures': AppConstants.enableVoiceFeatures,
    'biometricAuth': AppConstants.enableBiometricAuth,
    'offlineMode': AppConstants.enableOfflineMode,
    'analytics': AppConstants.enableAnalytics,
    'crashReporting': AppConstants.enableCrashReporting,
  };
});

/// App configuration provider
final appConfigProvider = Provider<AppConfig>((ref) {
  return AppConfig(
    apiBaseUrl: AppConstants.baseApiUrl,
    websocketUrl: AppConstants.baseWebsocketUrl,
    environment: EnvironmentConfig.environment,
    version: AppConstants.appVersion,
    buildNumber: AppConstants.appBuildNumber,
  );
});

/// App configuration model
class AppConfig {
  final String apiBaseUrl;
  final String websocketUrl;
  final String environment;
  final String version;
  final String buildNumber;

  const AppConfig({
    required this.apiBaseUrl,
    required this.websocketUrl,
    required this.environment,
    required this.version,
    required this.buildNumber,
  });

  bool get isDevelopment => environment == 'development';
  bool get isStaging => environment == 'staging';
  bool get isProduction => environment == 'production';
}

/// Data Repository Providers

/// Conversation repository provider
final conversationRepositoryProvider = Provider<ConversationRepository>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return ConversationRepository(apiClient);
});

/// Offline sync service provider
final offlineSyncServiceProvider = Provider<OfflineSyncService>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return OfflineSyncService(apiClient, Connectivity());
});

/// Storage optimization service provider
final storageOptimizationProvider = Provider<StorageOptimizationService>((ref) {
  return StorageOptimizationService();
});

/// Voice repository provider
final voiceRepositoryProvider = Provider<VoiceRepository>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return VoiceRepository(apiClient);
});

/// Audio recording service provider
final audioRecordingServiceProvider = Provider<AudioRecordingService>((ref) {
  return AudioRecordingService();
});

/// Text-to-speech service provider
final textToSpeechServiceProvider = Provider<TextToSpeechService>((ref) {
  return TextToSpeechService();
});
