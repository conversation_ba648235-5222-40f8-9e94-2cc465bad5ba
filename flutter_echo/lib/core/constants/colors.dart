/// Echo Color Palette
/// Copyright (c) 2025 Echo Inc.
/// 
/// Application color scheme and theme colors.

import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF2563EB); // Blue 600
  static const Color primaryLight = Color(0xFF3B82F6); // Blue 500
  static const Color primaryDark = Color(0xFF1D4ED8); // Blue 700
  static const Color primaryVariant = Color(0xFF1E40AF); // Blue 800
  
  // Secondary Colors
  static const Color secondary = Color(0xFFFFEE8C); // Yellow accent
  static const Color secondaryLight = Color(0xFFFEF3C7); // Yellow 100
  static const Color secondaryDark = Color(0xFFF59E0B); // Yellow 500
  
  // Background Colors
  static const Color backgroundLight = Color(0xFFFFFFFF); // White
  static const Color backgroundDark = Color(0xFF0F172A); // Slate 900
  static const Color surfaceLight = Color(0xFFF8FAFC); // Slate 50
  static const Color surfaceDark = Color(0xFF1E293B); // Slate 800
  
  // Text Colors
  static const Color textPrimary = Color(0xFF0F172A); // Slate 900
  static const Color textSecondary = Color(0xFF475569); // Slate 600
  static const Color textTertiary = Color(0xFF94A3B8); // Slate 400
  static const Color textInverse = Color(0xFFFFFFFF); // White
  
  // Dark Theme Text Colors
  static const Color textPrimaryDark = Color(0xFFF8FAFC); // Slate 50
  static const Color textSecondaryDark = Color(0xFFCBD5E1); // Slate 300
  static const Color textTertiaryDark = Color(0xFF64748B); // Slate 500
  
  // Status Colors
  static const Color success = Color(0xFF10B981); // Emerald 500
  static const Color successLight = Color(0xFFD1FAE5); // Emerald 100
  static const Color successDark = Color(0xFF059669); // Emerald 600
  
  static const Color warning = Color(0xFFF59E0B); // Amber 500
  static const Color warningLight = Color(0xFFFEF3C7); // Amber 100
  static const Color warningDark = Color(0xFFD97706); // Amber 600
  
  static const Color error = Color(0xFFEF4444); // Red 500
  static const Color errorLight = Color(0xFFFEE2E2); // Red 100
  static const Color errorDark = Color(0xFFDC2626); // Red 600
  
  static const Color info = Color(0xFF3B82F6); // Blue 500
  static const Color infoLight = Color(0xFFDBEAFE); // Blue 100
  static const Color infoDark = Color(0xFF2563EB); // Blue 600
  
  // Neutral Colors
  static const Color neutral50 = Color(0xFFF8FAFC);
  static const Color neutral100 = Color(0xFFF1F5F9);
  static const Color neutral200 = Color(0xFFE2E8F0);
  static const Color neutral300 = Color(0xFFCBD5E1);
  static const Color neutral400 = Color(0xFF94A3B8);
  static const Color neutral500 = Color(0xFF64748B);
  static const Color neutral600 = Color(0xFF475569);
  static const Color neutral700 = Color(0xFF334155);
  static const Color neutral800 = Color(0xFF1E293B);
  static const Color neutral900 = Color(0xFF0F172A);
  
  // Voice Recording Colors
  static const Color voiceRecording = Color(0xFFEF4444); // Red 500
  static const Color voiceRecordingLight = Color(0xFFFEE2E2); // Red 100
  static const Color voiceIdle = Color(0xFF94A3B8); // Slate 400
  static const Color voiceProcessing = Color(0xFFF59E0B); // Amber 500
  
  // Stock Colors
  static const Color stockPositive = Color(0xFF10B981); // Emerald 500
  static const Color stockNegative = Color(0xFFEF4444); // Red 500
  static const Color stockNeutral = Color(0xFF64748B); // Slate 500
  
  // Chat Colors
  static const Color userMessageBg = Color(0xFF2563EB); // Blue 600
  static const Color userMessageText = Color(0xFFFFFFFF); // White
  static const Color aiMessageBg = Color(0xFFF1F5F9); // Slate 100
  static const Color aiMessageBgDark = Color(0xFF334155); // Slate 700
  static const Color aiMessageText = Color(0xFF0F172A); // Slate 900
  static const Color aiMessageTextDark = Color(0xFFF8FAFC); // Slate 50
  
  // Border Colors
  static const Color borderLight = Color(0xFFE2E8F0); // Slate 200
  static const Color borderDark = Color(0xFF475569); // Slate 600
  static const Color borderFocus = Color(0xFF3B82F6); // Blue 500
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000); // Black 10%
  static const Color shadowMedium = Color(0x33000000); // Black 20%
  static const Color shadowDark = Color(0x4D000000); // Black 30%
  
  // Overlay Colors
  static const Color overlayLight = Color(0x80FFFFFF); // White 50%
  static const Color overlayDark = Color(0x80000000); // Black 50%
  static const Color scrim = Color(0xB3000000); // Black 70%
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary],
  );
  
  static const LinearGradient voiceGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [voiceRecording, Color(0xFFDC2626)], // Red 600
  );
  
  // Material 3 Color Scheme (Light)
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary,
    onPrimary: textInverse,
    primaryContainer: Color(0xFFDBEAFE), // Blue 100
    onPrimaryContainer: Color(0xFF1E3A8A), // Blue 900
    secondary: secondary,
    onSecondary: textPrimary,
    secondaryContainer: secondaryLight,
    onSecondaryContainer: Color(0xFF92400E), // Yellow 800
    tertiary: Color(0xFF7C3AED), // Violet 600
    onTertiary: textInverse,
    tertiaryContainer: Color(0xFFEDE9FE), // Violet 100
    onTertiaryContainer: Color(0xFF5B21B6), // Violet 800
    error: error,
    onError: textInverse,
    errorContainer: errorLight,
    onErrorContainer: errorDark,
    background: backgroundLight,
    onBackground: textPrimary,
    surface: surfaceLight,
    onSurface: textPrimary,
    surfaceVariant: neutral100,
    onSurfaceVariant: neutral600,
    outline: neutral300,
    outlineVariant: neutral200,
    shadow: shadowLight,
    scrim: scrim,
    inverseSurface: neutral800,
    onInverseSurface: neutral100,
    inversePrimary: Color(0xFF93C5FD), // Blue 300
    surfaceTint: primary,
  );
  
  // Material 3 Color Scheme (Dark)
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF93C5FD), // Blue 300
    onPrimary: Color(0xFF1E3A8A), // Blue 900
    primaryContainer: primaryDark,
    onPrimaryContainer: Color(0xFFDBEAFE), // Blue 100
    secondary: Color(0xFFFDE68A), // Yellow 200
    onSecondary: Color(0xFF92400E), // Yellow 800
    secondaryContainer: Color(0xFFD97706), // Amber 600
    onSecondaryContainer: Color(0xFFFEF3C7), // Yellow 100
    tertiary: Color(0xFFC4B5FD), // Violet 300
    onTertiary: Color(0xFF5B21B6), // Violet 800
    tertiaryContainer: Color(0xFF7C3AED), // Violet 600
    onTertiaryContainer: Color(0xFFEDE9FE), // Violet 100
    error: Color(0xFFF87171), // Red 400
    onError: Color(0xFF7F1D1D), // Red 900
    errorContainer: errorDark,
    onErrorContainer: errorLight,
    background: backgroundDark,
    onBackground: textPrimaryDark,
    surface: surfaceDark,
    onSurface: textPrimaryDark,
    surfaceVariant: neutral700,
    onSurfaceVariant: neutral300,
    outline: neutral600,
    outlineVariant: neutral700,
    shadow: shadowDark,
    scrim: scrim,
    inverseSurface: neutral100,
    onInverseSurface: neutral800,
    inversePrimary: primary,
    surfaceTint: Color(0xFF93C5FD), // Blue 300
  );
  
  // Semantic Color Getters
  static Color getStockColor(double changePercent) {
    if (changePercent > 0) return stockPositive;
    if (changePercent < 0) return stockNegative;
    return stockNeutral;
  }
  
  static Color getLeadScoreColor(int score) {
    if (score >= 80) return success;
    if (score >= 60) return warning;
    return error;
  }
  
  static Color getVoiceStateColor(String state) {
    switch (state) {
      case 'recording':
        return voiceRecording;
      case 'processing':
        return voiceProcessing;
      default:
        return voiceIdle;
    }
  }
}

/// Color utilities
extension ColorExtensions on Color {
  /// Get a lighter version of the color
  Color lighten([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// Get a darker version of the color
  Color darken([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// Get color with opacity
  Color withOpacity(double opacity) {
    return Color.fromRGBO(red, green, blue, opacity);
  }
}
