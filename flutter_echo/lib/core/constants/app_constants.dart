/// Echo Application Constants
/// Copyright (c) 2025 Echo Inc.
/// 
/// Application-wide constants and configuration values.

class AppConstants {
  // App Information
  static const String appName = 'Echo';
  static const String appDisplayName = 'Echo - Voice Investment Platform';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // Company Information
  static const String companyName = 'Echo Inc.';
  static const String companyWebsite = 'https://echo.com';
  static const String supportEmail = '<EMAIL>';
  
  // API Configuration
  static const String apiBaseUrl = 'https://api.shareflix.com';
  static const String apiVersion = 'v1';
  static const String websocketUrl = 'wss://api.shareflix.com/ws';
  
  // Development URLs
  static const String devApiBaseUrl = 'http://localhost:3000';
  static const String devWebsocketUrl = 'ws://localhost:3000/ws';
  
  // Authentication
  static const String accessPassword = 'echo2025';
  static const Duration sessionTimeout = Duration(hours: 24);
  static const Duration accessTokenExpiry = Duration(hours: 24);
  
  // Voice Configuration
  static const int voiceRecordingSampleRate = 16000;
  static const int voiceRecordingBitRate = 128000; // 128 kbps
  static const String voiceRecordingFormat = 'wav';
  static const Duration maxVoiceRecordingDuration = Duration(minutes: 5);
  static const Duration minVoiceRecordingDuration = Duration(seconds: 1);

  // Voice Activity Detection
  static const double vadSilenceThreshold = -40.0; // dB
  static const double vadVoiceThreshold = -30.0; // dB
  static const Duration vadSilenceTimeout = Duration(seconds: 2);
  static const Duration vadVoiceMinDuration = Duration(milliseconds: 500);

  // Voice Streaming
  static const int voiceStreamingChunkSize = 4096; // bytes
  static const Duration voiceStreamingInterval = Duration(milliseconds: 100);

  // Audio Settings
  static const double defaultVolume = 0.8;
  static const String ttsVoice = 'alloy';
  static const double ttsSpeechRate = 1.0;

  // OpenAI API Configuration
  static const String openaiApiKey = String.fromEnvironment('OPENAI_API_KEY', defaultValue: '');
  static const String openaiApiUrl = 'https://api.openai.com/v1';
  static const Duration openaiTimeout = Duration(minutes: 2);
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Layout
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Typography
  static const String fontFamily = 'Inter';
  static const double baseFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double largeFontSize = 16.0;
  static const double headingFontSize = 24.0;
  
  // Performance Targets
  static const Duration coldStartTarget = Duration(seconds: 3);
  static const Duration voiceLatencyTarget = Duration(milliseconds: 200);
  static const int targetFps = 60;
  static const int maxMemoryUsageMB = 100;
  static const Duration aiResponseTarget = Duration(seconds: 5);
  
  // Storage Configuration
  static const int maxConversations = 1000;
  static const int maxVoiceRecordings = 100;
  static const int maxStorageSizeMB = 500;
  static const Duration dataRetentionPeriod = Duration(days: 90);
  
  // Network Configuration
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Security Configuration
  static const int maxAuthFailures = 5;
  static const Duration authLockoutDuration = Duration(minutes: 30);
  static const Duration biometricTimeout = Duration(seconds: 30);
  
  // Conversation Configuration
  static const int maxMessageLength = 1000;
  static const int conversationHistoryLimit = 50;
  static const int maxStockMentions = 10;
  
  // Lead Qualification
  static const int minQualificationScore = 50;
  static const int maxQualificationScore = 100;
  static const List<String> investmentExperienceLevels = [
    'beginner',
    'intermediate',
    'advanced',
    'professional'
  ];
  
  // Stock Detection
  static const double minStockConfidence = 0.7;
  static const int maxStockResults = 5;
  static const List<String> supportedExchanges = [
    'NYSE',
    'NASDAQ',
    'AMEX'
  ];
  
  // Error Messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String authErrorMessage = 'Authentication failed. Please try again.';
  static const String voiceErrorMessage = 'Voice recording failed. Please check microphone permissions.';
  static const String storageErrorMessage = 'Storage error. Please check available space.';
  
  // Success Messages
  static const String conversationSavedMessage = 'Conversation saved successfully';
  static const String voiceRecordedMessage = 'Voice message recorded';
  static const String settingsUpdatedMessage = 'Settings updated successfully';
  
  // Feature Flags
  static const bool enableVoiceFeatures = true;
  static const bool enableBiometricAuth = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  
  // Debug Configuration
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceMonitoring = true;
  static const bool enableNetworkLogging = false;
  
  // Platform-specific
  static const String iosAppStoreId = '1234567890';
  static const String androidPackageName = 'com.echo.app';
  static const String iosUrlScheme = 'echo';
  static const String androidDeepLinkHost = 'echo.com';
  
  // Accessibility
  static const Duration accessibilityTimeout = Duration(seconds: 5);
  static const double minTouchTargetSize = 44.0;
  static const double accessibilityFontScale = 1.2;
  
  // Localization
  static const List<String> supportedLanguages = ['en'];
  static const String defaultLanguage = 'en';
  static const String defaultCountry = 'US';
  
  // Cache Configuration
  static const Duration cacheExpiry = Duration(hours: 1);
  static const int maxCacheSize = 50; // Number of items
  static const Duration imageCacheExpiry = Duration(days: 7);
  
  // WebSocket Configuration
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const int maxReconnectAttempts = 5;
  
  // Notification Configuration
  static const String notificationChannelId = 'echo_notifications';
  static const String notificationChannelName = 'Echo Notifications';
  static const String notificationChannelDescription = 'Notifications from Echo app';
  
  // Analytics Events
  static const String eventAppLaunched = 'app_launched';
  static const String eventConversationStarted = 'conversation_started';
  static const String eventVoiceRecorded = 'voice_recorded';
  static const String eventStockDetected = 'stock_detected';
  static const String eventLeadQualified = 'lead_qualified';
  static const String eventErrorOccurred = 'error_occurred';
  
  // Regex Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  static const String stockTickerPattern = r'^[A-Z]{1,5}$';
  
  // File Extensions
  static const List<String> supportedAudioFormats = ['wav', 'mp3', 'm4a'];
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Limits
  static const int maxFileUploadSizeMB = 10;
  static const int maxBatchSize = 100;
  static const int maxSearchResults = 50;
  
  // URLs
  static const String privacyPolicyUrl = 'https://echo.com/privacy';
  static const String termsOfServiceUrl = 'https://echo.com/terms';
  static const String helpUrl = 'https://echo.com/help';
  static const String feedbackUrl = 'https://echo.com/feedback';
  
  // Environment Detection
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get isDevelopment => !isProduction;
  
  // API Endpoints
  static String get baseApiUrl => isDevelopment ? devApiBaseUrl : apiBaseUrl;
  static String get baseWebsocketUrl => isDevelopment ? devWebsocketUrl : websocketUrl;
}

/// Environment-specific configuration
class EnvironmentConfig {
  static const String environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  
  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';
  
  static String get apiBaseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.shareflix.com';
      case 'staging':
        return 'https://staging-api.shareflix.com';
      default:
        return 'http://localhost:3000';
    }
  }
  
  static String get websocketUrl {
    switch (environment) {
      case 'production':
        return 'wss://api.shareflix.com/ws';
      case 'staging':
        return 'wss://staging-api.shareflix.com/ws';
      default:
        return 'ws://localhost:3000/ws';
    }
  }
}
