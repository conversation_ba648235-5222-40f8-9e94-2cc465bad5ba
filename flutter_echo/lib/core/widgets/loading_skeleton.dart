/// Loading Skeleton Widgets
/// Copyright (c) 2025 Echo Inc.
/// 
/// Skeleton loading widgets for improved perceived performance and user experience.

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Base skeleton widget with shimmer animation
class SkeletonWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;

  const SkeletonWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Shimmer.fromColors(
      baseColor: baseColor ?? (isDark ? Colors.grey[800]! : Colors.grey[300]!),
      highlightColor: highlightColor ?? (isDark ? Colors.grey[700]! : Colors.grey[100]!),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: baseColor ?? (isDark ? Colors.grey[800]! : Colors.grey[300]!),
          borderRadius: borderRadius ?? BorderRadius.circular(4.0),
        ),
      ),
    );
  }
}

/// Skeleton for text lines
class TextLineSkeleton extends StatelessWidget {
  final double? width;
  final double height;
  final int lines;
  final double spacing;

  const TextLineSkeleton({
    super.key,
    this.width,
    this.height = 16.0,
    this.lines = 1,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(lines, (index) {
        final isLast = index == lines - 1;
        final lineWidth = isLast && lines > 1 
            ? (width ?? double.infinity) * 0.7 
            : width;
        
        return Padding(
          padding: EdgeInsets.only(bottom: isLast ? 0 : spacing),
          child: SkeletonWidget(
            width: lineWidth,
            height: height,
            borderRadius: BorderRadius.circular(height / 2),
          ),
        );
      }),
    );
  }
}

/// Skeleton for circular avatars
class CircleAvatarSkeleton extends StatelessWidget {
  final double radius;

  const CircleAvatarSkeleton({
    super.key,
    this.radius = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonWidget(
      width: radius * 2,
      height: radius * 2,
      borderRadius: BorderRadius.circular(radius),
    );
  }
}

/// Skeleton for stock preview cards
class StockCardSkeleton extends StatelessWidget {
  const StockCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const SkeletonWidget(width: 60, height: 20), // Ticker
                const Spacer(),
                const SkeletonWidget(width: 24, height: 24, borderRadius: BorderRadius.all(Radius.circular(12))), // Favorite icon
              ],
            ),
            const SizedBox(height: 8),
            const TextLineSkeleton(width: 120, height: 14), // Company name
            const SizedBox(height: 12),
            Row(
              children: [
                const SkeletonWidget(width: 80, height: 24), // Price
                const SizedBox(width: 12),
                const SkeletonWidget(width: 60, height: 20), // Change %
              ],
            ),
            const SizedBox(height: 12),
            const TextLineSkeleton(width: 100, height: 12), // Sector
            const SizedBox(height: 8),
            const SkeletonWidget(width: 50, height: 20, borderRadius: BorderRadius.all(Radius.circular(10))), // Rating chip
          ],
        ),
      ),
    );
  }
}

/// Skeleton for conversation messages
class MessageSkeleton extends StatelessWidget {
  final bool isUser;

  const MessageSkeleton({
    super.key,
    this.isUser = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isUser) ...[
            const CircleAvatarSkeleton(radius: 16),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: const TextLineSkeleton(
                lines: 2,
                height: 14,
                spacing: 6,
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            const CircleAvatarSkeleton(radius: 16),
          ],
        ],
      ),
    );
  }
}

/// Skeleton for lead analytics dashboard
class LeadAnalyticsSkeleton extends StatelessWidget {
  const LeadAnalyticsSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const TextLineSkeleton(width: 200, height: 24),
          const SizedBox(height: 24),
          
          // Metrics grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: List.generate(4, (index) => 
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SkeletonWidget(width: 24, height: 24), // Icon
                      const SizedBox(height: 8),
                      const TextLineSkeleton(width: 60, height: 20), // Value
                      const SizedBox(height: 4),
                      const TextLineSkeleton(width: 80, height: 12), // Label
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Chart area
          const SkeletonWidget(
            width: double.infinity,
            height: 200,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          
          const SizedBox(height: 24),
          
          // Recent leads list
          const TextLineSkeleton(width: 150, height: 18),
          const SizedBox(height: 16),
          
          ...List.generate(3, (index) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                children: [
                  const CircleAvatarSkeleton(radius: 20),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextLineSkeleton(width: 120, height: 14),
                        SizedBox(height: 4),
                        TextLineSkeleton(width: 80, height: 12),
                      ],
                    ),
                  ),
                  const SkeletonWidget(width: 60, height: 20), // Score
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton for voice recording interface
class VoiceRecordingSkeleton extends StatelessWidget {
  const VoiceRecordingSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Microphone button skeleton
          const SkeletonWidget(
            width: 80,
            height: 80,
            borderRadius: BorderRadius.all(Radius.circular(40)),
          ),
          const SizedBox(height: 24),
          
          // Status text skeleton
          const TextLineSkeleton(width: 120, height: 16),
          const SizedBox(height: 16),
          
          // Amplitude visualization skeleton
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(7, (index) => 
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2.0),
                child: SkeletonWidget(
                  width: 4,
                  height: 20 + (index % 3) * 10,
                  borderRadius: const BorderRadius.all(Radius.circular(2)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton for stock detail screen
class StockDetailSkeleton extends StatelessWidget {
  const StockDetailSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextLineSkeleton(width: 80, height: 24), // Ticker
                    SizedBox(height: 8),
                    TextLineSkeleton(width: 150, height: 16), // Company name
                  ],
                ),
              ),
              const SkeletonWidget(width: 24, height: 24), // Favorite icon
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Price section
          const TextLineSkeleton(width: 120, height: 32), // Price
          const SizedBox(height: 8),
          const TextLineSkeleton(width: 80, height: 16), // Change
          
          const SizedBox(height: 32),
          
          // Chart skeleton
          const SkeletonWidget(
            width: double.infinity,
            height: 200,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          
          const SizedBox(height: 32),
          
          // Metrics section
          const TextLineSkeleton(width: 100, height: 20), // Section title
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 2,
            children: List.generate(6, (index) => 
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const TextLineSkeleton(width: 80, height: 12), // Label
                  const SizedBox(height: 4),
                  const TextLineSkeleton(width: 60, height: 16), // Value
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Investment thesis section
          const TextLineSkeleton(width: 150, height: 20), // Section title
          const SizedBox(height: 16),
          const TextLineSkeleton(lines: 4, height: 14, spacing: 8),
        ],
      ),
    );
  }
}

/// Skeleton list builder for dynamic content
class SkeletonListView extends StatelessWidget {
  final Widget skeletonItem;
  final int itemCount;
  final EdgeInsetsGeometry? padding;
  final double? itemSpacing;

  const SkeletonListView({
    super.key,
    required this.skeletonItem,
    this.itemCount = 5,
    this.padding,
    this.itemSpacing,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: padding,
      itemCount: itemCount,
      separatorBuilder: (context, index) => SizedBox(height: itemSpacing ?? 0),
      itemBuilder: (context, index) => skeletonItem,
    );
  }
}

/// Skeleton grid builder for dynamic content
class SkeletonGridView extends StatelessWidget {
  final Widget skeletonItem;
  final int itemCount;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  const SkeletonGridView({
    super.key,
    required this.skeletonItem,
    this.itemCount = 6,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 16.0,
    this.mainAxisSpacing = 16.0,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      padding: padding,
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      childAspectRatio: childAspectRatio,
      children: List.generate(itemCount, (index) => skeletonItem),
    );
  }
}
