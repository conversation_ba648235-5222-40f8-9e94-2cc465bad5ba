/// Error Handling Widgets
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive error widgets with recovery actions and user-friendly messaging.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Base error widget with customizable content and actions
class ErrorWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final List<ErrorAction>? actions;
  final Widget? illustration;
  final bool showDetails;
  final String? details;
  final VoidCallback? onRetry;

  const ErrorWidget({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.actions,
    this.illustration,
    this.showDetails = false,
    this.details,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration or icon
            if (illustration != null) 
              illustration!
            else if (icon != null)
              Icon(
                icon!,
                size: 64,
                color: theme.colorScheme.error,
              ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Message
            Text(
              message,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            
            // Details (expandable)
            if (showDetails && details != null) ...[
              const SizedBox(height: 16),
              ExpansionTile(
                title: const Text('Technical Details'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: SelectableText(
                      details!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Actions
            if (actions != null && actions!.isNotEmpty) ...[
              Wrap(
                spacing: 12.0,
                runSpacing: 8.0,
                alignment: WrapAlignment.center,
                children: actions!.map((action) => _buildActionButton(context, action)).toList(),
              ),
            ] else if (onRetry != null) ...[
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, ErrorAction action) {
    switch (action.type) {
      case ErrorActionType.primary:
        return ElevatedButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
      case ErrorActionType.secondary:
        return OutlinedButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
      case ErrorActionType.text:
        return TextButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
    }
  }
}

/// Network error widget with specific messaging and actions
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onGoOffline;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
    this.onGoOffline,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorWidget(
      title: 'Connection Problem',
      message: 'Please check your internet connection and try again.',
      icon: Icons.wifi_off,
      actions: [
        ErrorAction(
          type: ErrorActionType.primary,
          label: 'Try Again',
          icon: Icons.refresh,
          onPressed: onRetry,
        ),
        if (onGoOffline != null)
          ErrorAction(
            type: ErrorActionType.secondary,
            label: 'Go Offline',
            icon: Icons.cloud_off,
            onPressed: onGoOffline,
          ),
      ],
    );
  }
}

/// Permission error widget for microphone/camera access
class PermissionErrorWidget extends StatelessWidget {
  final String permissionType;
  final VoidCallback? onOpenSettings;
  final VoidCallback? onSkip;

  const PermissionErrorWidget({
    super.key,
    required this.permissionType,
    this.onOpenSettings,
    this.onSkip,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorWidget(
      title: '$permissionType Permission Required',
      message: 'Echo needs access to your $permissionType to provide voice features. Please grant permission in settings.',
      icon: Icons.mic_off,
      actions: [
        ErrorAction(
          type: ErrorActionType.primary,
          label: 'Open Settings',
          icon: Icons.settings,
          onPressed: onOpenSettings,
        ),
        if (onSkip != null)
          ErrorAction(
            type: ErrorActionType.text,
            label: 'Skip for Now',
            onPressed: onSkip,
          ),
      ],
    );
  }
}

/// Empty state widget for when no data is available
class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final Widget? illustration;
  final List<ErrorAction>? actions;

  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.illustration,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration or icon
            if (illustration != null) 
              illustration!
            else if (icon != null)
              Icon(
                icon!,
                size: 64,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Message
            Text(
              message,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            
            // Actions
            if (actions != null && actions!.isNotEmpty) ...[
              const SizedBox(height: 24),
              Wrap(
                spacing: 12.0,
                runSpacing: 8.0,
                alignment: WrapAlignment.center,
                children: actions!.map((action) => _buildActionButton(context, action)).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, ErrorAction action) {
    switch (action.type) {
      case ErrorActionType.primary:
        return ElevatedButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
      case ErrorActionType.secondary:
        return OutlinedButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
      case ErrorActionType.text:
        return TextButton.icon(
          onPressed: action.onPressed,
          icon: action.icon != null ? Icon(action.icon) : null,
          label: Text(action.label),
        );
    }
  }
}

/// Specific empty states for different features
class EmptyConversationsWidget extends StatelessWidget {
  final VoidCallback? onStartConversation;

  const EmptyConversationsWidget({
    super.key,
    this.onStartConversation,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Conversations Yet',
      message: 'Start your first conversation to discover investment opportunities.',
      icon: Icons.chat_bubble_outline,
      actions: onStartConversation != null ? [
        ErrorAction(
          type: ErrorActionType.primary,
          label: 'Start Conversation',
          icon: Icons.add,
          onPressed: onStartConversation,
        ),
      ] : null,
    );
  }
}

class EmptyStocksWidget extends StatelessWidget {
  final VoidCallback? onExploreStocks;

  const EmptyStocksWidget({
    super.key,
    this.onExploreStocks,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Stocks Found',
      message: 'Try adjusting your search or explore trending stocks.',
      icon: Icons.trending_up,
      actions: onExploreStocks != null ? [
        ErrorAction(
          type: ErrorActionType.primary,
          label: 'Explore Stocks',
          icon: Icons.explore,
          onPressed: onExploreStocks,
        ),
      ] : null,
    );
  }
}

class EmptyLeadsWidget extends StatelessWidget {
  final VoidCallback? onAddLead;

  const EmptyLeadsWidget({
    super.key,
    this.onAddLead,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Leads Yet',
      message: 'Start conversations to generate qualified investment leads.',
      icon: Icons.people_outline,
      actions: onAddLead != null ? [
        ErrorAction(
          type: ErrorActionType.primary,
          label: 'Start Conversation',
          icon: Icons.add,
          onPressed: onAddLead,
        ),
      ] : null,
    );
  }
}

/// Error boundary widget for catching and displaying errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      
      return ErrorWidget(
        title: 'Something Went Wrong',
        message: 'An unexpected error occurred. Please try again.',
        icon: Icons.error_outline,
        showDetails: true,
        details: _error.toString(),
        onRetry: () {
          setState(() {
            _error = null;
            _stackTrace = null;
          });
        },
      );
    }

    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    FlutterError.onError = (FlutterErrorDetails details) {
      if (mounted) {
        setState(() {
          _error = details.exception;
          _stackTrace = details.stack;
        });
        
        widget.onError?.call(details.exception, details.stack);
      }
    };
  }
}

/// Snackbar helper for showing error messages
class ErrorSnackBar {
  static void show(
    BuildContext context, {
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                onPressed: onAction,
              )
            : null,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  static void showSuccess(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        duration: duration,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green,
      ),
    );
  }

  static void showWarning(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        duration: duration,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// Error action definition
class ErrorAction {
  final ErrorActionType type;
  final String label;
  final IconData? icon;
  final VoidCallback? onPressed;

  const ErrorAction({
    required this.type,
    required this.label,
    this.icon,
    this.onPressed,
  });
}

/// Types of error actions
enum ErrorActionType {
  primary,
  secondary,
  text,
}
