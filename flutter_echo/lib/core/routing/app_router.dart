/// Echo Application Router
/// Copyright (c) 2025 Echo Inc.
/// 
/// GoRouter configuration for navigation and route management.

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/app_providers.dart';
import '../../features/auth/presentation/screens/access_control_screen.dart';
import '../../features/auth/presentation/screens/auth_screen.dart';
import '../../features/chat/presentation/screens/chat_screen.dart';
import '../../features/history/presentation/screens/conversation_history_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';
import '../../shared/widgets/error_screen.dart';
import '../../shared/widgets/loading_screen.dart';

/// App router configuration
class AppRouter {
  static const String accessRoute = '/access';
  static const String authRoute = '/auth';
  static const String homeRoute = '/';
  static const String chatRoute = '/chat';
  static const String historyRoute = '/history';
  static const String settingsRoute = '/settings';
  
  /// Create router with authentication guards
  static GoRouter createRouter(WidgetRef ref) {
    return GoRouter(
      initialLocation: homeRoute,
      debugLogDiagnostics: true,
      
      // Global redirect logic
      redirect: (BuildContext context, GoRouterState state) {
        final sessionState = ref.read(sessionStateProvider);
        final location = state.location;
        
        // Check access permission first
        if (!sessionState.hasAccessPermission && location != accessRoute) {
          return accessRoute;
        }
        
        // Check authentication for protected routes
        if (!sessionState.isAuthenticated && 
            location != accessRoute && 
            location != authRoute) {
          return authRoute;
        }
        
        // If authenticated and trying to access auth pages, redirect to home
        if (sessionState.isAuthenticated && 
            (location == accessRoute || location == authRoute)) {
          return homeRoute;
        }
        
        return null; // No redirect needed
      },
      
      // Route definitions
      routes: [
        // Access control route
        GoRoute(
          path: accessRoute,
          name: 'access',
          builder: (context, state) => const AccessControlScreen(),
        ),
        
        // Authentication route
        GoRoute(
          path: authRoute,
          name: 'auth',
          builder: (context, state) => const AuthScreen(),
        ),
        
        // Main app routes
        ShellRoute(
          builder: (context, state, child) {
            return MainShell(child: child);
          },
          routes: [
            // Home/Chat route
            GoRoute(
              path: homeRoute,
              name: 'home',
              builder: (context, state) => const ChatScreen(),
            ),
            
            // Chat route (same as home)
            GoRoute(
              path: chatRoute,
              name: 'chat',
              builder: (context, state) => const ChatScreen(),
            ),
            
            // Conversation history route
            GoRoute(
              path: historyRoute,
              name: 'history',
              builder: (context, state) => const ConversationHistoryScreen(),
            ),
            
            // Settings route
            GoRoute(
              path: settingsRoute,
              name: 'settings',
              builder: (context, state) => const SettingsScreen(),
            ),
          ],
        ),
      ],
      
      // Error handling
      errorBuilder: (context, state) => ErrorScreen(
        error: state.error?.toString() ?? 'Unknown error',
        onRetry: () => context.go(homeRoute),
      ),
      
      // Loading builder for async routes
      // Note: This is a custom extension, not part of standard GoRouter
      // You would implement this if needed for your specific use case
    );
  }
}

/// Main shell widget with bottom navigation
class MainShell extends ConsumerWidget {
  final Widget child;
  
  const MainShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: child,
      bottomNavigationBar: const MainBottomNavigation(),
    );
  }
}

/// Bottom navigation bar
class MainBottomNavigation extends ConsumerWidget {
  const MainBottomNavigation({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final location = GoRouterState.of(context).location;
    
    int currentIndex = 0;
    switch (location) {
      case AppRouter.homeRoute:
      case AppRouter.chatRoute:
        currentIndex = 0;
        break;
      case AppRouter.historyRoute:
        currentIndex = 1;
        break;
      case AppRouter.settingsRoute:
        currentIndex = 2;
        break;
    }

    return BottomNavigationBar(
      currentIndex: currentIndex,
      type: BottomNavigationBarType.fixed,
      onTap: (index) {
        switch (index) {
          case 0:
            context.go(AppRouter.homeRoute);
            break;
          case 1:
            context.go(AppRouter.historyRoute);
            break;
          case 2:
            context.go(AppRouter.settingsRoute);
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          activeIcon: Icon(Icons.chat_bubble),
          label: 'Chat',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.history_outlined),
          activeIcon: Icon(Icons.history),
          label: 'History',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings_outlined),
          activeIcon: Icon(Icons.settings),
          label: 'Settings',
        ),
      ],
    );
  }
}

/// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  return AppRouter.createRouter(ref);
});

/// Navigation service for programmatic navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  static BuildContext? get context => navigatorKey.currentContext;
  
  static void goToAccess() {
    if (context != null) {
      context!.go(AppRouter.accessRoute);
    }
  }
  
  static void goToAuth() {
    if (context != null) {
      context!.go(AppRouter.authRoute);
    }
  }
  
  static void goToHome() {
    if (context != null) {
      context!.go(AppRouter.homeRoute);
    }
  }
  
  static void goToChat() {
    if (context != null) {
      context!.go(AppRouter.chatRoute);
    }
  }
  
  static void goToHistory() {
    if (context != null) {
      context!.go(AppRouter.historyRoute);
    }
  }
  
  static void goToSettings() {
    if (context != null) {
      context!.go(AppRouter.settingsRoute);
    }
  }
  
  static void goBack() {
    if (context != null && Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop();
    }
  }
  
  static Future<T?> showDialog<T>({
    required Widget dialog,
    bool barrierDismissible = true,
  }) {
    if (context == null) return Future.value(null);
    
    return showDialog<T>(
      context: context!,
      barrierDismissible: barrierDismissible,
      builder: (context) => dialog,
    );
  }
  
  static void showSnackBar({
    required String message,
    SnackBarAction? action,
    Duration duration = const Duration(seconds: 4),
  }) {
    if (context == null) return;
    
    ScaffoldMessenger.of(context!).showSnackBar(
      SnackBar(
        content: Text(message),
        action: action,
        duration: duration,
      ),
    );
  }
  
  static void showBottomSheet({
    required Widget content,
    bool isScrollControlled = false,
  }) {
    if (context == null) return;
    
    showModalBottomSheet(
      context: context!,
      isScrollControlled: isScrollControlled,
      builder: (context) => content,
    );
  }
}

/// Route information model
class RouteInfo {
  final String path;
  final String name;
  final String title;
  final IconData icon;
  final IconData activeIcon;
  final bool requiresAuth;
  final bool showInNavigation;

  const RouteInfo({
    required this.path,
    required this.name,
    required this.title,
    required this.icon,
    required this.activeIcon,
    this.requiresAuth = true,
    this.showInNavigation = true,
  });
}

/// Available routes configuration
class AppRoutes {
  static const List<RouteInfo> routes = [
    RouteInfo(
      path: AppRouter.accessRoute,
      name: 'access',
      title: 'Access Control',
      icon: Icons.lock_outline,
      activeIcon: Icons.lock,
      requiresAuth: false,
      showInNavigation: false,
    ),
    RouteInfo(
      path: AppRouter.authRoute,
      name: 'auth',
      title: 'Authentication',
      icon: Icons.login_outlined,
      activeIcon: Icons.login,
      requiresAuth: false,
      showInNavigation: false,
    ),
    RouteInfo(
      path: AppRouter.homeRoute,
      name: 'home',
      title: 'Chat',
      icon: Icons.chat_bubble_outline,
      activeIcon: Icons.chat_bubble,
    ),
    RouteInfo(
      path: AppRouter.historyRoute,
      name: 'history',
      title: 'History',
      icon: Icons.history_outlined,
      activeIcon: Icons.history,
    ),
    RouteInfo(
      path: AppRouter.settingsRoute,
      name: 'settings',
      title: 'Settings',
      icon: Icons.settings_outlined,
      activeIcon: Icons.settings,
    ),
  ];
  
  static RouteInfo? getRouteInfo(String path) {
    try {
      return routes.firstWhere((route) => route.path == path);
    } catch (e) {
      return null;
    }
  }
  
  static List<RouteInfo> get navigationRoutes {
    return routes.where((route) => route.showInNavigation).toList();
  }
}
