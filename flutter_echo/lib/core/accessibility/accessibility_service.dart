/// Accessibility Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive accessibility features for screen readers, high contrast, and inclusive design.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';

/// Service for managing accessibility features and inclusive design
class AccessibilityService {
  static AccessibilityService? _instance;
  static AccessibilityService get instance => _instance ??= AccessibilityService._();
  AccessibilityService._();

  bool _isHighContrastEnabled = false;
  bool _isLargeTextEnabled = false;
  bool _isReduceMotionEnabled = false;
  bool _isScreenReaderEnabled = false;
  double _textScaleFactor = 1.0;

  /// Initialize accessibility service and detect system preferences
  Future<void> initialize() async {
    await _detectSystemAccessibilitySettings();
    _setupAccessibilityListeners();
  }

  /// Getters for accessibility states
  bool get isHighContrastEnabled => _isHighContrastEnabled;
  bool get isLargeTextEnabled => _isLargeTextEnabled;
  bool get isReduceMotionEnabled => _isReduceMotionEnabled;
  bool get isScreenReaderEnabled => _isScreenReaderEnabled;
  double get textScaleFactor => _textScaleFactor;

  /// Detect system accessibility settings
  Future<void> _detectSystemAccessibilitySettings() async {
    try {
      // Check for high contrast mode
      _isHighContrastEnabled = await _checkHighContrastMode();
      
      // Check for large text/font scaling
      _textScaleFactor = await _getSystemTextScaleFactor();
      _isLargeTextEnabled = _textScaleFactor > 1.2;
      
      // Check for reduce motion preference
      _isReduceMotionEnabled = await _checkReduceMotionPreference();
      
      // Check for screen reader
      _isScreenReaderEnabled = await _checkScreenReaderEnabled();
      
    } catch (e) {
      debugPrint('Error detecting accessibility settings: $e');
    }
  }

  /// Setup listeners for accessibility changes
  void _setupAccessibilityListeners() {
    // Listen for system accessibility changes
    WidgetsBinding.instance.platformDispatcher.onAccessibilityFeaturesChanged = () {
      _detectSystemAccessibilitySettings();
    };
  }

  /// Check if high contrast mode is enabled
  Future<bool> _checkHighContrastMode() async {
    try {
      final result = await const MethodChannel('echo/accessibility')
          .invokeMethod<bool>('isHighContrastEnabled');
      return result ?? false;
    } catch (e) {
      // Fallback: check MediaQuery for high contrast
      return false; // Will be set by MediaQuery in widget
    }
  }

  /// Get system text scale factor
  Future<double> _getSystemTextScaleFactor() async {
    try {
      final result = await const MethodChannel('echo/accessibility')
          .invokeMethod<double>('getTextScaleFactor');
      return result ?? 1.0;
    } catch (e) {
      return 1.0; // Will be set by MediaQuery in widget
    }
  }

  /// Check if reduce motion is enabled
  Future<bool> _checkReduceMotionPreference() async {
    try {
      final result = await const MethodChannel('echo/accessibility')
          .invokeMethod<bool>('isReduceMotionEnabled');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Check if screen reader is enabled
  Future<bool> _checkScreenReaderEnabled() async {
    try {
      final result = await const MethodChannel('echo/accessibility')
          .invokeMethod<bool>('isScreenReaderEnabled');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Update accessibility settings from MediaQuery
  void updateFromMediaQuery(MediaQueryData mediaQuery) {
    _isHighContrastEnabled = mediaQuery.highContrast;
    _textScaleFactor = mediaQuery.textScaleFactor;
    _isLargeTextEnabled = _textScaleFactor > 1.2;
    _isReduceMotionEnabled = mediaQuery.disableAnimations;
    _isScreenReaderEnabled = mediaQuery.accessibleNavigation;
  }

  /// Get accessible color scheme
  ColorScheme getAccessibleColorScheme(ColorScheme baseScheme) {
    if (!_isHighContrastEnabled) return baseScheme;

    return ColorScheme.fromSeed(
      seedColor: baseScheme.primary,
      brightness: baseScheme.brightness,
    ).copyWith(
      // High contrast adjustments
      primary: _adjustColorForContrast(baseScheme.primary, baseScheme.surface),
      onPrimary: _adjustColorForContrast(baseScheme.onPrimary, baseScheme.primary),
      secondary: _adjustColorForContrast(baseScheme.secondary, baseScheme.surface),
      onSecondary: _adjustColorForContrast(baseScheme.onSecondary, baseScheme.secondary),
      surface: baseScheme.brightness == Brightness.dark ? Colors.black : Colors.white,
      onSurface: baseScheme.brightness == Brightness.dark ? Colors.white : Colors.black,
      outline: baseScheme.brightness == Brightness.dark ? Colors.white70 : Colors.black87,
    );
  }

  /// Adjust color for better contrast
  Color _adjustColorForContrast(Color foreground, Color background) {
    final contrast = _calculateContrast(foreground, background);
    if (contrast >= 4.5) return foreground; // WCAG AA compliant

    // Adjust color to meet contrast requirements
    if (background.computeLuminance() > 0.5) {
      // Light background - darken foreground
      return Color.lerp(foreground, Colors.black, 0.3) ?? foreground;
    } else {
      // Dark background - lighten foreground
      return Color.lerp(foreground, Colors.white, 0.3) ?? foreground;
    }
  }

  /// Calculate contrast ratio between two colors
  double _calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Get accessible text theme
  TextTheme getAccessibleTextTheme(TextTheme baseTheme) {
    final scaleFactor = _isLargeTextEnabled ? _textScaleFactor : 1.0;
    
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 57) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: (baseTheme.displayMedium?.fontSize ?? 45) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 36) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize: (baseTheme.headlineLarge?.fontSize ?? 32) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: (baseTheme.headlineMedium?.fontSize ?? 28) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize: (baseTheme.headlineSmall?.fontSize ?? 24) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w400,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontSize: (baseTheme.titleLarge?.fontSize ?? 22) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontSize: (baseTheme.titleMedium?.fontSize ?? 16) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontSize: (baseTheme.titleSmall?.fontSize ?? 14) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: (baseTheme.bodyLarge?.fontSize ?? 16) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w500 : FontWeight.w400,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: (baseTheme.bodyMedium?.fontSize ?? 14) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w500 : FontWeight.w400,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: (baseTheme.bodySmall?.fontSize ?? 12) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w500 : FontWeight.w400,
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontSize: (baseTheme.labelLarge?.fontSize ?? 14) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontSize: (baseTheme.labelMedium?.fontSize ?? 12) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontSize: (baseTheme.labelSmall?.fontSize ?? 11) * scaleFactor,
        fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
      ),
    );
  }

  /// Get animation duration based on reduce motion preference
  Duration getAnimationDuration(Duration defaultDuration) {
    if (_isReduceMotionEnabled) {
      return Duration.zero; // Disable animations
    }
    return defaultDuration;
  }

  /// Get animation curve based on accessibility preferences
  Curve getAnimationCurve(Curve defaultCurve) {
    if (_isReduceMotionEnabled) {
      return Curves.linear; // Simple linear transitions
    }
    return defaultCurve;
  }

  /// Announce text to screen reader
  void announceToScreenReader(String message, {
    Assertiveness assertiveness = Assertiveness.polite,
  }) {
    if (!_isScreenReaderEnabled) return;

    SemanticsService.announce(
      message,
      TextDirection.ltr,
      assertiveness: assertiveness,
    );
  }

  /// Create semantic label for complex widgets
  String createSemanticLabel({
    required String baseLabel,
    String? state,
    String? value,
    String? hint,
  }) {
    final parts = <String>[baseLabel];
    
    if (state != null) parts.add(state);
    if (value != null) parts.add(value);
    if (hint != null) parts.add(hint);
    
    return parts.join(', ');
  }

  /// Get minimum touch target size
  Size getMinimumTouchTargetSize() {
    return const Size(48.0, 48.0); // WCAG recommended minimum
  }

  /// Check if touch target meets accessibility requirements
  bool isTouchTargetAccessible(Size targetSize) {
    final minSize = getMinimumTouchTargetSize();
    return targetSize.width >= minSize.width && targetSize.height >= minSize.height;
  }

  /// Get accessible button style
  ButtonStyle getAccessibleButtonStyle(ButtonStyle baseStyle) {
    return baseStyle.copyWith(
      minimumSize: MaterialStateProperty.all(getMinimumTouchTargetSize()),
      padding: MaterialStateProperty.all(
        const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      ),
      textStyle: MaterialStateProperty.all(
        TextStyle(
          fontSize: 16.0 * _textScaleFactor,
          fontWeight: _isHighContrastEnabled ? FontWeight.w600 : FontWeight.w500,
        ),
      ),
    );
  }

  /// Get accessible input decoration
  InputDecoration getAccessibleInputDecoration(InputDecoration baseDecoration) {
    return baseDecoration.copyWith(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      border: _isHighContrastEnabled 
          ? const OutlineInputBorder(
              borderSide: BorderSide(width: 2.0),
            )
          : baseDecoration.border,
      focusedBorder: _isHighContrastEnabled
          ? const OutlineInputBorder(
              borderSide: BorderSide(width: 3.0),
            )
          : baseDecoration.focusedBorder,
    );
  }

  /// Create accessible focus node with enhanced behavior
  FocusNode createAccessibleFocusNode({
    String? debugLabel,
    bool skipTraversal = false,
  }) {
    return FocusNode(
      debugLabel: debugLabel,
      skipTraversal: skipTraversal,
      descendantsAreFocusable: true,
      descendantsAreTraversable: true,
    );
  }

  /// Dispose of accessibility service
  void dispose() {
    // Clean up any resources
  }
}

/// Assertiveness levels for screen reader announcements
enum Assertiveness {
  polite,
  assertive,
}

/// Extension for assertiveness conversion
extension AssertivenesExtension on Assertiveness {
  Assertiveness get toSemanticsAssertiveness {
    switch (this) {
      case Assertiveness.polite:
        return Assertiveness.polite;
      case Assertiveness.assertive:
        return Assertiveness.assertive;
    }
  }
}
