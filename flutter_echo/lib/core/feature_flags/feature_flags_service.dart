/// Feature Flags Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Feature flags for gradual rollout and A/B testing capabilities.

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

/// Service for managing feature flags and A/B testing
class FeatureFlagsService {
  static FeatureFlagsService? _instance;
  static FeatureFlagsService get instance => _instance ??= FeatureFlagsService._();
  FeatureFlagsService._();

  bool _isInitialized = false;
  Map<String, dynamic> _flags = {};
  Map<String, dynamic> _defaultFlags = {};
  String? _userId;
  Map<String, dynamic> _userAttributes = {};

  /// Initialize feature flags service
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userAttributes,
    Map<String, dynamic>? defaultFlags,
  }) async {
    try {
      _userId = userId;
      _userAttributes = userAttributes ?? {};
      _defaultFlags = defaultFlags ?? _getDefaultFlags();
      
      // Load flags from remote service
      await _loadRemoteFlags();
      
      // Load cached flags as fallback
      await _loadCachedFlags();
      
      _isInitialized = true;
      
    } catch (e) {
      debugPrint('Error initializing feature flags: $e');
      _flags = Map<String, dynamic>.from(_defaultFlags);
      _isInitialized = true;
    }
  }

  /// Load feature flags from remote service
  Future<void> _loadRemoteFlags() async {
    try {
      final result = await const MethodChannel('echo/feature_flags')
          .invokeMethod<String>('getRemoteFlags', {
            'userId': _userId,
            'userAttributes': _userAttributes,
          });
      
      if (result != null) {
        final remoteFlags = jsonDecode(result) as Map<String, dynamic>;
        _flags.addAll(remoteFlags);
        
        // Cache flags locally
        await _cacheFlags(remoteFlags);
      }
    } catch (e) {
      debugPrint('Error loading remote flags: $e');
    }
  }

  /// Load cached feature flags
  Future<void> _loadCachedFlags() async {
    try {
      final result = await const MethodChannel('echo/feature_flags')
          .invokeMethod<String>('getCachedFlags');
      
      if (result != null) {
        final cachedFlags = jsonDecode(result) as Map<String, dynamic>;
        
        // Only use cached flags if we don't have remote flags
        for (final entry in cachedFlags.entries) {
          if (!_flags.containsKey(entry.key)) {
            _flags[entry.key] = entry.value;
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading cached flags: $e');
    }
  }

  /// Cache feature flags locally
  Future<void> _cacheFlags(Map<String, dynamic> flags) async {
    try {
      await const MethodChannel('echo/feature_flags')
          .invokeMethod('cacheFlags', {
            'flags': jsonEncode(flags),
          });
    } catch (e) {
      debugPrint('Error caching flags: $e');
    }
  }

  /// Get default feature flags
  Map<String, dynamic> _getDefaultFlags() {
    return {
      // Voice features
      'voice_recording_enabled': true,
      'voice_streaming_enabled': true,
      'voice_activity_detection_enabled': true,
      'speech_to_text_enabled': true,
      'text_to_speech_enabled': true,
      
      // Stock features
      'stock_detection_enabled': true,
      'stock_preview_cards_enabled': true,
      'stock_flip_animation_enabled': true,
      'stock_favorites_enabled': true,
      'stock_search_enabled': true,
      'stock_filtering_enabled': true,
      
      // Lead features
      'lead_scoring_enabled': true,
      'lead_analytics_dashboard_enabled': true,
      'lead_export_enabled': true,
      'predefined_prompts_enabled': true,
      
      // UI/UX features
      'haptic_feedback_enabled': true,
      'accessibility_features_enabled': true,
      'high_contrast_mode_enabled': true,
      'reduce_motion_enabled': false,
      'loading_skeletons_enabled': true,
      
      // Performance features
      'lazy_loading_enabled': true,
      'image_caching_enabled': true,
      'data_compression_enabled': true,
      'offline_mode_enabled': true,
      
      // Analytics features
      'analytics_enabled': true,
      'crash_reporting_enabled': true,
      'performance_monitoring_enabled': true,
      
      // Experimental features
      'new_chat_interface_enabled': false,
      'advanced_stock_analysis_enabled': false,
      'ai_powered_recommendations_enabled': false,
      'real_time_collaboration_enabled': false,
      
      // A/B test variants
      'onboarding_flow_variant': 'default', // 'default', 'simplified', 'guided'
      'stock_card_design_variant': 'default', // 'default', 'compact', 'detailed'
      'voice_ui_variant': 'default', // 'default', 'minimal', 'enhanced'
    };
  }

  /// Check if a feature flag is enabled
  bool isEnabled(String flagName) {
    if (!_isInitialized) {
      return _defaultFlags[flagName] ?? false;
    }
    
    return _flags[flagName] ?? _defaultFlags[flagName] ?? false;
  }

  /// Get feature flag value (for non-boolean flags)
  T? getValue<T>(String flagName, [T? defaultValue]) {
    if (!_isInitialized) {
      return _defaultFlags[flagName] ?? defaultValue;
    }
    
    final value = _flags[flagName] ?? _defaultFlags[flagName] ?? defaultValue;
    return value is T ? value : defaultValue;
  }

  /// Get string variant for A/B testing
  String getVariant(String flagName, [String defaultVariant = 'default']) {
    return getValue<String>(flagName, defaultVariant) ?? defaultVariant;
  }

  /// Get numeric value for configuration
  double getNumericValue(String flagName, [double defaultValue = 0.0]) {
    final value = getValue<dynamic>(flagName, defaultValue);
    if (value is num) {
      return value.toDouble();
    }
    return defaultValue;
  }

  /// Update user attributes for flag evaluation
  Future<void> updateUserAttributes(Map<String, dynamic> attributes) async {
    _userAttributes.addAll(attributes);
    
    if (_isInitialized) {
      // Refresh flags with new user attributes
      await _loadRemoteFlags();
    }
  }

  /// Set user ID for flag evaluation
  Future<void> setUserId(String userId) async {
    _userId = userId;
    
    if (_isInitialized) {
      // Refresh flags with new user ID
      await _loadRemoteFlags();
    }
  }

  /// Override flag value locally (for testing)
  void overrideFlag(String flagName, dynamic value) {
    _flags[flagName] = value;
  }

  /// Remove flag override
  void removeOverride(String flagName) {
    if (_flags.containsKey(flagName)) {
      _flags.remove(flagName);
    }
  }

  /// Clear all overrides
  void clearOverrides() {
    _flags.clear();
    _flags.addAll(_defaultFlags);
  }

  /// Refresh flags from remote service
  Future<void> refresh() async {
    if (_isInitialized) {
      await _loadRemoteFlags();
    }
  }

  /// Voice feature flags
  bool get isVoiceRecordingEnabled => isEnabled('voice_recording_enabled');
  bool get isVoiceStreamingEnabled => isEnabled('voice_streaming_enabled');
  bool get isVoiceActivityDetectionEnabled => isEnabled('voice_activity_detection_enabled');
  bool get isSpeechToTextEnabled => isEnabled('speech_to_text_enabled');
  bool get isTextToSpeechEnabled => isEnabled('text_to_speech_enabled');

  /// Stock feature flags
  bool get isStockDetectionEnabled => isEnabled('stock_detection_enabled');
  bool get isStockPreviewCardsEnabled => isEnabled('stock_preview_cards_enabled');
  bool get isStockFlipAnimationEnabled => isEnabled('stock_flip_animation_enabled');
  bool get isStockFavoritesEnabled => isEnabled('stock_favorites_enabled');
  bool get isStockSearchEnabled => isEnabled('stock_search_enabled');
  bool get isStockFilteringEnabled => isEnabled('stock_filtering_enabled');

  /// Lead feature flags
  bool get isLeadScoringEnabled => isEnabled('lead_scoring_enabled');
  bool get isLeadAnalyticsDashboardEnabled => isEnabled('lead_analytics_dashboard_enabled');
  bool get isLeadExportEnabled => isEnabled('lead_export_enabled');
  bool get isPredefinedPromptsEnabled => isEnabled('predefined_prompts_enabled');

  /// UI/UX feature flags
  bool get isHapticFeedbackEnabled => isEnabled('haptic_feedback_enabled');
  bool get isAccessibilityFeaturesEnabled => isEnabled('accessibility_features_enabled');
  bool get isHighContrastModeEnabled => isEnabled('high_contrast_mode_enabled');
  bool get isReduceMotionEnabled => isEnabled('reduce_motion_enabled');
  bool get isLoadingSkeletonsEnabled => isEnabled('loading_skeletons_enabled');

  /// Performance feature flags
  bool get isLazyLoadingEnabled => isEnabled('lazy_loading_enabled');
  bool get isImageCachingEnabled => isEnabled('image_caching_enabled');
  bool get isDataCompressionEnabled => isEnabled('data_compression_enabled');
  bool get isOfflineModeEnabled => isEnabled('offline_mode_enabled');

  /// Analytics feature flags
  bool get isAnalyticsEnabled => isEnabled('analytics_enabled');
  bool get isCrashReportingEnabled => isEnabled('crash_reporting_enabled');
  bool get isPerformanceMonitoringEnabled => isEnabled('performance_monitoring_enabled');

  /// Experimental feature flags
  bool get isNewChatInterfaceEnabled => isEnabled('new_chat_interface_enabled');
  bool get isAdvancedStockAnalysisEnabled => isEnabled('advanced_stock_analysis_enabled');
  bool get isAIPoweredRecommendationsEnabled => isEnabled('ai_powered_recommendations_enabled');
  bool get isRealTimeCollaborationEnabled => isEnabled('real_time_collaboration_enabled');

  /// A/B test variants
  String get onboardingFlowVariant => getVariant('onboarding_flow_variant');
  String get stockCardDesignVariant => getVariant('stock_card_design_variant');
  String get voiceUIVariant => getVariant('voice_ui_variant');

  /// Configuration values
  double get voiceRecordingMaxDuration => getNumericValue('voice_recording_max_duration', 300.0); // 5 minutes
  double get stockDetectionConfidenceThreshold => getNumericValue('stock_detection_confidence_threshold', 0.7);
  double get leadScoringThreshold => getNumericValue('lead_scoring_threshold', 60.0);
  int get maxCachedStocks => getNumericValue('max_cached_stocks', 1000).toInt();
  int get maxConversationHistory => getNumericValue('max_conversation_history', 100).toInt();

  /// Get all flags for debugging
  Map<String, dynamic> getAllFlags() {
    return Map<String, dynamic>.from(_flags);
  }

  /// Get flag evaluation context
  Map<String, dynamic> getEvaluationContext() {
    return {
      'user_id': _userId,
      'user_attributes': _userAttributes,
      'platform': defaultTargetPlatform.name,
      'app_version': '1.0.0', // Would be dynamic in real app
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Track flag evaluation for analytics
  void trackFlagEvaluation(String flagName, dynamic value) {
    // This would integrate with analytics service
    debugPrint('Flag evaluated: $flagName = $value');
  }

  /// Dispose of feature flags service
  void dispose() {
    _flags.clear();
    _defaultFlags.clear();
    _userAttributes.clear();
  }
}

/// Feature flag widget for conditional rendering
class FeatureFlag extends StatelessWidget {
  final String flagName;
  final Widget child;
  final Widget? fallback;

  const FeatureFlag({
    super.key,
    required this.flagName,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    final isEnabled = FeatureFlagsService.instance.isEnabled(flagName);
    
    if (isEnabled) {
      return child;
    } else if (fallback != null) {
      return fallback!;
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// Feature variant widget for A/B testing
class FeatureVariant extends StatelessWidget {
  final String flagName;
  final Map<String, Widget> variants;
  final Widget? defaultWidget;

  const FeatureVariant({
    super.key,
    required this.flagName,
    required this.variants,
    this.defaultWidget,
  });

  @override
  Widget build(BuildContext context) {
    final variant = FeatureFlagsService.instance.getVariant(flagName);
    
    if (variants.containsKey(variant)) {
      return variants[variant]!;
    } else if (defaultWidget != null) {
      return defaultWidget!;
    } else {
      return const SizedBox.shrink();
    }
  }
}
