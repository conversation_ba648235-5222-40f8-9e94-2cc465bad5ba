/// WebSocket Client
/// Copyright (c) 2025 Echo Inc.
/// 
/// WebSocket client for real-time communication.

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import '../constants/app_constants.dart';
import '../utils/logger.dart';

/// WebSocket client for real-time communication
class WebSocketClient {
  final String _url;
  WebSocket? _socket;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();

  WebSocketClient(this._url);

  /// Stream of incoming messages
  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  
  /// Stream of connection status
  Stream<bool> get connectionStatus => _connectionController.stream;
  
  /// Current connection status
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect(String token) async {
    try {
      Logger.info('Connecting to WebSocket: $_url');
      
      final uri = Uri.parse('$_url?token=$token');
      _socket = await WebSocket.connect(uri.toString());
      
      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
      
      Logger.info('WebSocket connected successfully');
      
      // Listen for messages
      _socket!.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
      
      // Start heartbeat
      _startHeartbeat();
      
    } catch (e) {
      Logger.error('Failed to connect to WebSocket', e);
      _handleConnectionFailure();
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    _shouldReconnect = false;
    _stopHeartbeat();
    _stopReconnectTimer();
    
    if (_socket != null) {
      await _socket!.close();
      _socket = null;
    }
    
    _isConnected = false;
    _connectionController.add(false);
    
    Logger.info('WebSocket disconnected');
  }

  /// Send message to server
  void sendMessage(Map<String, dynamic> message) {
    if (_isConnected && _socket != null) {
      try {
        final jsonMessage = jsonEncode(message);
        _socket!.add(jsonMessage);
        Logger.debug('Sent WebSocket message: ${message['type']}');
      } catch (e) {
        Logger.error('Failed to send WebSocket message', e);
      }
    } else {
      Logger.warning('Cannot send message: WebSocket not connected');
    }
  }

  /// Send binary data (for voice)
  void sendBinaryData(List<int> data) {
    if (_isConnected && _socket != null) {
      try {
        _socket!.add(data);
        Logger.debug('Sent binary data: ${data.length} bytes');
      } catch (e) {
        Logger.error('Failed to send binary data', e);
      }
    } else {
      Logger.warning('Cannot send binary data: WebSocket not connected');
    }
  }

  /// Subscribe to a channel
  void subscribe(String channel) {
    sendMessage({
      'type': 'subscribe',
      'channel': channel,
    });
  }

  /// Unsubscribe from a channel
  void unsubscribe(String channel) {
    sendMessage({
      'type': 'unsubscribe',
      'channel': channel,
    });
  }

  /// Send ping message
  void ping() {
    sendMessage({
      'type': 'ping',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle incoming messages
  void _handleMessage(dynamic data) {
    try {
      if (data is String) {
        final message = jsonDecode(data) as Map<String, dynamic>;
        Logger.debug('Received WebSocket message: ${message['type']}');
        
        // Handle special message types
        switch (message['type']) {
          case 'pong':
            // Heartbeat response
            break;
          case 'connection_established':
            Logger.info('WebSocket connection established');
            break;
          default:
            _messageController.add(message);
        }
      } else {
        // Handle binary data
        Logger.debug('Received binary data: ${data.length} bytes');
        _messageController.add({
          'type': 'binary_data',
          'data': data,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      Logger.error('Failed to handle WebSocket message', e);
    }
  }

  /// Handle WebSocket errors
  void _handleError(dynamic error) {
    Logger.error('WebSocket error', error);
    _handleConnectionFailure();
  }

  /// Handle WebSocket disconnection
  void _handleDisconnection() {
    Logger.warning('WebSocket disconnected');
    _isConnected = false;
    _connectionController.add(false);
    _stopHeartbeat();
    
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  /// Handle connection failure
  void _handleConnectionFailure() {
    _isConnected = false;
    _connectionController.add(false);
    _stopHeartbeat();
    
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= AppConstants.maxReconnectAttempts) {
      Logger.error('Max reconnection attempts reached');
      return;
    }
    
    _reconnectAttempts++;
    final delay = Duration(
      seconds: AppConstants.reconnectDelay.inSeconds * _reconnectAttempts,
    );
    
    Logger.info('Scheduling reconnection attempt $_reconnectAttempts in ${delay.inSeconds}s');
    
    _reconnectTimer = Timer(delay, () {
      if (_shouldReconnect) {
        // Note: This would need the token to be stored or passed differently
        // For now, we'll just log the attempt
        Logger.info('Attempting to reconnect...');
      }
    });
  }

  /// Start heartbeat timer
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(
      AppConstants.heartbeatInterval,
      (_) => ping(),
    );
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Stop reconnect timer
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}
