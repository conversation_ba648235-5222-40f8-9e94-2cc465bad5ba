/// API Client
/// Copyright (c) 2025 Echo Inc.
/// 
/// HTTP client for API communication with the backend.

import 'package:dio/dio.dart';

import '../constants/app_constants.dart';
import '../utils/logger.dart';

/// API client for backend communication
class ApiClient {
  final Dio _dio;
  String? _accessToken;
  String? _sessionToken;

  ApiClient(this._dio);

  /// Initialize API client with authentication
  Future<bool> initialize({
    required String accessPassword,
    required String deviceId,
    required String platform,
    required String appVersion,
  }) async {
    try {
      // Step 1: Verify access password
      final accessResult = await verifyAccessPassword(accessPassword);
      if (accessResult['success'] != true) {
        Logger.error('Access password verification failed');
        return false;
      }

      // Step 2: Create mobile session
      final sessionResult = await createMobileSession(
        deviceId: deviceId,
        platform: platform,
        appVersion: appVersion,
      );
      if (sessionResult['success'] != true) {
        Logger.error('Mobile session creation failed');
        return false;
      }

      Logger.info('API client initialized successfully');
      return true;
    } catch (e) {
      Logger.error('Failed to initialize API client', e);
      return false;
    }
  }

  /// Verify access password and get access token
  Future<Map<String, dynamic>> verifyAccessPassword(String password) async {
    try {
      final response = await _dio.post(
        '/api/access/verify',
        data: {'password': password},
      );

      if (response.data['success'] == true) {
        // Store access token from cookie if available
        final cookies = response.headers['set-cookie'];
        if (cookies != null) {
          for (final cookie in cookies) {
            if (cookie.startsWith('accessToken=')) {
              _accessToken = cookie.split('=')[1].split(';')[0];
              break;
            }
          }
        }
      }

      return response.data;
    } catch (e) {
      Logger.error('Failed to verify access password', e);
      rethrow;
    }
  }

  /// Create mobile session
  Future<Map<String, dynamic>> createMobileSession({
    required String deviceId,
    required String platform,
    required String appVersion,
    String? userAgent,
  }) async {
    try {
      // Add access token to headers if available
      final headers = <String, String>{};
      if (_accessToken != null) {
        headers['x-access-token'] = _accessToken!;
      }

      final response = await _dio.post(
        '/api/mobile/session/create',
        data: {
          'deviceId': deviceId,
          'platform': platform,
          'appVersion': appVersion,
          'userAgent': userAgent,
        },
        options: Options(headers: headers),
      );

      // Store session token if successful
      if (response.data['success'] == true) {
        _sessionToken = response.data['sessionToken'];
      }

      return response.data;
    } catch (e) {
      Logger.error('Failed to create mobile session', e);
      rethrow;
    }
  }

  /// Get session info
  Future<Map<String, dynamic>> getSessionInfo() async {
    try {
      final response = await _dio.get('/api/mobile/session/info');
      return response.data;
    } catch (e) {
      Logger.error('Failed to get session info', e);
      rethrow;
    }
  }

  /// Update user preferences
  Future<Map<String, dynamic>> updateUserPreferences(
    Map<String, dynamic> preferences,
  ) async {
    try {
      final response = await _dio.put(
        '/api/mobile/session/preferences',
        data: {'preferences': preferences},
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to update user preferences', e);
      rethrow;
    }
  }

  /// Send chat message
  Future<Map<String, dynamic>> sendChatMessage({
    required String message,
    String? conversationId,
  }) async {
    try {
      // Add session token to headers if available
      final headers = <String, String>{};
      if (_sessionToken != null) {
        headers['Authorization'] = 'Bearer $_sessionToken';
      }
      if (_accessToken != null) {
        headers['x-access-token'] = _accessToken!;
      }

      final response = await _dio.post(
        '/api/mobile/chat',
        data: {
          'message': message,
          'conversationId': conversationId,
        },
        options: Options(headers: headers),
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to send chat message', e);
      rethrow;
    }
  }

  /// Get grouped conversation history
  Future<Map<String, dynamic>> getConversationHistory({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get(
        '/api/mobile/conversation-history/grouped',
        queryParameters: {
          'limit': limit,
          'offset': offset,
        },
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to get conversation history', e);
      rethrow;
    }
  }

  /// Verify access password
  Future<Map<String, dynamic>> verifyAccessPassword(String password) async {
    try {
      final response = await _dio.post(
        '/api/access/verify',
        data: {'password': password},
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to verify access password', e);
      rethrow;
    }
  }

  /// Terminate session
  Future<Map<String, dynamic>> terminateSession() async {
    try {
      final response = await _dio.delete('/api/mobile/session/terminate');
      return response.data;
    } catch (e) {
      Logger.error('Failed to terminate session', e);
      rethrow;
    }
  }

  /// Set authorization token
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Clear authorization token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
}
