/// API Client
/// Copyright (c) 2025 Echo Inc.
/// 
/// HTTP client for API communication with the backend.

import 'package:dio/dio.dart';

import '../constants/app_constants.dart';
import '../utils/logger.dart';

/// API client for backend communication
class ApiClient {
  final Dio _dio;

  ApiClient(this._dio);

  /// Create mobile session
  Future<Map<String, dynamic>> createMobileSession({
    required String deviceId,
    required String platform,
    required String appVersion,
    String? userAgent,
  }) async {
    try {
      final response = await _dio.post(
        '/api/mobile/session/create',
        data: {
          'deviceId': deviceId,
          'platform': platform,
          'appVersion': appVersion,
          'userAgent': userAgent,
        },
      );

      return response.data;
    } catch (e) {
      Logger.error('Failed to create mobile session', e);
      rethrow;
    }
  }

  /// Get session info
  Future<Map<String, dynamic>> getSessionInfo() async {
    try {
      final response = await _dio.get('/api/mobile/session/info');
      return response.data;
    } catch (e) {
      Logger.error('Failed to get session info', e);
      rethrow;
    }
  }

  /// Update user preferences
  Future<Map<String, dynamic>> updateUserPreferences(
    Map<String, dynamic> preferences,
  ) async {
    try {
      final response = await _dio.put(
        '/api/mobile/session/preferences',
        data: {'preferences': preferences},
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to update user preferences', e);
      rethrow;
    }
  }

  /// Send chat message
  Future<Map<String, dynamic>> sendChatMessage({
    required String message,
    String? conversationId,
  }) async {
    try {
      final response = await _dio.post(
        '/api/mobile/chat',
        data: {
          'message': message,
          'conversationId': conversationId,
        },
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to send chat message', e);
      rethrow;
    }
  }

  /// Get grouped conversation history
  Future<Map<String, dynamic>> getConversationHistory({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get(
        '/api/mobile/conversation-history/grouped',
        queryParameters: {
          'limit': limit,
          'offset': offset,
        },
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to get conversation history', e);
      rethrow;
    }
  }

  /// Verify access password
  Future<Map<String, dynamic>> verifyAccessPassword(String password) async {
    try {
      final response = await _dio.post(
        '/api/access/verify',
        data: {'password': password},
      );
      return response.data;
    } catch (e) {
      Logger.error('Failed to verify access password', e);
      rethrow;
    }
  }

  /// Terminate session
  Future<Map<String, dynamic>> terminateSession() async {
    try {
      final response = await _dio.delete('/api/mobile/session/terminate');
      return response.data;
    } catch (e) {
      Logger.error('Failed to terminate session', e);
      rethrow;
    }
  }

  /// Set authorization token
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Clear authorization token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
}
