/// Haptic Feedback Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive haptic feedback system for enhanced user interactions.

import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

/// Service for managing haptic feedback throughout the application
class HapticFeedbackService {
  static HapticFeedbackService? _instance;
  static HapticFeedbackService get instance => _instance ??= HapticFeedbackService._();
  HapticFeedbackService._();

  bool _isEnabled = true;
  bool _isSystemHapticsEnabled = true;

  /// Initialize haptic feedback service
  Future<void> initialize() async {
    await _checkSystemHapticSupport();
  }

  /// Check if system supports haptic feedback
  Future<void> _checkSystemHapticSupport() async {
    try {
      // Check if device supports haptics
      _isSystemHapticsEnabled = await _hasHapticSupport();
    } catch (e) {
      debugPrint('Error checking haptic support: $e');
      _isSystemHapticsEnabled = false;
    }
  }

  /// Check if device has haptic support
  Future<bool> _hasHapticSupport() async {
    try {
      final result = await const MethodChannel('echo/haptics')
          .invokeMethod<bool>('hasHapticSupport');
      return result ?? false;
    } catch (e) {
      // Fallback: assume mobile devices have haptic support
      return defaultTargetPlatform == TargetPlatform.iOS || 
             defaultTargetPlatform == TargetPlatform.android;
    }
  }

  /// Enable or disable haptic feedback
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Check if haptic feedback is enabled
  bool get isEnabled => _isEnabled && _isSystemHapticsEnabled;

  /// Light haptic feedback for subtle interactions
  Future<void> light() async {
    if (!isEnabled) return;
    
    try {
      await HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Error triggering light haptic: $e');
    }
  }

  /// Medium haptic feedback for standard interactions
  Future<void> medium() async {
    if (!isEnabled) return;
    
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      debugPrint('Error triggering medium haptic: $e');
    }
  }

  /// Heavy haptic feedback for important interactions
  Future<void> heavy() async {
    if (!isEnabled) return;
    
    try {
      await HapticFeedback.heavyImpact();
    } catch (e) {
      debugPrint('Error triggering heavy haptic: $e');
    }
  }

  /// Selection haptic feedback for picker/selector interactions
  Future<void> selection() async {
    if (!isEnabled) return;
    
    try {
      await HapticFeedback.selectionClick();
    } catch (e) {
      debugPrint('Error triggering selection haptic: $e');
    }
  }

  /// Vibrate for notifications and alerts
  Future<void> vibrate() async {
    if (!isEnabled) return;
    
    try {
      await HapticFeedback.vibrate();
    } catch (e) {
      debugPrint('Error triggering vibration: $e');
    }
  }

  /// Success haptic pattern for positive feedback
  Future<void> success() async {
    if (!isEnabled) return;
    
    try {
      // Custom success pattern: light-medium-light
      await light();
      await Future.delayed(const Duration(milliseconds: 50));
      await medium();
      await Future.delayed(const Duration(milliseconds: 50));
      await light();
    } catch (e) {
      debugPrint('Error triggering success haptic: $e');
    }
  }

  /// Error haptic pattern for negative feedback
  Future<void> error() async {
    if (!isEnabled) return;
    
    try {
      // Custom error pattern: heavy-heavy-heavy with pauses
      await heavy();
      await Future.delayed(const Duration(milliseconds: 100));
      await heavy();
      await Future.delayed(const Duration(milliseconds: 100));
      await heavy();
    } catch (e) {
      debugPrint('Error triggering error haptic: $e');
    }
  }

  /// Warning haptic pattern for cautionary feedback
  Future<void> warning() async {
    if (!isEnabled) return;
    
    try {
      // Custom warning pattern: medium-pause-medium
      await medium();
      await Future.delayed(const Duration(milliseconds: 150));
      await medium();
    } catch (e) {
      debugPrint('Error triggering warning haptic: $e');
    }
  }

  /// Voice interaction haptic feedback
  Future<void> voiceStart() async {
    if (!isEnabled) return;
    
    try {
      // Gentle feedback for voice recording start
      await light();
    } catch (e) {
      debugPrint('Error triggering voice start haptic: $e');
    }
  }

  /// Voice recording stop haptic feedback
  Future<void> voiceStop() async {
    if (!isEnabled) return;
    
    try {
      // Confirming feedback for voice recording stop
      await medium();
    } catch (e) {
      debugPrint('Error triggering voice stop haptic: $e');
    }
  }

  /// Voice activity detection haptic feedback
  Future<void> voiceDetected() async {
    if (!isEnabled) return;
    
    try {
      // Subtle feedback when voice is detected
      await light();
    } catch (e) {
      debugPrint('Error triggering voice detected haptic: $e');
    }
  }

  /// Button press haptic feedback
  Future<void> buttonPress() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering button press haptic: $e');
    }
  }

  /// Toggle switch haptic feedback
  Future<void> toggle() async {
    if (!isEnabled) return;
    
    try {
      await selection();
    } catch (e) {
      debugPrint('Error triggering toggle haptic: $e');
    }
  }

  /// Swipe gesture haptic feedback
  Future<void> swipe() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering swipe haptic: $e');
    }
  }

  /// Long press haptic feedback
  Future<void> longPress() async {
    if (!isEnabled) return;
    
    try {
      await medium();
    } catch (e) {
      debugPrint('Error triggering long press haptic: $e');
    }
  }

  /// Drag start haptic feedback
  Future<void> dragStart() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering drag start haptic: $e');
    }
  }

  /// Drag end haptic feedback
  Future<void> dragEnd() async {
    if (!isEnabled) return;
    
    try {
      await medium();
    } catch (e) {
      debugPrint('Error triggering drag end haptic: $e');
    }
  }

  /// Stock card flip haptic feedback
  Future<void> stockCardFlip() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering stock card flip haptic: $e');
    }
  }

  /// Stock favorite toggle haptic feedback
  Future<void> stockFavorite() async {
    if (!isEnabled) return;
    
    try {
      await medium();
    } catch (e) {
      debugPrint('Error triggering stock favorite haptic: $e');
    }
  }

  /// Lead score update haptic feedback
  Future<void> leadScoreUpdate() async {
    if (!isEnabled) return;
    
    try {
      // Gentle feedback for score updates
      await light();
    } catch (e) {
      debugPrint('Error triggering lead score update haptic: $e');
    }
  }

  /// Message send haptic feedback
  Future<void> messageSend() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering message send haptic: $e');
    }
  }

  /// Message receive haptic feedback
  Future<void> messageReceive() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering message receive haptic: $e');
    }
  }

  /// Navigation haptic feedback
  Future<void> navigation() async {
    if (!isEnabled) return;
    
    try {
      await selection();
    } catch (e) {
      debugPrint('Error triggering navigation haptic: $e');
    }
  }

  /// Tab switch haptic feedback
  Future<void> tabSwitch() async {
    if (!isEnabled) return;
    
    try {
      await selection();
    } catch (e) {
      debugPrint('Error triggering tab switch haptic: $e');
    }
  }

  /// Modal open haptic feedback
  Future<void> modalOpen() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering modal open haptic: $e');
    }
  }

  /// Modal close haptic feedback
  Future<void> modalClose() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering modal close haptic: $e');
    }
  }

  /// Refresh haptic feedback
  Future<void> refresh() async {
    if (!isEnabled) return;
    
    try {
      await medium();
    } catch (e) {
      debugPrint('Error triggering refresh haptic: $e');
    }
  }

  /// Search haptic feedback
  Future<void> search() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering search haptic: $e');
    }
  }

  /// Filter apply haptic feedback
  Future<void> filterApply() async {
    if (!isEnabled) return;
    
    try {
      await medium();
    } catch (e) {
      debugPrint('Error triggering filter apply haptic: $e');
    }
  }

  /// Data load complete haptic feedback
  Future<void> dataLoadComplete() async {
    if (!isEnabled) return;
    
    try {
      await light();
    } catch (e) {
      debugPrint('Error triggering data load complete haptic: $e');
    }
  }

  /// Custom haptic pattern
  Future<void> customPattern(List<HapticEvent> events) async {
    if (!isEnabled) return;
    
    try {
      for (final event in events) {
        switch (event.type) {
          case HapticType.light:
            await light();
            break;
          case HapticType.medium:
            await medium();
            break;
          case HapticType.heavy:
            await heavy();
            break;
          case HapticType.selection:
            await selection();
            break;
          case HapticType.vibrate:
            await vibrate();
            break;
        }
        
        if (event.delay != null) {
          await Future.delayed(event.delay!);
        }
      }
    } catch (e) {
      debugPrint('Error triggering custom haptic pattern: $e');
    }
  }

  /// Get haptic feedback settings
  Map<String, dynamic> getSettings() {
    return {
      'enabled': _isEnabled,
      'systemSupported': _isSystemHapticsEnabled,
      'platform': defaultTargetPlatform.name,
    };
  }

  /// Dispose of haptic feedback service
  void dispose() {
    // Clean up any resources
  }
}

/// Haptic event for custom patterns
class HapticEvent {
  final HapticType type;
  final Duration? delay;

  const HapticEvent({
    required this.type,
    this.delay,
  });
}

/// Types of haptic feedback
enum HapticType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}

/// Extension for easy haptic feedback on widgets
extension HapticFeedbackExtension on Widget {
  Widget withHapticFeedback({
    required VoidCallback onTap,
    HapticType hapticType = HapticType.light,
  }) {
    return GestureDetector(
      onTap: () async {
        final haptics = HapticFeedbackService.instance;
        switch (hapticType) {
          case HapticType.light:
            await haptics.light();
            break;
          case HapticType.medium:
            await haptics.medium();
            break;
          case HapticType.heavy:
            await haptics.heavy();
            break;
          case HapticType.selection:
            await haptics.selection();
            break;
          case HapticType.vibrate:
            await haptics.vibrate();
            break;
        }
        onTap();
      },
      child: this,
    );
  }
}
