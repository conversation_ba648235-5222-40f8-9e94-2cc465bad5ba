/// Performance Monitoring Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive performance monitoring for app optimization and user experience.

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';

/// Service for monitoring app performance and resource usage
class PerformanceMonitoringService {
  static PerformanceMonitoringService? _instance;
  static PerformanceMonitoringService get instance => _instance ??= PerformanceMonitoringService._();
  PerformanceMonitoringService._();

  bool _isEnabled = true;
  bool _isInitialized = false;
  Timer? _monitoringTimer;
  final List<PerformanceMetric> _metrics = [];
  final Map<String, Stopwatch> _activeTimers = {};

  /// Initialize performance monitoring
  Future<void> initialize() async {
    try {
      _isInitialized = true;
      
      // Start periodic monitoring
      _startPeriodicMonitoring();
      
      // Monitor app lifecycle
      _setupAppLifecycleMonitoring();
      
      // Track app startup time
      await _trackAppStartupTime();
      
    } catch (e) {
      debugPrint('Error initializing performance monitoring: $e');
    }
  }

  /// Enable or disable performance monitoring
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    
    if (!enabled) {
      _stopPeriodicMonitoring();
    } else if (_isInitialized) {
      _startPeriodicMonitoring();
    }
  }

  /// Start periodic performance monitoring
  void _startPeriodicMonitoring() {
    _monitoringTimer?.cancel();
    
    if (!_isEnabled) return;
    
    _monitoringTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _collectPerformanceMetrics();
    });
  }

  /// Stop periodic monitoring
  void _stopPeriodicMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  /// Setup app lifecycle monitoring
  void _setupAppLifecycleMonitoring() {
    // This would integrate with WidgetsBindingObserver
    // to track app state changes
  }

  /// Track app startup time
  Future<void> _trackAppStartupTime() async {
    try {
      final startupTime = await const MethodChannel('echo/performance')
          .invokeMethod<int>('getAppStartupTime');
      
      if (startupTime != null) {
        recordMetric(
          name: 'app_startup_time',
          value: startupTime.toDouble(),
          unit: 'milliseconds',
          category: PerformanceCategory.startup,
        );
      }
    } catch (e) {
      debugPrint('Error tracking app startup time: $e');
    }
  }

  /// Collect performance metrics
  Future<void> _collectPerformanceMetrics() async {
    if (!_isEnabled) return;

    try {
      // Memory usage
      final memoryUsage = await _getMemoryUsage();
      recordMetric(
        name: 'memory_usage',
        value: memoryUsage,
        unit: 'MB',
        category: PerformanceCategory.memory,
      );

      // CPU usage
      final cpuUsage = await _getCPUUsage();
      recordMetric(
        name: 'cpu_usage',
        value: cpuUsage,
        unit: 'percentage',
        category: PerformanceCategory.cpu,
      );

      // Battery level
      final batteryLevel = await _getBatteryLevel();
      recordMetric(
        name: 'battery_level',
        value: batteryLevel,
        unit: 'percentage',
        category: PerformanceCategory.battery,
      );

      // Network status
      final networkLatency = await _getNetworkLatency();
      if (networkLatency != null) {
        recordMetric(
          name: 'network_latency',
          value: networkLatency,
          unit: 'milliseconds',
          category: PerformanceCategory.network,
        );
      }

      // Storage usage
      final storageUsage = await _getStorageUsage();
      recordMetric(
        name: 'storage_usage',
        value: storageUsage,
        unit: 'MB',
        category: PerformanceCategory.storage,
      );

    } catch (e) {
      debugPrint('Error collecting performance metrics: $e');
    }
  }

  /// Get memory usage in MB
  Future<double> _getMemoryUsage() async {
    try {
      final result = await const MethodChannel('echo/performance')
          .invokeMethod<double>('getMemoryUsage');
      return result ?? 0.0;
    } catch (e) {
      // Fallback estimation
      return ProcessInfo.currentRss / (1024 * 1024); // Convert bytes to MB
    }
  }

  /// Get CPU usage percentage
  Future<double> _getCPUUsage() async {
    try {
      final result = await const MethodChannel('echo/performance')
          .invokeMethod<double>('getCPUUsage');
      return result ?? 0.0;
    } catch (e) {
      return 0.0; // Fallback
    }
  }

  /// Get battery level percentage
  Future<double> _getBatteryLevel() async {
    try {
      final result = await const MethodChannel('echo/performance')
          .invokeMethod<double>('getBatteryLevel');
      return result ?? 100.0;
    } catch (e) {
      return 100.0; // Fallback
    }
  }

  /// Get network latency in milliseconds
  Future<double?> _getNetworkLatency() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Simple ping to check network latency
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      
      stopwatch.stop();
      
      if (result.isNotEmpty) {
        return stopwatch.elapsedMilliseconds.toDouble();
      }
      return null;
    } catch (e) {
      return null; // No network connection
    }
  }

  /// Get storage usage in MB
  Future<double> _getStorageUsage() async {
    try {
      final result = await const MethodChannel('echo/performance')
          .invokeMethod<double>('getStorageUsage');
      return result ?? 0.0;
    } catch (e) {
      return 0.0; // Fallback
    }
  }

  /// Record a performance metric
  void recordMetric({
    required String name,
    required double value,
    required String unit,
    required PerformanceCategory category,
    Map<String, dynamic>? metadata,
  }) {
    if (!_isEnabled) return;

    final metric = PerformanceMetric(
      name: name,
      value: value,
      unit: unit,
      category: category,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );

    _metrics.add(metric);

    // Keep only recent metrics to prevent memory issues
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, _metrics.length - 1000);
    }

    // Log critical performance issues
    _checkPerformanceThresholds(metric);
  }

  /// Start timing an operation
  void startTimer(String operationName) {
    _activeTimers[operationName] = Stopwatch()..start();
  }

  /// Stop timing an operation and record the duration
  void stopTimer(String operationName, {
    PerformanceCategory category = PerformanceCategory.operation,
    Map<String, dynamic>? metadata,
  }) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch != null) {
      stopwatch.stop();
      recordMetric(
        name: operationName,
        value: stopwatch.elapsedMilliseconds.toDouble(),
        unit: 'milliseconds',
        category: category,
        metadata: metadata,
      );
    }
  }

  /// Time an async operation
  Future<T> timeOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    PerformanceCategory category = PerformanceCategory.operation,
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      
      recordMetric(
        name: operationName,
        value: stopwatch.elapsedMilliseconds.toDouble(),
        unit: 'milliseconds',
        category: category,
        metadata: {
          'success': true,
          ...?metadata,
        },
      );
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      recordMetric(
        name: operationName,
        value: stopwatch.elapsedMilliseconds.toDouble(),
        unit: 'milliseconds',
        category: category,
        metadata: {
          'success': false,
          'error': e.toString(),
          ...?metadata,
        },
      );
      
      rethrow;
    }
  }

  /// Check performance thresholds and log warnings
  void _checkPerformanceThresholds(PerformanceMetric metric) {
    switch (metric.name) {
      case 'memory_usage':
        if (metric.value > 200) { // 200MB
          debugPrint('WARNING: High memory usage: ${metric.value}MB');
        }
        break;
      case 'cpu_usage':
        if (metric.value > 80) { // 80%
          debugPrint('WARNING: High CPU usage: ${metric.value}%');
        }
        break;
      case 'network_latency':
        if (metric.value > 2000) { // 2 seconds
          debugPrint('WARNING: High network latency: ${metric.value}ms');
        }
        break;
      case 'app_startup_time':
        if (metric.value > 3000) { // 3 seconds
          debugPrint('WARNING: Slow app startup: ${metric.value}ms');
        }
        break;
    }
  }

  /// Get performance summary
  PerformanceSummary getPerformanceSummary({
    Duration? timeWindow,
  }) {
    final cutoffTime = timeWindow != null 
        ? DateTime.now().subtract(timeWindow)
        : DateTime.now().subtract(const Duration(hours: 1));

    final recentMetrics = _metrics
        .where((metric) => metric.timestamp.isAfter(cutoffTime))
        .toList();

    final summary = <String, MetricSummary>{};

    for (final metric in recentMetrics) {
      if (!summary.containsKey(metric.name)) {
        summary[metric.name] = MetricSummary(
          name: metric.name,
          unit: metric.unit,
          category: metric.category,
          values: [],
        );
      }
      summary[metric.name]!.values.add(metric.value);
    }

    // Calculate statistics for each metric
    for (final metricSummary in summary.values) {
      metricSummary.calculateStatistics();
    }

    return PerformanceSummary(
      timeWindow: timeWindow ?? const Duration(hours: 1),
      metricSummaries: summary,
      totalMetrics: recentMetrics.length,
      timestamp: DateTime.now(),
    );
  }

  /// Get metrics by category
  List<PerformanceMetric> getMetricsByCategory(
    PerformanceCategory category, {
    Duration? timeWindow,
  }) {
    final cutoffTime = timeWindow != null 
        ? DateTime.now().subtract(timeWindow)
        : DateTime.now().subtract(const Duration(hours: 1));

    return _metrics
        .where((metric) => 
            metric.category == category && 
            metric.timestamp.isAfter(cutoffTime))
        .toList();
  }

  /// Export performance data
  Map<String, dynamic> exportPerformanceData({
    Duration? timeWindow,
  }) {
    final summary = getPerformanceSummary(timeWindow: timeWindow);
    
    return {
      'summary': summary.toJson(),
      'raw_metrics': _metrics
          .where((metric) => timeWindow == null || 
              metric.timestamp.isAfter(DateTime.now().subtract(timeWindow)))
          .map((metric) => metric.toJson())
          .toList(),
      'export_timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Clear performance data
  void clearData() {
    _metrics.clear();
    _activeTimers.clear();
  }

  /// Dispose of performance monitoring service
  void dispose() {
    _stopPeriodicMonitoring();
    _activeTimers.clear();
    _metrics.clear();
  }
}

/// Performance metric model
class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final PerformanceCategory category;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.category,
    required this.timestamp,
    required this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'unit': unit,
      'category': category.name,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Performance categories
enum PerformanceCategory {
  startup,
  memory,
  cpu,
  battery,
  network,
  storage,
  operation,
  ui,
  database,
  api,
}

/// Metric summary with statistics
class MetricSummary {
  final String name;
  final String unit;
  final PerformanceCategory category;
  final List<double> values;
  
  double? average;
  double? minimum;
  double? maximum;
  double? median;
  double? standardDeviation;

  MetricSummary({
    required this.name,
    required this.unit,
    required this.category,
    required this.values,
  });

  void calculateStatistics() {
    if (values.isEmpty) return;

    values.sort();
    
    average = values.reduce((a, b) => a + b) / values.length;
    minimum = values.first;
    maximum = values.last;
    median = values.length % 2 == 0
        ? (values[values.length ~/ 2 - 1] + values[values.length ~/ 2]) / 2
        : values[values.length ~/ 2];
    
    // Calculate standard deviation
    final variance = values
        .map((value) => (value - average!) * (value - average!))
        .reduce((a, b) => a + b) / values.length;
    standardDeviation = variance.sqrt();
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'unit': unit,
      'category': category.name,
      'count': values.length,
      'average': average,
      'minimum': minimum,
      'maximum': maximum,
      'median': median,
      'standard_deviation': standardDeviation,
    };
  }
}

/// Performance summary
class PerformanceSummary {
  final Duration timeWindow;
  final Map<String, MetricSummary> metricSummaries;
  final int totalMetrics;
  final DateTime timestamp;

  PerformanceSummary({
    required this.timeWindow,
    required this.metricSummaries,
    required this.totalMetrics,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'time_window_minutes': timeWindow.inMinutes,
      'metric_summaries': metricSummaries.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'total_metrics': totalMetrics,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Extension for square root calculation
extension DoubleExtension on double {
  double sqrt() {
    return this < 0 ? double.nan : this.squareRoot();
  }
  
  double squareRoot() {
    if (this < 0) return double.nan;
    if (this == 0) return 0;
    
    double x = this;
    double prev;
    
    do {
      prev = x;
      x = (x + this / x) / 2;
    } while ((x - prev).abs() > 0.0001);
    
    return x;
  }
}
