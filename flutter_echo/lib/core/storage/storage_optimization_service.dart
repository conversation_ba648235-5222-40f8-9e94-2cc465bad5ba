/// Storage Optimization Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for optimizing local storage with compression and cleanup.

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';

import '../utils/result.dart';
import '../utils/logger.dart';
import 'hive_setup.dart';

/// Service for storage optimization and compression
class StorageOptimizationService {
  static const String _compressedDataDir = 'compressed_data';
  static const String _backupDir = 'backups';
  static const int _compressionThreshold = 1024; // 1KB
  static const int _maxBackups = 5;
  static const Duration _cleanupInterval = Duration(hours: 24);
  
  static DateTime? _lastCleanup;

  /// Initialize storage optimization
  static Future<Result<void>> initialize() async {
    try {
      Logger.info('Initializing storage optimization service...');
      
      // Create directories
      await _createDirectories();
      
      // Perform initial cleanup if needed
      await _performCleanupIfNeeded();
      
      Logger.info('Storage optimization service initialized');
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize storage optimization', e, stackTrace);
      return Result.error('Storage optimization initialization failed', 
                         StorageException('Init error', originalError: e));
    }
  }

  /// Compress data if it exceeds threshold
  static Future<Result<Uint8List>> compressData(String data) async {
    try {
      final bytes = utf8.encode(data);
      
      if (bytes.length < _compressionThreshold) {
        // Don't compress small data
        return Result.success(Uint8List.fromList(bytes));
      }

      final archive = Archive();
      final file = ArchiveFile('data', bytes.length, bytes);
      archive.addFile(file);
      
      final compressed = ZipEncoder().encode(archive);
      if (compressed == null) {
        return Result.error('Compression failed');
      }

      final compressionRatio = (1 - (compressed.length / bytes.length)) * 100;
      Logger.debug('Data compressed: ${bytes.length} -> ${compressed.length} bytes (${compressionRatio.toStringAsFixed(1)}% reduction)');
      
      return Result.success(Uint8List.fromList(compressed));
    } catch (e, stackTrace) {
      Logger.error('Failed to compress data', e, stackTrace);
      return Result.error('Data compression failed', 
                         StorageException('Compression error', originalError: e));
    }
  }

  /// Decompress data
  static Future<Result<String>> decompressData(Uint8List compressedData) async {
    try {
      // Try to decode as UTF-8 first (uncompressed data)
      try {
        final uncompressed = utf8.decode(compressedData);
        return Result.success(uncompressed);
      } catch (_) {
        // Data is compressed, proceed with decompression
      }

      final archive = ZipDecoder().decodeBytes(compressedData);
      if (archive.isEmpty) {
        return Result.error('Invalid compressed data');
      }

      final file = archive.first;
      final decompressed = utf8.decode(file.content as List<int>);
      
      Logger.debug('Data decompressed: ${compressedData.length} -> ${decompressed.length} bytes');
      return Result.success(decompressed);
    } catch (e, stackTrace) {
      Logger.error('Failed to decompress data', e, stackTrace);
      return Result.error('Data decompression failed', 
                         StorageException('Decompression error', originalError: e));
    }
  }

  /// Optimize database storage
  static Future<Result<StorageOptimizationResult>> optimizeStorage() async {
    try {
      Logger.info('Starting storage optimization...');
      
      final result = StorageOptimizationResult();
      final startTime = DateTime.now();
      
      // Get initial storage stats
      final initialStats = await getStorageStatistics();
      if (initialStats.isError) {
        return Result.error('Failed to get initial storage stats');
      }
      
      result.initialSize = initialStats.data!['totalSize'] as int;
      
      // Compact Hive databases
      await HiveSetup.compactDatabase();
      result.compactionCompleted = true;
      
      // Clean up old data
      await _cleanupOldData();
      result.cleanupCompleted = true;
      
      // Compress large text fields
      await _compressLargeTextFields();
      result.compressionCompleted = true;
      
      // Get final storage stats
      final finalStats = await getStorageStatistics();
      if (finalStats.isSuccess) {
        result.finalSize = finalStats.data!['totalSize'] as int;
        result.spaceSaved = result.initialSize - result.finalSize;
        result.compressionRatio = result.spaceSaved / result.initialSize;
      }
      
      result.duration = DateTime.now().difference(startTime);
      
      Logger.info('Storage optimization completed: ${result.spaceSaved} bytes saved (${(result.compressionRatio * 100).toStringAsFixed(1)}%)');
      return Result.success(result);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to optimize storage', e, stackTrace);
      return Result.error('Storage optimization failed', 
                         StorageException('Optimization error', originalError: e));
    }
  }

  /// Get storage statistics
  static Future<Result<Map<String, dynamic>>> getStorageStatistics() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final stats = <String, dynamic>{};
      
      // Get Hive database stats
      final hiveStats = HiveSetup.getDatabaseStats();
      stats.addAll(hiveStats);
      
      // Calculate total size
      int totalSize = 0;
      await for (final entity in appDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      
      stats['totalSize'] = totalSize;
      stats['totalSizeMB'] = (totalSize / (1024 * 1024)).toStringAsFixed(2);
      
      // Get cache size
      final cacheDir = await getTemporaryDirectory();
      int cacheSize = 0;
      await for (final entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          cacheSize += await entity.length();
        }
      }
      
      stats['cacheSize'] = cacheSize;
      stats['cacheSizeMB'] = (cacheSize / (1024 * 1024)).toStringAsFixed(2);
      
      return Result.success(stats);
    } catch (e, stackTrace) {
      Logger.error('Failed to get storage statistics', e, stackTrace);
      return Result.error('Failed to get storage stats', 
                         StorageException('Stats error', originalError: e));
    }
  }

  /// Clear cache data
  static Future<Result<void>> clearCache() async {
    try {
      Logger.info('Clearing cache data...');
      
      final cacheDir = await getTemporaryDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create();
      }
      
      // Clear Hive cache box
      final cacheBox = HiveSetup.cacheBox;
      await cacheBox.clear();
      
      Logger.info('Cache cleared successfully');
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to clear cache', e, stackTrace);
      return Result.error('Cache clear failed', 
                         StorageException('Cache clear error', originalError: e));
    }
  }

  /// Create backup of critical data
  static Future<Result<String>> createBackup() async {
    try {
      Logger.info('Creating data backup...');
      
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/$_backupDir');
      await backupDir.create(recursive: true);
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupFile = File('${backupDir.path}/backup_$timestamp.zip');
      
      // Create archive of critical data
      final archive = Archive();
      
      // Add Hive database files
      final hiveDir = Directory('${appDir.path}/hive');
      if (await hiveDir.exists()) {
        await for (final entity in hiveDir.list(recursive: true)) {
          if (entity is File) {
            final relativePath = entity.path.substring(appDir.path.length + 1);
            final bytes = await entity.readAsBytes();
            archive.addFile(ArchiveFile(relativePath, bytes.length, bytes));
          }
        }
      }
      
      // Encode and save backup
      final encoded = ZipEncoder().encode(archive);
      if (encoded != null) {
        await backupFile.writeAsBytes(encoded);
      }
      
      // Clean up old backups
      await _cleanupOldBackups(backupDir);
      
      Logger.info('Backup created: ${backupFile.path}');
      return Result.success(backupFile.path);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to create backup', e, stackTrace);
      return Result.error('Backup creation failed', 
                         StorageException('Backup error', originalError: e));
    }
  }

  /// Create required directories
  static Future<void> _createDirectories() async {
    final appDir = await getApplicationDocumentsDirectory();
    
    final compressedDir = Directory('${appDir.path}/$_compressedDataDir');
    await compressedDir.create(recursive: true);
    
    final backupDir = Directory('${appDir.path}/$_backupDir');
    await backupDir.create(recursive: true);
  }

  /// Perform cleanup if needed
  static Future<void> _performCleanupIfNeeded() async {
    if (_lastCleanup == null || 
        DateTime.now().difference(_lastCleanup!).compareTo(_cleanupInterval) > 0) {
      await _cleanupOldData();
      _lastCleanup = DateTime.now();
    }
  }

  /// Clean up old data
  static Future<void> _cleanupOldData() async {
    try {
      Logger.debug('Cleaning up old data...');
      
      // Clean up expired sync operations
      final syncBox = HiveSetup.syncQueueBox;
      final expiredKeys = syncBox.values
          .where((op) => op.isExpired)
          .map((op) => op.key)
          .toList();
      
      await syncBox.deleteAll(expiredKeys);
      
      // Clean up old cache entries
      await clearCache();
      
      Logger.debug('Old data cleanup completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to cleanup old data', e, stackTrace);
    }
  }

  /// Compress large text fields in database
  static Future<void> _compressLargeTextFields() async {
    try {
      Logger.debug('Compressing large text fields...');
      
      // This would compress large message content, conversation data, etc.
      // Implementation depends on specific compression strategy
      
      Logger.debug('Text field compression completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to compress text fields', e, stackTrace);
    }
  }

  /// Clean up old backups
  static Future<void> _cleanupOldBackups(Directory backupDir) async {
    try {
      final backupFiles = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.zip'))
          .cast<File>()
          .toList();
      
      if (backupFiles.length > _maxBackups) {
        // Sort by modification time (oldest first)
        backupFiles.sort((a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()));
        
        // Delete oldest backups
        for (int i = 0; i < backupFiles.length - _maxBackups; i++) {
          await backupFiles[i].delete();
          Logger.debug('Deleted old backup: ${backupFiles[i].path}');
        }
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to cleanup old backups', e, stackTrace);
    }
  }
}

/// Storage optimization result
class StorageOptimizationResult {
  int initialSize = 0;
  int finalSize = 0;
  int spaceSaved = 0;
  double compressionRatio = 0.0;
  Duration duration = Duration.zero;
  bool compactionCompleted = false;
  bool cleanupCompleted = false;
  bool compressionCompleted = false;

  Map<String, dynamic> toJson() {
    return {
      'initialSize': initialSize,
      'finalSize': finalSize,
      'spaceSaved': spaceSaved,
      'compressionRatio': compressionRatio,
      'duration': duration.inMilliseconds,
      'compactionCompleted': compactionCompleted,
      'cleanupCompleted': cleanupCompleted,
      'compressionCompleted': compressionCompleted,
    };
  }
}
