/// Hive Database Setup
/// Copyright (c) 2025 Echo Inc.
/// 
/// Hive database initialization with encrypted boxes and type adapters.

import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../constants/app_constants.dart';
import '../security/encryption_service.dart';
import '../utils/logger.dart';
import '../../features/chat/data/models/conversation_entity.dart';
import '../../features/chat/data/models/message_entity.dart';
import '../../features/stocks/data/models/stock_entity.dart';
import '../../features/leads/data/models/lead_entity.dart';
import '../../features/auth/data/models/user_entity.dart';
import '../../features/sync/data/models/sync_queue_entity.dart';
import '../../features/settings/data/models/user_preferences_entity.dart';
import '../../features/voice/data/models/audio_recording_entity.dart';
import '../../features/voice/data/models/audio_playback_entity.dart';
import '../../features/stocks/data/models/stock_mention.dart';

/// Hive database setup and initialization
class HiveSetup {
  static const String _conversationsBox = 'conversations';
  static const String _messagesBox = 'messages';
  static const String _stocksBox = 'stocks';
  static const String _leadsBox = 'leads';
  static const String _usersBox = 'users';
  static const String _syncQueueBox = 'sync_queue';
  static const String _preferencesBox = 'preferences';
  static const String _cacheBox = 'cache';
  static const String _metadataBox = 'metadata';
  static const String _audioRecordingsBox = 'audio_recordings';
  static const String _audioPlaybackBox = 'audio_playback';

  /// Initialize Hive database
  static Future<void> initialize() async {
    try {
      Logger.info('Initializing Hive database...');
      
      // Initialize Hive Flutter
      await Hive.initFlutter();
      
      // Register type adapters
      await _registerTypeAdapters();
      
      // Open encrypted boxes
      await _openEncryptedBoxes();
      
      // Open regular boxes
      await _openRegularBoxes();
      
      Logger.info('Hive database initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize Hive database', e, stackTrace);
      rethrow;
    }
  }

  /// Register all Hive type adapters
  static Future<void> _registerTypeAdapters() async {
    // Core entities
    Hive.registerAdapter(ConversationEntityAdapter());
    Hive.registerAdapter(MessageEntityAdapter());
    Hive.registerAdapter(MessageTypeAdapter());
    Hive.registerAdapter(StockEntityAdapter());
    Hive.registerAdapter(AnalystRatingAdapter());
    Hive.registerAdapter(LeadEntityAdapter());
    Hive.registerAdapter(InvestmentExperienceAdapter());
    Hive.registerAdapter(RiskToleranceAdapter());
    Hive.registerAdapter(InvestmentTimelineAdapter());
    Hive.registerAdapter(UserEntityAdapter());
    Hive.registerAdapter(UserRoleAdapter());
    
    // Sync and metadata entities
    Hive.registerAdapter(SyncQueueEntityAdapter());
    Hive.registerAdapter(SyncOperationTypeAdapter());
    Hive.registerAdapter(SyncStatusAdapter());
    Hive.registerAdapter(UserPreferencesEntityAdapter());
    Hive.registerAdapter(ConversationMetadataAdapter());
    Hive.registerAdapter(MessageMetadataAdapter());
    Hive.registerAdapter(StockMentionAdapter());

    // Voice entities (TypeId 25-30)
    Hive.registerAdapter(AudioRecordingEntityAdapter()); // TypeId 25
    Hive.registerAdapter(AudioQualityAdapter()); // TypeId 26
    Hive.registerAdapter(AudioSegmentAdapter()); // TypeId 27
    Hive.registerAdapter(AudioWordAdapter()); // TypeId 28
    Hive.registerAdapter(AudioPlaybackEntityAdapter()); // TypeId 29
    Hive.registerAdapter(AudioPlaybackQualityAdapter()); // TypeId 30

    // Stock mention entities (TypeId 31-33)
    Hive.registerAdapter(StockMentionAdapter()); // TypeId 31
    Hive.registerAdapter(StockMentionTypeAdapter()); // TypeId 32
    Hive.registerAdapter(StockMentionIntentAdapter()); // TypeId 33

    Logger.debug('Hive type adapters registered');
  }

  /// Open encrypted boxes for sensitive data
  static Future<void> _openEncryptedBoxes() async {
    final encryptionKey = await EncryptionService.getHiveEncryptionKey();
    
    // Open encrypted boxes
    await Hive.openBox<ConversationEntity>(
      _conversationsBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );
    
    await Hive.openBox<MessageEntity>(
      _messagesBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );
    
    await Hive.openBox<LeadEntity>(
      _leadsBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );
    
    await Hive.openBox<UserEntity>(
      _usersBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );
    
    await Hive.openBox<UserPreferencesEntity>(
      _preferencesBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );

    await Hive.openBox<AudioRecordingEntity>(
      _audioRecordingsBox,
      encryptionCipher: HiveAesCipher(encryptionKey),
    );

    Logger.debug('Encrypted Hive boxes opened');
  }

  /// Open regular boxes for non-sensitive data
  static Future<void> _openRegularBoxes() async {
    // Open regular boxes
    await Hive.openBox<StockEntity>(_stocksBox);
    await Hive.openBox<SyncQueueEntity>(_syncQueueBox);
    await Hive.openBox<AudioPlaybackEntity>(_audioPlaybackBox);
    await Hive.openBox(_cacheBox);
    await Hive.openBox(_metadataBox);

    Logger.debug('Regular Hive boxes opened');
  }

  /// Get conversations box
  static Box<ConversationEntity> get conversationsBox {
    return Hive.box<ConversationEntity>(_conversationsBox);
  }

  /// Get messages box
  static Box<MessageEntity> get messagesBox {
    return Hive.box<MessageEntity>(_messagesBox);
  }

  /// Get stocks box
  static Box<StockEntity> get stocksBox {
    return Hive.box<StockEntity>(_stocksBox);
  }

  /// Get leads box
  static Box<LeadEntity> get leadsBox {
    return Hive.box<LeadEntity>(_leadsBox);
  }

  /// Get users box
  static Box<UserEntity> get usersBox {
    return Hive.box<UserEntity>(_usersBox);
  }

  /// Get sync queue box
  static Box<SyncQueueEntity> get syncQueueBox {
    return Hive.box<SyncQueueEntity>(_syncQueueBox);
  }

  /// Get preferences box
  static Box<UserPreferencesEntity> get preferencesBox {
    return Hive.box<UserPreferencesEntity>(_preferencesBox);
  }

  /// Get cache box
  static Box get cacheBox {
    return Hive.box(_cacheBox);
  }

  /// Get metadata box
  static Box get metadataBox {
    return Hive.box(_metadataBox);
  }

  /// Get audio recordings box
  static Box<AudioRecordingEntity> get audioRecordingsBox {
    return Hive.box<AudioRecordingEntity>(_audioRecordingsBox);
  }

  /// Get audio playback box
  static Box<AudioPlaybackEntity> get audioPlaybackBox {
    return Hive.box<AudioPlaybackEntity>(_audioPlaybackBox);
  }

  /// Clear all data (for logout/reset)
  static Future<void> clearAllData() async {
    try {
      Logger.info('Clearing all Hive data...');
      
      await conversationsBox.clear();
      await messagesBox.clear();
      await stocksBox.clear();
      await leadsBox.clear();
      await usersBox.clear();
      await syncQueueBox.clear();
      await preferencesBox.clear();
      await audioRecordingsBox.clear();
      await audioPlaybackBox.clear();
      await cacheBox.clear();
      await metadataBox.clear();
      
      Logger.info('All Hive data cleared');
    } catch (e, stackTrace) {
      Logger.error('Failed to clear Hive data', e, stackTrace);
      rethrow;
    }
  }

  /// Get database statistics
  static Map<String, int> getDatabaseStats() {
    return {
      'conversations': conversationsBox.length,
      'messages': messagesBox.length,
      'stocks': stocksBox.length,
      'leads': leadsBox.length,
      'users': usersBox.length,
      'syncQueue': syncQueueBox.length,
      'preferences': preferencesBox.length,
      'cache': cacheBox.length,
      'metadata': metadataBox.length,
    };
  }

  /// Compact database (optimize storage)
  static Future<void> compactDatabase() async {
    try {
      Logger.info('Compacting Hive database...');
      
      await conversationsBox.compact();
      await messagesBox.compact();
      await stocksBox.compact();
      await leadsBox.compact();
      await usersBox.compact();
      await syncQueueBox.compact();
      await preferencesBox.compact();
      await cacheBox.compact();
      await metadataBox.compact();
      
      Logger.info('Hive database compacted successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to compact Hive database', e, stackTrace);
      rethrow;
    }
  }

  /// Close all boxes
  static Future<void> close() async {
    try {
      Logger.info('Closing Hive database...');
      await Hive.close();
      Logger.info('Hive database closed');
    } catch (e, stackTrace) {
      Logger.error('Failed to close Hive database', e, stackTrace);
      rethrow;
    }
  }

  /// Delete database (for complete reset)
  static Future<void> deleteDatabase() async {
    try {
      Logger.warning('Deleting Hive database...');
      
      await close();
      await Hive.deleteFromDisk();
      
      Logger.warning('Hive database deleted');
    } catch (e, stackTrace) {
      Logger.error('Failed to delete Hive database', e, stackTrace);
      rethrow;
    }
  }
}
