/// Result Pattern Implementation
/// Copyright (c) 2025 Echo Inc.
/// 
/// Result pattern for error handling in repositories and services.

import '../constants/app_constants.dart';
import 'logger.dart';

/// Result pattern for handling success and error states
sealed class Result<T> {
  const Result();

  /// Create a success result
  const factory Result.success(T data) = Success<T>;

  /// Create an error result
  const factory Result.error(String message, [Exception? exception]) = Error<T>;

  /// Check if result is success
  bool get isSuccess => this is Success<T>;

  /// Check if result is error
  bool get isError => this is Error<T>;

  /// Get data if success, null if error
  T? get data => switch (this) {
    Success<T> success => success.data,
    Error<T> _ => null,
  };

  /// Get error message if error, null if success
  String? get errorMessage => switch (this) {
    Success<T> _ => null,
    Error<T> error => error.message,
  };

  /// Get exception if error, null if success
  Exception? get exception => switch (this) {
    Success<T> _ => null,
    Error<T> error => error.exception,
  };

  /// Transform success data
  Result<U> map<U>(U Function(T data) transform) {
    return switch (this) {
      Success<T> success => Result.success(transform(success.data)),
      Error<T> error => Result.error(error.message, error.exception),
    };
  }

  /// Transform success data with async function
  Future<Result<U>> mapAsync<U>(Future<U> Function(T data) transform) async {
    return switch (this) {
      Success<T> success => {
        try {
          final result = await transform(success.data);
          Result.success(result);
        } catch (e) {
          Logger.error('Error in mapAsync transform', e);
          Result.error('Transform failed: ${e.toString()}', 
                      e is Exception ? e : Exception(e.toString()));
        }
      },
      Error<T> error => Result.error(error.message, error.exception),
    };
  }

  /// Chain results together
  Result<U> flatMap<U>(Result<U> Function(T data) transform) {
    return switch (this) {
      Success<T> success => transform(success.data),
      Error<T> error => Result.error(error.message, error.exception),
    };
  }

  /// Handle both success and error cases
  U fold<U>({
    required U Function(T data) onSuccess,
    required U Function(String message, Exception? exception) onError,
  }) {
    return switch (this) {
      Success<T> success => onSuccess(success.data),
      Error<T> error => onError(error.message, error.exception),
    };
  }

  /// Execute side effect on success
  Result<T> onSuccess(void Function(T data) action) {
    if (this is Success<T>) {
      action((this as Success<T>).data);
    }
    return this;
  }

  /// Execute side effect on error
  Result<T> onError(void Function(String message, Exception? exception) action) {
    if (this is Error<T>) {
      final error = this as Error<T>;
      action(error.message, error.exception);
    }
    return this;
  }

  /// Get data or throw exception
  T getOrThrow() {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> error => throw error.exception ?? Exception(error.message),
    };
  }

  /// Get data or return default value
  T getOrDefault(T defaultValue) {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> _ => defaultValue,
    };
  }

  /// Get data or compute default value
  T getOrElse(T Function() defaultValue) {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> _ => defaultValue(),
    };
  }

  /// Recover from error with new value
  Result<T> recover(T Function(String message, Exception? exception) recovery) {
    return switch (this) {
      Success<T> success => success,
      Error<T> error => Result.success(recovery(error.message, error.exception)),
    };
  }

  /// Recover from error with new result
  Result<T> recoverWith(Result<T> Function(String message, Exception? exception) recovery) {
    return switch (this) {
      Success<T> success => success,
      Error<T> error => recovery(error.message, error.exception),
    };
  }

  @override
  String toString() {
    return switch (this) {
      Success<T> success => 'Success(${success.data})',
      Error<T> error => 'Error(${error.message})',
    };
  }
}

/// Success result
final class Success<T> extends Result<T> {
  final T data;

  const Success(this.data);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is Success<T> && other.data == data);
  }

  @override
  int get hashCode => data.hashCode;
}

/// Error result
final class Error<T> extends Result<T> {
  final String message;
  final Exception? exception;

  const Error(this.message, [this.exception]);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is Error<T> && 
         other.message == message && 
         other.exception == exception);
  }

  @override
  int get hashCode => Object.hash(message, exception);
}

/// Extension for Future<Result<T>>
extension FutureResultExtension<T> on Future<Result<T>> {
  /// Transform future result
  Future<Result<U>> mapAsync<U>(Future<U> Function(T data) transform) async {
    final result = await this;
    return result.mapAsync(transform);
  }

  /// Chain future results
  Future<Result<U>> flatMapAsync<U>(Future<Result<U>> Function(T data) transform) async {
    final result = await this;
    return switch (result) {
      Success<T> success => await transform(success.data),
      Error<T> error => Result.error(error.message, error.exception),
    };
  }

  /// Handle timeout with error result
  Future<Result<T>> timeout(Duration duration, {String? timeoutMessage}) {
    return Future.any([
      this,
      Future.delayed(duration).then((_) => Result<T>.error(
        timeoutMessage ?? 'Operation timed out after ${duration.inSeconds}s',
        TimeoutException('Operation timeout', duration),
      )),
    ]);
  }

  /// Retry on error
  Future<Result<T>> retry(int maxAttempts, {Duration? delay}) async {
    var attempts = 0;
    Result<T>? lastResult;

    while (attempts < maxAttempts) {
      attempts++;
      lastResult = await this;
      
      if (lastResult.isSuccess) {
        return lastResult;
      }

      if (attempts < maxAttempts && delay != null) {
        await Future.delayed(delay);
      }
    }

    return lastResult!;
  }
}

/// Timeout exception
class TimeoutException implements Exception {
  final String message;
  final Duration? duration;

  const TimeoutException(this.message, [this.duration]);

  @override
  String toString() => 'TimeoutException: $message';
}

/// Repository exception
class RepositoryException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const RepositoryException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'RepositoryException: $message${code != null ? ' (code: $code)' : ''}';
}

/// Network exception
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const NetworkException(this.message, {this.statusCode, this.originalError});

  @override
  String toString() => 'NetworkException: $message${statusCode != null ? ' (status: $statusCode)' : ''}';
}

/// Validation exception
class ValidationException implements Exception {
  final String message;
  final Map<String, String>? fieldErrors;

  const ValidationException(this.message, {this.fieldErrors});

  @override
  String toString() => 'ValidationException: $message';
}

/// Storage exception
class StorageException implements Exception {
  final String message;
  final dynamic originalError;

  const StorageException(this.message, {this.originalError});

  @override
  String toString() => 'StorageException: $message';
}
