/// App Initializer
/// Copyright (c) 2025 Echo Inc.
/// 
/// Centralized initialization for all app services and features.

import '../services/analytics_service.dart';
import '../services/logging_service.dart';
import '../storage/local_storage.dart';
import '../utils/logger.dart';
import '../../features/auth/services/auth_service.dart';
import '../../features/chat/services/chat_service.dart';
import '../../features/history/services/conversation_history_service.dart';
import '../../features/stocks/services/stock_detection_service.dart';
import '../../features/voice/services/audio_recording_service.dart';
import '../../features/voice/services/speech_to_text_service.dart';
import '../../features/voice/services/text_to_speech_service.dart';
import '../../features/voice/services/voice_activity_detection_service.dart';
import '../../features/voice/services/voice_streaming_service.dart';

/// App initializer for all services and features
class AppInitializer {
  static bool _isInitialized = false;

  /// Initialize all app services
  static Future<void> initialize() async {
    if (_isInitialized) {
      Logger.info('App services already initialized');
      return;
    }

    try {
      Logger.info('Starting app services initialization...');

      // Initialize voice services first (critical for user experience)
      await _initializeVoiceServices();

      // Initialize other core services
      await _initializeCoreServices();

      // Initialize feature services
      await _initializeFeatureServices();

      _isInitialized = true;
      Logger.info('All app services initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize app services', e, stackTrace);
      rethrow;
    }
  }

  /// Initialize voice-related services
  static Future<void> _initializeVoiceServices() async {
    try {
      Logger.info('Initializing voice services...');

      // Initialize text-to-speech service
      final ttsResult = await TextToSpeechService.initialize();
      if (ttsResult.isError) {
        Logger.warning('TTS initialization failed: ${ttsResult.errorMessage}');
      } else {
        Logger.info('Text-to-speech service initialized');
      }

      // Initialize audio recording service
      final recordingResult = await AudioRecordingService.initialize();
      if (recordingResult.isError) {
        Logger.warning('Audio recording initialization failed: ${recordingResult.errorMessage}');
      } else {
        Logger.info('Audio recording service initialized');
      }

      // Initialize voice activity detection
      final vadResult = await VoiceActivityDetectionService.initialize();
      if (vadResult.isError) {
        Logger.warning('VAD initialization failed: ${vadResult.errorMessage}');
      } else {
        Logger.info('Voice activity detection service initialized');
      }

      // Initialize voice streaming service
      final streamingResult = await VoiceStreamingService.initialize();
      if (streamingResult.isError) {
        Logger.warning('Voice streaming initialization failed: ${streamingResult.errorMessage}');
      } else {
        Logger.info('Voice streaming service initialized');
      }

      Logger.info('Voice services initialization completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize voice services', e, stackTrace);
      // Don't rethrow - voice services are important but not critical for basic app function
    }
  }

  /// Initialize core services
  static Future<void> _initializeCoreServices() async {
    try {
      Logger.info('Initializing core services...');

      // Initialize authentication service
      await AuthService.initialize();
      Logger.info('Authentication service initialized');

      // Initialize chat service
      await ChatService.initialize();
      Logger.info('Chat service initialized');

      Logger.info('Core services initialization completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize core services', e, stackTrace);
      rethrow;
    }
  }

  /// Initialize feature services
  static Future<void> _initializeFeatureServices() async {
    try {
      Logger.info('Initializing feature services...');

      // Initialize conversation history service
      await ConversationHistoryService.initialize();
      Logger.info('Conversation history service initialized');

      // Initialize stock detection service
      await StockDetectionService.initialize();
      Logger.info('Stock detection service initialized');

      Logger.info('Feature services initialization completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize feature services', e, stackTrace);
      // Don't rethrow - feature services are optional
    }
  }

  /// Check if app is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
