/// Logger Utility
/// Copyright (c) 2025 Echo Inc.
/// 
/// Centralized logging utility for the application.

import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';

/// Application logger utility
class Logger {
  static const String _tag = 'Echo';

  /// Log debug message
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    if (AppConstants.enableDebugLogging && kDebugMode) {
      developer.log(
        message,
        name: _tag,
        level: 500, // Debug level
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Log info message
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 800, // Info level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log warning message
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 900, // Warning level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log error message
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log fatal error message
  static void fatal(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 1200, // Fatal level
      error: error,
      stackTrace: stackTrace,
    );
  }
}
