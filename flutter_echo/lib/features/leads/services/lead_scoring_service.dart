/// Lead Scoring Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for calculating lead scores with weighted criteria and analytics.

import 'dart:math' as math;

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../data/models/lead_entity.dart';

/// Service for lead scoring and qualification
class LeadScoringService {
  // Scoring weights (total should equal 1.0)
  static const Map<String, double> _scoringWeights = {
    'investmentExperience': 0.25,
    'portfolioSize': 0.30,
    'riskTolerance': 0.15,
    'investmentGoals': 0.15,
    'engagementLevel': 0.10,
    'responseQuality': 0.05,
  };

  // Score thresholds
  static const double _highScoreThreshold = 80.0;
  static const double _mediumScoreThreshold = 60.0;
  static const double _lowScoreThreshold = 40.0;

  /// Calculate comprehensive lead score
  static Result<LeadScore> calculateLeadScore(LeadEntity lead) {
    try {
      Logger.debug('Calculating lead score for: ${lead.id}');

      final scores = <String, double>{};
      double totalScore = 0.0;

      // 1. Investment Experience Score (25%)
      final experienceScore = _calculateExperienceScore(lead);
      scores['investmentExperience'] = experienceScore;
      totalScore += experienceScore * _scoringWeights['investmentExperience']!;

      // 2. Portfolio Size Score (30%)
      final portfolioScore = _calculatePortfolioScore(lead);
      scores['portfolioSize'] = portfolioScore;
      totalScore += portfolioScore * _scoringWeights['portfolioSize']!;

      // 3. Risk Tolerance Score (15%)
      final riskScore = _calculateRiskToleranceScore(lead);
      scores['riskTolerance'] = riskScore;
      totalScore += riskScore * _scoringWeights['riskTolerance']!;

      // 4. Investment Goals Score (15%)
      final goalsScore = _calculateInvestmentGoalsScore(lead);
      scores['investmentGoals'] = goalsScore;
      totalScore += goalsScore * _scoringWeights['investmentGoals']!;

      // 5. Engagement Level Score (10%)
      final engagementScore = _calculateEngagementScore(lead);
      scores['engagementLevel'] = engagementScore;
      totalScore += engagementScore * _scoringWeights['engagementLevel']!;

      // 6. Response Quality Score (5%)
      final responseScore = _calculateResponseQualityScore(lead);
      scores['responseQuality'] = responseScore;
      totalScore += responseScore * _scoringWeights['responseQuality']!;

      // Create lead score object
      final leadScore = LeadScore(
        leadId: lead.id,
        totalScore: totalScore.clamp(0.0, 100.0),
        categoryScores: scores,
        qualificationLevel: _getQualificationLevel(totalScore),
        calculatedAt: DateTime.now(),
        factors: _getScoreFactors(lead, scores),
        recommendations: _getRecommendations(lead, totalScore, scores),
      );

      Logger.info('Lead score calculated: ${leadScore.totalScore.toStringAsFixed(1)} (${leadScore.qualificationLevel.name})');
      return Result.success(leadScore);

    } catch (e, stackTrace) {
      Logger.error('Failed to calculate lead score', e, stackTrace);
      return Result.error('Lead scoring failed', 
                         ServiceException('Scoring error', originalError: e));
    }
  }

  /// Calculate investment experience score (0-100)
  static double _calculateExperienceScore(LeadEntity lead) {
    if (lead.investmentExperience == null) return 0.0;

    switch (lead.investmentExperience!) {
      case InvestmentExperience.beginner:
        return 20.0;
      case InvestmentExperience.intermediate:
        return 50.0;
      case InvestmentExperience.advanced:
        return 80.0;
      case InvestmentExperience.expert:
        return 100.0;
    }
  }

  /// Calculate portfolio size score (0-100)
  static double _calculatePortfolioScore(LeadEntity lead) {
    if (lead.portfolioSize == null) return 0.0;

    switch (lead.portfolioSize!) {
      case PortfolioSize.under10k:
        return 10.0;
      case PortfolioSize.from10kTo50k:
        return 25.0;
      case PortfolioSize.from50kTo250k:
        return 50.0;
      case PortfolioSize.from250kTo1m:
        return 75.0;
      case PortfolioSize.over1m:
        return 100.0;
    }
  }

  /// Calculate risk tolerance score (0-100)
  static double _calculateRiskToleranceScore(LeadEntity lead) {
    if (lead.riskTolerance == null) return 0.0;

    switch (lead.riskTolerance!) {
      case RiskTolerance.conservative:
        return 40.0;
      case RiskTolerance.moderate:
        return 70.0;
      case RiskTolerance.aggressive:
        return 100.0;
    }
  }

  /// Calculate investment goals score (0-100)
  static double _calculateInvestmentGoalsScore(LeadEntity lead) {
    if (lead.investmentGoals == null || lead.investmentGoals!.isEmpty) {
      return 0.0;
    }

    // Score based on number and type of goals
    double score = 0.0;
    final goals = lead.investmentGoals!;

    // Base score for having goals
    score += 30.0;

    // Bonus for specific high-value goals
    if (goals.contains(InvestmentGoal.wealthBuilding)) score += 20.0;
    if (goals.contains(InvestmentGoal.retirement)) score += 15.0;
    if (goals.contains(InvestmentGoal.diversification)) score += 15.0;
    if (goals.contains(InvestmentGoal.growthInvesting)) score += 10.0;
    if (goals.contains(InvestmentGoal.incomeGeneration)) score += 10.0;

    return math.min(score, 100.0);
  }

  /// Calculate engagement level score (0-100)
  static double _calculateEngagementScore(LeadEntity lead) {
    double score = 0.0;

    // Message count factor
    final messageCount = lead.messageCount ?? 0;
    if (messageCount > 0) {
      score += math.min(messageCount * 5.0, 40.0); // Max 40 points for messages
    }

    // Conversation duration factor
    if (lead.firstContactAt != null && lead.lastContactAt != null) {
      final duration = lead.lastContactAt!.difference(lead.firstContactAt!);
      final days = duration.inDays;
      
      if (days > 0) {
        score += math.min(days * 2.0, 30.0); // Max 30 points for duration
      }
    }

    // Response time factor
    if (lead.averageResponseTime != null) {
      final responseMinutes = lead.averageResponseTime!.inMinutes;
      if (responseMinutes < 60) {
        score += 30.0; // Fast responder
      } else if (responseMinutes < 240) {
        score += 20.0; // Moderate responder
      } else if (responseMinutes < 1440) {
        score += 10.0; // Slow responder
      }
    }

    return math.min(score, 100.0);
  }

  /// Calculate response quality score (0-100)
  static double _calculateResponseQualityScore(LeadEntity lead) {
    double score = 50.0; // Base score

    // Adjust based on available data quality
    if (lead.investmentExperience != null) score += 10.0;
    if (lead.portfolioSize != null) score += 15.0;
    if (lead.riskTolerance != null) score += 10.0;
    if (lead.investmentGoals != null && lead.investmentGoals!.isNotEmpty) score += 15.0;

    return math.min(score, 100.0);
  }

  /// Get qualification level based on total score
  static LeadQualificationLevel _getQualificationLevel(double totalScore) {
    if (totalScore >= _highScoreThreshold) {
      return LeadQualificationLevel.high;
    } else if (totalScore >= _mediumScoreThreshold) {
      return LeadQualificationLevel.medium;
    } else if (totalScore >= _lowScoreThreshold) {
      return LeadQualificationLevel.low;
    } else {
      return LeadQualificationLevel.veryLow;
    }
  }

  /// Get score factors explanation
  static List<ScoreFactor> _getScoreFactors(LeadEntity lead, Map<String, double> scores) {
    final factors = <ScoreFactor>[];

    factors.add(ScoreFactor(
      category: 'Investment Experience',
      score: scores['investmentExperience']!,
      weight: _scoringWeights['investmentExperience']!,
      description: _getExperienceDescription(lead.investmentExperience),
    ));

    factors.add(ScoreFactor(
      category: 'Portfolio Size',
      score: scores['portfolioSize']!,
      weight: _scoringWeights['portfolioSize']!,
      description: _getPortfolioDescription(lead.portfolioSize),
    ));

    factors.add(ScoreFactor(
      category: 'Risk Tolerance',
      score: scores['riskTolerance']!,
      weight: _scoringWeights['riskTolerance']!,
      description: _getRiskToleranceDescription(lead.riskTolerance),
    ));

    factors.add(ScoreFactor(
      category: 'Investment Goals',
      score: scores['investmentGoals']!,
      weight: _scoringWeights['investmentGoals']!,
      description: _getGoalsDescription(lead.investmentGoals),
    ));

    factors.add(ScoreFactor(
      category: 'Engagement Level',
      score: scores['engagementLevel']!,
      weight: _scoringWeights['engagementLevel']!,
      description: _getEngagementDescription(lead),
    ));

    factors.add(ScoreFactor(
      category: 'Response Quality',
      score: scores['responseQuality']!,
      weight: _scoringWeights['responseQuality']!,
      description: 'Quality of provided information',
    ));

    return factors;
  }

  /// Get recommendations based on score
  static List<String> _getRecommendations(LeadEntity lead, double totalScore, Map<String, double> scores) {
    final recommendations = <String>[];

    if (totalScore >= _highScoreThreshold) {
      recommendations.add('High-priority lead - schedule immediate follow-up');
      recommendations.add('Provide premium investment insights and personalized recommendations');
    } else if (totalScore >= _mediumScoreThreshold) {
      recommendations.add('Qualified lead - engage with targeted content');
      recommendations.add('Focus on education and building trust');
    } else {
      recommendations.add('Nurture lead with educational content');
      recommendations.add('Gather more qualification information');
    }

    // Specific recommendations based on weak areas
    if (scores['portfolioSize']! < 50.0) {
      recommendations.add('Focus on portfolio growth strategies');
    }

    if (scores['investmentExperience']! < 50.0) {
      recommendations.add('Provide educational content for beginners');
    }

    if (scores['engagementLevel']! < 50.0) {
      recommendations.add('Increase engagement with interactive content');
    }

    return recommendations;
  }

  // Helper methods for descriptions
  static String _getExperienceDescription(InvestmentExperience? experience) {
    if (experience == null) return 'Not specified';
    return experience.displayName;
  }

  static String _getPortfolioDescription(PortfolioSize? size) {
    if (size == null) return 'Not specified';
    return size.displayName;
  }

  static String _getRiskToleranceDescription(RiskTolerance? tolerance) {
    if (tolerance == null) return 'Not specified';
    return tolerance.displayName;
  }

  static String _getGoalsDescription(List<InvestmentGoal>? goals) {
    if (goals == null || goals.isEmpty) return 'Not specified';
    return goals.map((g) => g.displayName).join(', ');
  }

  static String _getEngagementDescription(LeadEntity lead) {
    final messageCount = lead.messageCount ?? 0;
    if (messageCount == 0) return 'No engagement yet';
    if (messageCount < 5) return 'Low engagement';
    if (messageCount < 15) return 'Moderate engagement';
    return 'High engagement';
  }

  /// Get scoring statistics
  static Map<String, dynamic> getScoringStatistics() {
    return {
      'weights': _scoringWeights,
      'thresholds': {
        'high': _highScoreThreshold,
        'medium': _mediumScoreThreshold,
        'low': _lowScoreThreshold,
      },
      'categories': _scoringWeights.keys.toList(),
    };
  }
}

/// Lead score data class
class LeadScore {
  final String leadId;
  final double totalScore;
  final Map<String, double> categoryScores;
  final LeadQualificationLevel qualificationLevel;
  final DateTime calculatedAt;
  final List<ScoreFactor> factors;
  final List<String> recommendations;

  const LeadScore({
    required this.leadId,
    required this.totalScore,
    required this.categoryScores,
    required this.qualificationLevel,
    required this.calculatedAt,
    required this.factors,
    required this.recommendations,
  });

  /// Get weighted contribution for a category
  double getWeightedScore(String category) {
    final score = categoryScores[category] ?? 0.0;
    final weight = LeadScoringService._scoringWeights[category] ?? 0.0;
    return score * weight;
  }

  /// Get score color for UI
  String get scoreColor {
    if (totalScore >= 80) return '#4CAF50'; // Green
    if (totalScore >= 60) return '#8BC34A'; // Light Green
    if (totalScore >= 40) return '#FFC107'; // Amber
    return '#F44336'; // Red
  }

  /// Get score description
  String get scoreDescription {
    if (totalScore >= 80) return 'Excellent';
    if (totalScore >= 60) return 'Good';
    if (totalScore >= 40) return 'Fair';
    return 'Poor';
  }
}

/// Score factor data class
class ScoreFactor {
  final String category;
  final double score;
  final double weight;
  final String description;

  const ScoreFactor({
    required this.category,
    required this.score,
    required this.weight,
    required this.description,
  });

  double get weightedScore => score * weight;
}

/// Lead qualification level enumeration
enum LeadQualificationLevel {
  veryLow,
  low,
  medium,
  high,
}

/// Extension for lead qualification level
extension LeadQualificationLevelExtension on LeadQualificationLevel {
  String get name {
    switch (this) {
      case LeadQualificationLevel.veryLow:
        return 'Very Low';
      case LeadQualificationLevel.low:
        return 'Low';
      case LeadQualificationLevel.medium:
        return 'Medium';
      case LeadQualificationLevel.high:
        return 'High';
    }
  }

  String get description {
    switch (this) {
      case LeadQualificationLevel.veryLow:
        return 'Requires significant nurturing';
      case LeadQualificationLevel.low:
        return 'Needs education and engagement';
      case LeadQualificationLevel.medium:
        return 'Qualified prospect';
      case LeadQualificationLevel.high:
        return 'High-priority qualified lead';
    }
  }

  String get color {
    switch (this) {
      case LeadQualificationLevel.veryLow:
        return '#F44336'; // Red
      case LeadQualificationLevel.low:
        return '#FF9800'; // Orange
      case LeadQualificationLevel.medium:
        return '#FFC107'; // Amber
      case LeadQualificationLevel.high:
        return '#4CAF50'; // Green
    }
  }
}
