/// Lead Export Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for exporting lead data in various formats (CSV, JSON, Excel).

import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../data/models/lead_entity.dart';
import '../services/lead_scoring_service.dart';

/// Service for exporting lead data
class LeadExportService {
  /// Export leads to CSV format
  static Future<Result<String>> exportToCSV({
    required List<LeadEntity> leads,
    bool includeScores = true,
    bool includeMetadata = false,
  }) async {
    try {
      Logger.debug('Exporting ${leads.length} leads to CSV');

      final csvData = StringBuffer();
      
      // CSV Header
      final headers = [
        'ID',
        'Name',
        'Email',
        'Phone',
        'Created At',
        'Last Contact',
        'Status',
        'Investment Experience',
        'Portfolio Size',
        'Risk Tolerance',
        'Investment Goals',
        'Message Count',
        'Average Response Time (minutes)',
      ];
      
      if (includeScores) {
        headers.addAll([
          'Score',
          'Qualification Level',
          'Score Calculated At',
        ]);
      }
      
      if (includeMetadata) {
        headers.addAll([
          'Source',
          'Referrer',
          'Device Type',
          'Location',
          'Notes',
        ]);
      }
      
      csvData.writeln(headers.map(_escapeCsvField).join(','));
      
      // CSV Data Rows
      for (final lead in leads) {
        final row = [
          lead.id,
          lead.name ?? '',
          lead.email ?? '',
          lead.phone ?? '',
          _formatDateTime(lead.createdAt),
          lead.lastContactAt != null ? _formatDateTime(lead.lastContactAt!) : '',
          lead.status?.name ?? '',
          lead.investmentExperience?.displayName ?? '',
          lead.portfolioSize?.displayName ?? '',
          lead.riskTolerance?.displayName ?? '',
          lead.investmentGoals?.map((g) => g.displayName).join('; ') ?? '',
          '${lead.messageCount ?? 0}',
          lead.averageResponseTime?.inMinutes.toString() ?? '',
        ];
        
        if (includeScores) {
          row.addAll([
            lead.score?.toStringAsFixed(2) ?? '',
            lead.qualificationLevel?.name ?? '',
            lead.scoreCalculatedAt != null ? _formatDateTime(lead.scoreCalculatedAt!) : '',
          ]);
        }
        
        if (includeMetadata) {
          row.addAll([
            lead.source ?? '',
            lead.referrer ?? '',
            lead.deviceType ?? '',
            lead.location ?? '',
            lead.notes ?? '',
          ]);
        }
        
        csvData.writeln(row.map(_escapeCsvField).join(','));
      }
      
      // Save to file
      final fileName = 'leads_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      final filePath = await _saveToFile(fileName, csvData.toString());
      
      Logger.info('CSV export completed: $filePath');
      return Result.success(filePath);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to export leads to CSV', e, stackTrace);
      return Result.error('CSV export failed', 
                         ServiceException('Export error', originalError: e));
    }
  }

  /// Export leads to JSON format
  static Future<Result<String>> exportToJSON({
    required List<LeadEntity> leads,
    bool includeScores = true,
    bool includeMetadata = true,
    bool prettyPrint = true,
  }) async {
    try {
      Logger.debug('Exporting ${leads.length} leads to JSON');

      final exportData = {
        'exportedAt': DateTime.now().toIso8601String(),
        'totalLeads': leads.length,
        'includeScores': includeScores,
        'includeMetadata': includeMetadata,
        'leads': leads.map((lead) => _leadToJson(
          lead,
          includeScores: includeScores,
          includeMetadata: includeMetadata,
        )).toList(),
      };
      
      final jsonString = prettyPrint 
          ? const JsonEncoder.withIndent('  ').convert(exportData)
          : json.encode(exportData);
      
      // Save to file
      final fileName = 'leads_export_${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = await _saveToFile(fileName, jsonString);
      
      Logger.info('JSON export completed: $filePath');
      return Result.success(filePath);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to export leads to JSON', e, stackTrace);
      return Result.error('JSON export failed', 
                         ServiceException('Export error', originalError: e));
    }
  }

  /// Export lead analytics summary
  static Future<Result<String>> exportAnalyticsSummary({
    required List<LeadEntity> leads,
    required Map<String, dynamic> statistics,
  }) async {
    try {
      Logger.debug('Exporting analytics summary for ${leads.length} leads');

      final summary = {
        'exportedAt': DateTime.now().toIso8601String(),
        'summary': {
          'totalLeads': leads.length,
          'qualifiedLeads': leads.where((l) => l.qualificationLevel != null).length,
          'averageScore': leads.isNotEmpty 
              ? leads.map((l) => l.score ?? 0).reduce((a, b) => a + b) / leads.length
              : 0,
          'conversionRate': statistics['conversionRate'] ?? 0,
        },
        'scoreDistribution': _calculateScoreDistribution(leads),
        'experienceDistribution': _calculateExperienceDistribution(leads),
        'portfolioSizeDistribution': _calculatePortfolioSizeDistribution(leads),
        'riskToleranceDistribution': _calculateRiskToleranceDistribution(leads),
        'topPerformingLeads': leads
            .where((l) => l.score != null)
            .toList()
            ..sort((a, b) => (b.score ?? 0).compareTo(a.score ?? 0))
            ..take(10)
            .map((l) => {
              'id': l.id,
              'name': l.name,
              'score': l.score,
              'qualificationLevel': l.qualificationLevel?.name,
            }).toList(),
        'recentActivity': leads
            .where((l) => l.lastContactAt != null)
            .toList()
            ..sort((a, b) => b.lastContactAt!.compareTo(a.lastContactAt!))
            ..take(20)
            .map((l) => {
              'id': l.id,
              'name': l.name,
              'lastContact': l.lastContactAt?.toIso8601String(),
              'messageCount': l.messageCount,
            }).toList(),
      };
      
      final jsonString = const JsonEncoder.withIndent('  ').convert(summary);
      
      // Save to file
      final fileName = 'lead_analytics_${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = await _saveToFile(fileName, jsonString);
      
      Logger.info('Analytics summary export completed: $filePath');
      return Result.success(filePath);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to export analytics summary', e, stackTrace);
      return Result.error('Analytics export failed', 
                         ServiceException('Export error', originalError: e));
    }
  }

  /// Share exported file
  static Future<Result<void>> shareFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return Result.error('File not found: $filePath');
      }
      
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Lead export data',
      );
      
      Logger.info('File shared: $filePath');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to share file', e, stackTrace);
      return Result.error('File sharing failed', 
                         ServiceException('Share error', originalError: e));
    }
  }

  /// Get export statistics
  static Map<String, dynamic> getExportStatistics() {
    return {
      'supportedFormats': ['CSV', 'JSON'],
      'maxLeadsPerExport': 10000,
      'includeOptions': [
        'Basic Information',
        'Contact Details',
        'Investment Profile',
        'Scoring Data',
        'Metadata',
        'Analytics Summary',
      ],
    };
  }

  // Helper Methods

  static Map<String, dynamic> _leadToJson(
    LeadEntity lead, {
    required bool includeScores,
    required bool includeMetadata,
  }) {
    final data = {
      'id': lead.id,
      'name': lead.name,
      'email': lead.email,
      'phone': lead.phone,
      'createdAt': lead.createdAt.toIso8601String(),
      'lastContactAt': lead.lastContactAt?.toIso8601String(),
      'firstContactAt': lead.firstContactAt?.toIso8601String(),
      'status': lead.status?.name,
      'investmentExperience': lead.investmentExperience?.name,
      'portfolioSize': lead.portfolioSize?.name,
      'riskTolerance': lead.riskTolerance?.name,
      'investmentGoals': lead.investmentGoals?.map((g) => g.name).toList(),
      'messageCount': lead.messageCount,
      'averageResponseTime': lead.averageResponseTime?.inMinutes,
    };
    
    if (includeScores) {
      data.addAll({
        'score': lead.score,
        'qualificationLevel': lead.qualificationLevel?.name,
        'scoreCalculatedAt': lead.scoreCalculatedAt?.toIso8601String(),
      });
    }
    
    if (includeMetadata) {
      data.addAll({
        'source': lead.source,
        'referrer': lead.referrer,
        'deviceType': lead.deviceType,
        'location': lead.location,
        'notes': lead.notes,
        'tags': lead.tags,
        'customFields': lead.customFields,
      });
    }
    
    return data;
  }

  static String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static Future<String> _saveToFile(String fileName, String content) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsString(content);
    return file.path;
  }

  static Map<String, int> _calculateScoreDistribution(List<LeadEntity> leads) {
    final distribution = <String, int>{
      '0-20': 0,
      '21-40': 0,
      '41-60': 0,
      '61-80': 0,
      '81-100': 0,
    };
    
    for (final lead in leads) {
      final score = lead.score ?? 0;
      if (score <= 20) {
        distribution['0-20'] = distribution['0-20']! + 1;
      } else if (score <= 40) {
        distribution['21-40'] = distribution['21-40']! + 1;
      } else if (score <= 60) {
        distribution['41-60'] = distribution['41-60']! + 1;
      } else if (score <= 80) {
        distribution['61-80'] = distribution['61-80']! + 1;
      } else {
        distribution['81-100'] = distribution['81-100']! + 1;
      }
    }
    
    return distribution;
  }

  static Map<String, int> _calculateExperienceDistribution(List<LeadEntity> leads) {
    final distribution = <String, int>{};
    
    for (final lead in leads) {
      final experience = lead.investmentExperience?.displayName ?? 'Unknown';
      distribution[experience] = (distribution[experience] ?? 0) + 1;
    }
    
    return distribution;
  }

  static Map<String, int> _calculatePortfolioSizeDistribution(List<LeadEntity> leads) {
    final distribution = <String, int>{};
    
    for (final lead in leads) {
      final portfolioSize = lead.portfolioSize?.displayName ?? 'Unknown';
      distribution[portfolioSize] = (distribution[portfolioSize] ?? 0) + 1;
    }
    
    return distribution;
  }

  static Map<String, int> _calculateRiskToleranceDistribution(List<LeadEntity> leads) {
    final distribution = <String, int>{};
    
    for (final lead in leads) {
      final riskTolerance = lead.riskTolerance?.displayName ?? 'Unknown';
      distribution[riskTolerance] = (distribution[riskTolerance] ?? 0) + 1;
    }
    
    return distribution;
  }
}
