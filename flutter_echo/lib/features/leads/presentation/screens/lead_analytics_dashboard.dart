/// Lead Analytics Dashboard Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive analytics dashboard for lead management and performance tracking.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../../../shared/widgets/error_boundary.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../../providers/lead_providers.dart';
import '../widgets/lead_score_visualization.dart';
import '../widgets/lead_statistics_chart.dart';
import '../widgets/lead_conversion_funnel.dart';

/// Lead analytics dashboard screen
class LeadAnalyticsDashboard extends ConsumerStatefulWidget {
  const LeadAnalyticsDashboard({super.key});

  @override
  ConsumerState<LeadAnalyticsDashboard> createState() => _LeadAnalyticsDashboardState();
}

class _LeadAnalyticsDashboardState extends ConsumerState<LeadAnalyticsDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateRange _selectedDateRange = DateRange.lastMonth;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ErrorBoundary(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Lead Analytics'),
          backgroundColor: AppColors.surface,
          foregroundColor: AppColors.textPrimary,
          elevation: 0,
          actions: [
            // Date range selector
            PopupMenuButton<DateRange>(
              icon: Icon(
                Icons.date_range,
                color: AppColors.textSecondary,
              ),
              onSelected: (range) {
                setState(() {
                  _selectedDateRange = range;
                });
              },
              itemBuilder: (context) => DateRange.values.map((range) {
                return PopupMenuItem(
                  value: range,
                  child: Text(range.displayName),
                );
              }).toList(),
            ),
            // Export button
            IconButton(
              onPressed: _exportData,
              icon: Icon(
                Icons.download,
                color: AppColors.textSecondary,
              ),
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(text: 'Overview'),
              Tab(text: 'Performance'),
              Tab(text: 'Conversion'),
              Tab(text: 'Insights'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(),
            _buildPerformanceTab(),
            _buildConversionTab(),
            _buildInsightsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    final statisticsAsync = ref.watch(leadStatisticsProvider(_selectedDateRange));
    
    return statisticsAsync.when(
      data: (statistics) => _buildOverviewContent(statistics),
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => _buildErrorState(error.toString()),
    );
  }

  Widget _buildOverviewContent(Map<String, dynamic> statistics) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key metrics cards
          _buildKeyMetricsGrid(statistics),
          
          const SizedBox(height: 24.0),
          
          // Lead distribution chart
          _buildLeadDistributionChart(statistics),
          
          const SizedBox(height: 24.0),
          
          // Recent leads list
          _buildRecentLeadsList(),
        ],
      ),
    );
  }

  Widget _buildKeyMetricsGrid(Map<String, dynamic> statistics) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16.0,
      mainAxisSpacing: 16.0,
      children: [
        _buildMetricCard(
          'Total Leads',
          '${statistics['totalLeads'] ?? 0}',
          Icons.people,
          AppColors.primary,
          '+${statistics['newLeadsThisPeriod'] ?? 0} this period',
        ),
        _buildMetricCard(
          'Qualified Leads',
          '${statistics['qualifiedLeads'] ?? 0}',
          Icons.verified,
          AppColors.success,
          '${((statistics['qualificationRate'] ?? 0) * 100).toStringAsFixed(1)}% rate',
        ),
        _buildMetricCard(
          'Avg Score',
          '${(statistics['averageScore'] ?? 0).toStringAsFixed(1)}',
          Icons.trending_up,
          AppColors.warning,
          '${statistics['scoreImprovement'] ?? 0}% improvement',
        ),
        _buildMetricCard(
          'Conversion Rate',
          '${((statistics['conversionRate'] ?? 0) * 100).toStringAsFixed(1)}%',
          Icons.conversion,
          AppColors.error,
          '${statistics['conversions'] ?? 0} conversions',
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: AppTheme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Icon(
                  icon,
                  color: color,
                  size: 20.0,
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Text(
              value,
              style: AppTheme.textTheme.headlineMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4.0),
            Text(
              subtitle,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeadDistributionChart(Map<String, dynamic> statistics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lead Distribution by Score',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            SizedBox(
              height: 200.0,
              child: LeadStatisticsChart(
                data: statistics['scoreDistribution'] ?? {},
                chartType: ChartType.bar,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentLeadsList() {
    final recentLeadsAsync = ref.watch(recentLeadsProvider(10));
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Leads',
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () => _viewAllLeads(),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            recentLeadsAsync.when(
              data: (leads) => Column(
                children: leads.take(5).map((lead) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getScoreColor(lead.score ?? 0).withOpacity(0.1),
                      child: Text(
                        '${(lead.score ?? 0).toInt()}',
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: _getScoreColor(lead.score ?? 0),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    title: Text(
                      lead.name ?? 'Anonymous',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      _formatDate(lead.createdAt),
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 4.0,
                      ),
                      decoration: BoxDecoration(
                        color: _getQualificationColor(lead.qualificationLevel).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Text(
                        lead.qualificationLevel?.name ?? 'Unqualified',
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: _getQualificationColor(lead.qualificationLevel),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    onTap: () => _viewLeadDetails(lead),
                  );
                }).toList(),
              ),
              loading: () => const LoadingIndicator(),
              error: (error, stackTrace) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return const Center(
      child: Text('Performance analytics coming soon'),
    );
  }

  Widget _buildConversionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Conversion Funnel',
                    style: AppTheme.textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  const SizedBox(
                    height: 300.0,
                    child: LeadConversionFunnel(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return const Center(
      child: Text('AI insights coming soon'),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.0,
            color: AppColors.error,
          ),
          const SizedBox(height: 16.0),
          Text(
            'Failed to load analytics',
            style: AppTheme.textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            error,
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return AppColors.success;
    if (score >= 60) return AppColors.warning;
    if (score >= 40) return AppColors.error.withOpacity(0.7);
    return AppColors.error;
  }

  Color _getQualificationColor(dynamic qualification) {
    // This would map to actual qualification levels
    return AppColors.primary;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Export functionality not yet implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _viewAllLeads() {
    // Navigate to leads list screen
  }

  void _viewLeadDetails(dynamic lead) {
    // Navigate to lead details screen
  }
}

/// Date range enumeration
enum DateRange {
  lastWeek,
  lastMonth,
  lastQuarter,
  lastYear,
  allTime,
}

/// Extension for date range
extension DateRangeExtension on DateRange {
  String get displayName {
    switch (this) {
      case DateRange.lastWeek:
        return 'Last Week';
      case DateRange.lastMonth:
        return 'Last Month';
      case DateRange.lastQuarter:
        return 'Last Quarter';
      case DateRange.lastYear:
        return 'Last Year';
      case DateRange.allTime:
        return 'All Time';
    }
  }

  DateTime get startDate {
    final now = DateTime.now();
    switch (this) {
      case DateRange.lastWeek:
        return now.subtract(const Duration(days: 7));
      case DateRange.lastMonth:
        return DateTime(now.year, now.month - 1, now.day);
      case DateRange.lastQuarter:
        return DateTime(now.year, now.month - 3, now.day);
      case DateRange.lastYear:
        return DateTime(now.year - 1, now.month, now.day);
      case DateRange.allTime:
        return DateTime(2020, 1, 1); // Arbitrary start date
    }
  }
}
