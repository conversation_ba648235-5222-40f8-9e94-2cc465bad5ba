/// Lead Statistics Chart Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Chart widget for displaying lead statistics and analytics data.

import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';

/// Lead statistics chart widget
class LeadStatisticsChart extends StatefulWidget {
  final Map<String, dynamic> data;
  final ChartType chartType;
  final bool showLegend;
  final bool animated;

  const LeadStatisticsChart({
    super.key,
    required this.data,
    this.chartType = ChartType.bar,
    this.showLegend = true,
    this.animated = true,
  });

  @override
  State<LeadStatisticsChart> createState() => _LeadStatisticsChartState();
}

class _LeadStatisticsChartState extends State<LeadStatisticsChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    if (widget.animated) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Chart area
        Expanded(
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                painter: _getChartPainter(),
                size: Size.infinite,
              );
            },
          ),
        ),
        
        // Legend
        if (widget.showLegend) ...[
          const SizedBox(height: 16.0),
          _buildLegend(),
        ],
      ],
    );
  }

  CustomPainter _getChartPainter() {
    switch (widget.chartType) {
      case ChartType.bar:
        return BarChartPainter(
          data: widget.data,
          animation: _animation.value,
        );
      case ChartType.pie:
        return PieChartPainter(
          data: widget.data,
          animation: _animation.value,
        );
      case ChartType.line:
        return LineChartPainter(
          data: widget.data,
          animation: _animation.value,
        );
    }
  }

  Widget _buildLegend() {
    final items = widget.data.entries.toList();
    
    return Wrap(
      spacing: 16.0,
      runSpacing: 8.0,
      children: items.map((entry) {
        final color = _getColorForIndex(items.indexOf(entry));
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.0,
              height: 12.0,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6.0),
            Text(
              entry.key,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.primary.withOpacity(0.7),
      AppColors.success.withOpacity(0.7),
    ];
    return colors[index % colors.length];
  }
}

/// Chart type enumeration
enum ChartType {
  bar,
  pie,
  line,
}

/// Bar chart painter
class BarChartPainter extends CustomPainter {
  final Map<String, dynamic> data;
  final double animation;

  BarChartPainter({
    required this.data,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final entries = data.entries.toList();
    final maxValue = entries.map((e) => (e.value as num).toDouble()).reduce(math.max);
    final barWidth = size.width / entries.length * 0.8;
    final spacing = size.width / entries.length * 0.2;

    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final value = (entry.value as num).toDouble();
      final normalizedValue = value / maxValue;
      final animatedHeight = size.height * normalizedValue * animation;

      final x = i * (barWidth + spacing) + spacing / 2;
      final y = size.height - animatedHeight;

      final paint = Paint()
        ..color = _getColorForIndex(i)
        ..style = PaintingStyle.fill;

      final rect = Rect.fromLTWH(x, y, barWidth, animatedHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4.0)),
        paint,
      );

      // Draw value label
      final textPainter = TextPainter(
        text: TextSpan(
          text: value.toInt().toString(),
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          x + barWidth / 2 - textPainter.width / 2,
          y - textPainter.height - 4,
        ),
      );
    }
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! BarChartPainter ||
           oldDelegate.data != data ||
           oldDelegate.animation != animation;
  }
}

/// Pie chart painter
class PieChartPainter extends CustomPainter {
  final Map<String, dynamic> data;
  final double animation;

  PieChartPainter({
    required this.data,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 * 0.8;
    final total = data.values.map((v) => (v as num).toDouble()).reduce((a, b) => a + b);

    double startAngle = -math.pi / 2;
    final entries = data.entries.toList();

    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final value = (entry.value as num).toDouble();
      final sweepAngle = (value / total) * 2 * math.pi * animation;

      final paint = Paint()
        ..color = _getColorForIndex(i)
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // Draw center circle for donut effect
    final centerPaint = Paint()
      ..color = AppColors.surface
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.5, centerPaint);
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! PieChartPainter ||
           oldDelegate.data != data ||
           oldDelegate.animation != animation;
  }
}

/// Line chart painter
class LineChartPainter extends CustomPainter {
  final Map<String, dynamic> data;
  final double animation;

  LineChartPainter({
    required this.data,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final entries = data.entries.toList();
    final maxValue = entries.map((e) => (e.value as num).toDouble()).reduce(math.max);
    final points = <Offset>[];

    // Calculate points
    for (int i = 0; i < entries.length; i++) {
      final value = (entries[i].value as num).toDouble();
      final x = size.width * i / (entries.length - 1);
      final y = size.height * (1 - (value / maxValue));
      points.add(Offset(x, y));
    }

    // Draw line
    final paint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, points.first.dy);
      
      final animatedLength = (points.length * animation).floor();
      for (int i = 1; i < animatedLength; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
    }

    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    final animatedLength = (points.length * animation).floor();
    for (int i = 0; i < animatedLength; i++) {
      canvas.drawCircle(points[i], 4.0, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! LineChartPainter ||
           oldDelegate.data != data ||
           oldDelegate.animation != animation;
  }
}
