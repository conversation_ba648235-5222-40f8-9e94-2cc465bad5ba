/// Lead Conversion Funnel Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget for visualizing lead conversion funnel with stages and conversion rates.

import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';

/// Lead conversion funnel widget
class LeadConversionFunnel extends StatefulWidget {
  final List<FunnelStage>? stages;
  final bool showPercentages;
  final bool animated;

  const LeadConversionFunnel({
    super.key,
    this.stages,
    this.showPercentages = true,
    this.animated = true,
  });

  @override
  State<LeadConversionFunnel> createState() => _LeadConversionFunnelState();
}

class _LeadConversionFunnelState extends State<LeadConversionFunnel>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  List<FunnelStage> _stages = [];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _stages = widget.stages ?? _getDefaultStages();
    
    if (widget.animated) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  List<FunnelStage> _getDefaultStages() {
    return [
      FunnelStage(
        name: 'Visitors',
        count: 1000,
        color: AppColors.primary,
        description: 'Total website visitors',
      ),
      FunnelStage(
        name: 'Engaged',
        count: 400,
        color: AppColors.primary.withOpacity(0.8),
        description: 'Started conversation',
      ),
      FunnelStage(
        name: 'Qualified',
        count: 150,
        color: AppColors.success,
        description: 'Provided investment info',
      ),
      FunnelStage(
        name: 'High Score',
        count: 60,
        color: AppColors.success.withOpacity(0.8),
        description: 'Score above 70',
      ),
      FunnelStage(
        name: 'Converted',
        count: 25,
        color: AppColors.warning,
        description: 'Became clients',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: FunnelPainter(
            stages: _stages,
            animation: _animation.value,
            showPercentages: widget.showPercentages,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

/// Funnel stage data class
class FunnelStage {
  final String name;
  final int count;
  final Color color;
  final String description;

  const FunnelStage({
    required this.name,
    required this.count,
    required this.color,
    required this.description,
  });

  double getConversionRate(FunnelStage previousStage) {
    if (previousStage.count == 0) return 0.0;
    return count / previousStage.count;
  }
}

/// Funnel painter
class FunnelPainter extends CustomPainter {
  final List<FunnelStage> stages;
  final double animation;
  final bool showPercentages;

  FunnelPainter({
    required this.stages,
    required this.animation,
    required this.showPercentages,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (stages.isEmpty) return;

    final maxCount = stages.first.count.toDouble();
    final stageHeight = size.height / stages.length;
    final maxWidth = size.width * 0.8;
    final centerX = size.width / 2;

    for (int i = 0; i < stages.length; i++) {
      final stage = stages[i];
      final y = i * stageHeight;
      final normalizedWidth = (stage.count / maxCount) * maxWidth;
      final animatedWidth = normalizedWidth * animation;

      // Draw funnel segment
      _drawFunnelSegment(
        canvas,
        centerX,
        y,
        animatedWidth,
        stageHeight,
        stage.color,
        i == stages.length - 1, // isLast
      );

      // Draw stage label
      _drawStageLabel(
        canvas,
        stage,
        centerX,
        y + stageHeight / 2,
        animatedWidth,
        i > 0 ? stages[i - 1] : null,
      );
    }
  }

  void _drawFunnelSegment(
    Canvas canvas,
    double centerX,
    double y,
    double width,
    double height,
    Color color,
    bool isLast,
  ) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final left = centerX - width / 2;
    final right = centerX + width / 2;
    final top = y;
    final bottom = y + height;

    final path = Path();
    
    if (isLast) {
      // Last segment is rectangular
      path.addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTRB(left, top, right, bottom),
          const Radius.circular(8.0),
        ),
      );
    } else {
      // Trapezoid shape for funnel effect
      final nextWidth = width * 0.8; // Next segment is 80% of current
      final nextLeft = centerX - nextWidth / 2;
      final nextRight = centerX + nextWidth / 2;
      
      path.moveTo(left, top);
      path.lineTo(right, top);
      path.lineTo(nextRight, bottom);
      path.lineTo(nextLeft, bottom);
      path.close();
    }

    canvas.drawPath(path, paint);

    // Draw border
    final borderPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawPath(path, borderPaint);
  }

  void _drawStageLabel(
    Canvas canvas,
    FunnelStage stage,
    double centerX,
    double centerY,
    double width,
    FunnelStage? previousStage,
  ) {
    // Stage name and count
    final nameTextPainter = TextPainter(
      text: TextSpan(
        text: stage.name,
        style: AppTheme.textTheme.titleSmall?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    nameTextPainter.layout();

    final countTextPainter = TextPainter(
      text: TextSpan(
        text: '${stage.count}',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    countTextPainter.layout();

    // Position labels
    final nameY = centerY - countTextPainter.height / 2 - 4;
    final countY = centerY - countTextPainter.height / 2 + nameTextPainter.height;

    nameTextPainter.paint(
      canvas,
      Offset(centerX - nameTextPainter.width / 2, nameY),
    );

    countTextPainter.paint(
      canvas,
      Offset(centerX - countTextPainter.width / 2, countY),
    );

    // Conversion rate (if not first stage)
    if (showPercentages && previousStage != null) {
      final conversionRate = stage.getConversionRate(previousStage);
      final percentageText = '${(conversionRate * 100).toStringAsFixed(1)}%';
      
      final percentageTextPainter = TextPainter(
        text: TextSpan(
          text: percentageText,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      percentageTextPainter.layout();

      // Position percentage to the right of the funnel
      final percentageX = centerX + width / 2 + 16;
      final percentageY = centerY - percentageTextPainter.height / 2;

      // Draw background circle
      final circlePaint = Paint()
        ..color = AppColors.surface
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(percentageX + percentageTextPainter.width / 2, percentageY + percentageTextPainter.height / 2),
        percentageTextPainter.width / 2 + 8,
        circlePaint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = AppColors.border.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawCircle(
        Offset(percentageX + percentageTextPainter.width / 2, percentageY + percentageTextPainter.height / 2),
        percentageTextPainter.width / 2 + 8,
        borderPaint,
      );

      percentageTextPainter.paint(canvas, Offset(percentageX, percentageY));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! FunnelPainter ||
           oldDelegate.stages != stages ||
           oldDelegate.animation != animation ||
           oldDelegate.showPercentages != showPercentages;
  }
}
