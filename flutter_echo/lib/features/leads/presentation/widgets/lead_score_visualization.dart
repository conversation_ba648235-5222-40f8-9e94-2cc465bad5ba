/// Lead Score Visualization Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget for visualizing lead scores with circular progress indicators and breakdown charts.

import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../services/lead_scoring_service.dart';

/// Lead score visualization widget
class LeadScoreVisualization extends StatefulWidget {
  final LeadScore leadScore;
  final bool showBreakdown;
  final bool showRecommendations;
  final double size;

  const LeadScoreVisualization({
    super.key,
    required this.leadScore,
    this.showBreakdown = true,
    this.showRecommendations = true,
    this.size = 200.0,
  });

  @override
  State<LeadScoreVisualization> createState() => _LeadScoreVisualizationState();
}

class _LeadScoreVisualizationState extends State<LeadScoreVisualization>
    with TickerProviderStateMixin {
  late AnimationController _scoreController;
  late AnimationController _breakdownController;
  late Animation<double> _scoreAnimation;
  late Animation<double> _breakdownAnimation;

  @override
  void initState() {
    super.initState();
    
    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _breakdownController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _scoreAnimation = Tween<double>(
      begin: 0.0,
      end: widget.leadScore.totalScore / 100.0,
    ).animate(CurvedAnimation(
      parent: _scoreController,
      curve: Curves.easeOutCubic,
    ));
    
    _breakdownAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _breakdownController,
      curve: Curves.easeOutCubic,
    ));
    
    // Start animations
    _scoreController.forward();
    if (widget.showBreakdown) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _breakdownController.forward();
      });
    }
  }

  @override
  void dispose() {
    _scoreController.dispose();
    _breakdownController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main score visualization
        _buildMainScoreVisualization(),
        
        if (widget.showBreakdown) ...[
          const SizedBox(height: 24.0),
          _buildScoreBreakdown(),
        ],
        
        if (widget.showRecommendations) ...[
          const SizedBox(height: 24.0),
          _buildRecommendations(),
        ],
      ],
    );
  }

  Widget _buildMainScoreVisualization() {
    return Center(
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: AnimatedBuilder(
          animation: _scoreAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: CircularScorePainter(
                progress: _scoreAnimation.value,
                score: widget.leadScore.totalScore,
                qualificationLevel: widget.leadScore.qualificationLevel,
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${(widget.leadScore.totalScore * _scoreAnimation.value).toInt()}',
                      style: AppTheme.textTheme.headlineLarge?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                        fontSize: widget.size * 0.15,
                      ),
                    ),
                    Text(
                      'SCORE',
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 1.2,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12.0,
                        vertical: 4.0,
                      ),
                      decoration: BoxDecoration(
                        color: _getQualificationColor().withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.0),
                        border: Border.all(
                          color: _getQualificationColor().withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        widget.leadScore.qualificationLevel.name,
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: _getQualificationColor(),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildScoreBreakdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Score Breakdown',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            AnimatedBuilder(
              animation: _breakdownAnimation,
              builder: (context, child) {
                return Column(
                  children: widget.leadScore.factors.map((factor) {
                    return _buildFactorRow(factor);
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFactorRow(ScoreFactor factor) {
    final animatedScore = factor.score * _breakdownAnimation.value;
    final animatedWeightedScore = factor.weightedScore * _breakdownAnimation.value;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  factor.category,
                  style: AppTheme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Text(
                '${animatedScore.toStringAsFixed(1)} (${(factor.weight * 100).toInt()}%)',
                style: AppTheme.textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4.0),
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: animatedScore / 100.0,
                  backgroundColor: AppColors.border.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getScoreColor(factor.score),
                  ),
                ),
              ),
              const SizedBox(width: 8.0),
              SizedBox(
                width: 40.0,
                child: Text(
                  '+${animatedWeightedScore.toStringAsFixed(1)}',
                  style: AppTheme.textTheme.bodySmall?.copyWith(
                    color: _getScoreColor(factor.score),
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
          if (factor.description.isNotEmpty) ...[
            const SizedBox(height: 4.0),
            Text(
              factor.description,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary.withOpacity(0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendations() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: AppColors.primary,
                  size: 20.0,
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Recommendations',
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            ...widget.leadScore.recommendations.map((recommendation) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 6.0,
                      height: 6.0,
                      margin: const EdgeInsets.only(top: 6.0, right: 8.0),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        recommendation,
                        style: AppTheme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Color _getQualificationColor() {
    switch (widget.leadScore.qualificationLevel) {
      case LeadQualificationLevel.high:
        return AppColors.success;
      case LeadQualificationLevel.medium:
        return AppColors.warning;
      case LeadQualificationLevel.low:
        return AppColors.error.withOpacity(0.7);
      case LeadQualificationLevel.veryLow:
        return AppColors.error;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return AppColors.success;
    if (score >= 60) return AppColors.warning;
    if (score >= 40) return AppColors.error.withOpacity(0.7);
    return AppColors.error;
  }
}

/// Circular score painter
class CircularScorePainter extends CustomPainter {
  final double progress;
  final double score;
  final LeadQualificationLevel qualificationLevel;

  CircularScorePainter({
    required this.progress,
    required this.score,
    required this.qualificationLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 20;
    
    // Background circle
    final backgroundPaint = Paint()
      ..color = AppColors.border.withOpacity(0.1)
      ..strokeWidth = 12.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    canvas.drawCircle(center, radius, backgroundPaint);
    
    // Progress arc
    final progressPaint = Paint()
      ..color = _getProgressColor()
      ..strokeWidth = 12.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    final startAngle = -math.pi / 2; // Start from top
    final sweepAngle = 2 * math.pi * progress;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
    
    // Gradient overlay for visual appeal
    if (progress > 0) {
      final gradientPaint = Paint()
        ..shader = RadialGradient(
          colors: [
            _getProgressColor().withOpacity(0.1),
            _getProgressColor().withOpacity(0.05),
            Colors.transparent,
          ],
        ).createShader(Rect.fromCircle(center: center, radius: radius));
      
      canvas.drawCircle(center, radius - 6, gradientPaint);
    }
  }

  Color _getProgressColor() {
    switch (qualificationLevel) {
      case LeadQualificationLevel.high:
        return AppColors.success;
      case LeadQualificationLevel.medium:
        return AppColors.warning;
      case LeadQualificationLevel.low:
        return AppColors.error.withOpacity(0.7);
      case LeadQualificationLevel.veryLow:
        return AppColors.error;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! CircularScorePainter ||
           oldDelegate.progress != progress ||
           oldDelegate.score != score ||
           oldDelegate.qualificationLevel != qualificationLevel;
  }
}
