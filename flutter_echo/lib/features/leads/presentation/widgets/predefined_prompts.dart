/// Predefined Prompts Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget for displaying predefined prompt suggestions to help users start conversations.

import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';

/// Predefined prompts widget with categorized suggestions
class PredefinedPrompts extends StatefulWidget {
  final Function(String) onPromptSelected;
  final bool showCategories;
  final int maxPromptsPerCategory;

  const PredefinedPrompts({
    super.key,
    required this.onPromptSelected,
    this.showCategories = true,
    this.maxPromptsPerCategory = 3,
  });

  @override
  State<PredefinedPrompts> createState() => _PredefinedPromptsState();
}

class _PredefinedPromptsState extends State<PredefinedPrompts> {
  PromptCategory _selectedCategory = PromptCategory.investmentGoals;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Text(
          'Suggested Questions',
          style: AppTheme.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8.0),
        Text(
          'Get started with these conversation starters',
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        
        const SizedBox(height: 16.0),
        
        // Category selector
        if (widget.showCategories) ...[
          _buildCategorySelector(),
          const SizedBox(height: 16.0),
        ],
        
        // Prompt chips
        _buildPromptChips(),
      ],
    );
  }

  Widget _buildCategorySelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: PromptCategory.values.map((category) {
          final isSelected = _selectedCategory == category;
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: FilterChip(
              label: Text(category.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedCategory = category;
                  });
                }
              },
              selectedColor: AppColors.primary.withOpacity(0.1),
              checkmarkColor: AppColors.primary,
              labelStyle: AppTheme.textTheme.bodySmall?.copyWith(
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected 
                    ? AppColors.primary 
                    : AppColors.border.withOpacity(0.3),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPromptChips() {
    final prompts = _getPromptsForCategory(_selectedCategory);
    final displayPrompts = prompts.take(widget.maxPromptsPerCategory).toList();
    
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: displayPrompts.map((prompt) {
        return _buildPromptChip(prompt);
      }).toList(),
    );
  }

  Widget _buildPromptChip(PromptSuggestion prompt) {
    return ActionChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            prompt.icon,
            style: const TextStyle(fontSize: 14.0),
          ),
          const SizedBox(width: 6.0),
          Flexible(
            child: Text(
              prompt.text,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppColors.textPrimary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      onPressed: () => widget.onPromptSelected(prompt.text),
      backgroundColor: AppColors.surface,
      side: BorderSide(
        color: AppColors.border.withOpacity(0.3),
      ),
      elevation: 0,
      pressElevation: 2,
    );
  }

  List<PromptSuggestion> _getPromptsForCategory(PromptCategory category) {
    switch (category) {
      case PromptCategory.investmentGoals:
        return [
          PromptSuggestion('🎯', 'What are your investment goals?'),
          PromptSuggestion('💰', 'How much are you looking to invest?'),
          PromptSuggestion('📈', 'Are you focused on growth or income?'),
          PromptSuggestion('🏠', 'Are you saving for retirement or a major purchase?'),
          PromptSuggestion('⏰', 'What\'s your investment timeline?'),
        ];
      
      case PromptCategory.riskTolerance:
        return [
          PromptSuggestion('⚖️', 'What\'s your risk tolerance?'),
          PromptSuggestion('📊', 'How do you handle market volatility?'),
          PromptSuggestion('🛡️', 'Do you prefer safe or aggressive investments?'),
          PromptSuggestion('💔', 'How much loss can you tolerate?'),
          PromptSuggestion('🎢', 'Are you comfortable with ups and downs?'),
        ];
      
      case PromptCategory.portfolioSize:
        return [
          PromptSuggestion('💼', 'What\'s your current portfolio size?'),
          PromptSuggestion('📊', 'How much do you have invested?'),
          PromptSuggestion('💵', 'What\'s your investment budget?'),
          PromptSuggestion('📈', 'How much can you invest monthly?'),
          PromptSuggestion('🏦', 'Do you have existing investments?'),
        ];
      
      case PromptCategory.experience:
        return [
          PromptSuggestion('🎓', 'What\'s your investment experience?'),
          PromptSuggestion('📚', 'Are you new to investing?'),
          PromptSuggestion('🏆', 'How long have you been investing?'),
          PromptSuggestion('🤔', 'What investment strategies do you know?'),
          PromptSuggestion('💡', 'What would you like to learn about?'),
        ];
      
      case PromptCategory.stockAnalysis:
        return [
          PromptSuggestion('🔍', 'Tell me about [stock ticker]'),
          PromptSuggestion('📊', 'What\'s your analysis of [company]?'),
          PromptSuggestion('💹', 'Should I buy, hold, or sell [stock]?'),
          PromptSuggestion('⚡', 'What are the latest trends in [sector]?'),
          PromptSuggestion('🎯', 'What\'s your price target for [stock]?'),
        ];
      
      case PromptCategory.marketInsights:
        return [
          PromptSuggestion('🌍', 'What\'s happening in the markets today?'),
          PromptSuggestion('📰', 'Any important market news?'),
          PromptSuggestion('🔮', 'What\'s your market outlook?'),
          PromptSuggestion('⚠️', 'What risks should I watch for?'),
          PromptSuggestion('🚀', 'What sectors are you bullish on?'),
        ];
    }
  }
}

/// Prompt category enumeration
enum PromptCategory {
  investmentGoals,
  riskTolerance,
  portfolioSize,
  experience,
  stockAnalysis,
  marketInsights,
}

/// Extension for prompt category
extension PromptCategoryExtension on PromptCategory {
  String get displayName {
    switch (this) {
      case PromptCategory.investmentGoals:
        return 'Goals';
      case PromptCategory.riskTolerance:
        return 'Risk';
      case PromptCategory.portfolioSize:
        return 'Portfolio';
      case PromptCategory.experience:
        return 'Experience';
      case PromptCategory.stockAnalysis:
        return 'Stocks';
      case PromptCategory.marketInsights:
        return 'Markets';
    }
  }

  String get icon {
    switch (this) {
      case PromptCategory.investmentGoals:
        return '🎯';
      case PromptCategory.riskTolerance:
        return '⚖️';
      case PromptCategory.portfolioSize:
        return '💼';
      case PromptCategory.experience:
        return '🎓';
      case PromptCategory.stockAnalysis:
        return '📊';
      case PromptCategory.marketInsights:
        return '🌍';
    }
  }

  String get description {
    switch (this) {
      case PromptCategory.investmentGoals:
        return 'Questions about investment objectives and goals';
      case PromptCategory.riskTolerance:
        return 'Questions about risk appetite and tolerance';
      case PromptCategory.portfolioSize:
        return 'Questions about portfolio size and budget';
      case PromptCategory.experience:
        return 'Questions about investment experience and knowledge';
      case PromptCategory.stockAnalysis:
        return 'Questions about specific stocks and analysis';
      case PromptCategory.marketInsights:
        return 'Questions about market trends and insights';
    }
  }
}

/// Prompt suggestion data class
class PromptSuggestion {
  final String icon;
  final String text;
  final String? category;
  final int priority;

  const PromptSuggestion(
    this.icon,
    this.text, {
    this.category,
    this.priority = 0,
  });

  /// Create a personalized prompt
  PromptSuggestion personalize({
    String? userName,
    String? portfolioSize,
    String? experience,
  }) {
    String personalizedText = text;
    
    // Replace placeholders with user data
    if (userName != null) {
      personalizedText = personalizedText.replaceAll('[user]', userName);
    }
    
    if (portfolioSize != null) {
      personalizedText = personalizedText.replaceAll('[portfolio]', portfolioSize);
    }
    
    if (experience != null) {
      personalizedText = personalizedText.replaceAll('[experience]', experience);
    }
    
    return PromptSuggestion(
      icon,
      personalizedText,
      category: category,
      priority: priority,
    );
  }
}

/// Predefined prompts service
class PredefinedPromptsService {
  /// Get prompts for a specific user context
  static List<PromptSuggestion> getContextualPrompts({
    String? userName,
    String? portfolioSize,
    String? experience,
    String? riskTolerance,
  }) {
    final prompts = <PromptSuggestion>[];
    
    // Add contextual prompts based on user data
    if (portfolioSize == null) {
      prompts.add(const PromptSuggestion('💼', 'What\'s your current portfolio size?'));
    }
    
    if (experience == null) {
      prompts.add(const PromptSuggestion('🎓', 'What\'s your investment experience level?'));
    }
    
    if (riskTolerance == null) {
      prompts.add(const PromptSuggestion('⚖️', 'What\'s your risk tolerance?'));
    }
    
    // Add general prompts
    prompts.addAll([
      const PromptSuggestion('🌍', 'What\'s happening in the markets today?'),
      const PromptSuggestion('🔍', 'Can you analyze a specific stock for me?'),
      const PromptSuggestion('💡', 'What investment strategies do you recommend?'),
    ]);
    
    return prompts;
  }

  /// Get trending prompts based on market conditions
  static List<PromptSuggestion> getTrendingPrompts() {
    return [
      const PromptSuggestion('🚀', 'What are the hottest stocks right now?'),
      const PromptSuggestion('📈', 'Which sectors are performing well?'),
      const PromptSuggestion('⚠️', 'What market risks should I watch?'),
      const PromptSuggestion('💰', 'Where should I invest $10,000?'),
      const PromptSuggestion('🔮', 'What\'s your market outlook for 2025?'),
    ];
  }

  /// Get prompts for beginners
  static List<PromptSuggestion> getBeginnerPrompts() {
    return [
      const PromptSuggestion('🎓', 'How do I start investing?'),
      const PromptSuggestion('📚', 'What are the basics I should know?'),
      const PromptSuggestion('💡', 'What\'s the difference between stocks and bonds?'),
      const PromptSuggestion('🛡️', 'How do I minimize investment risk?'),
      const PromptSuggestion('📊', 'What should I look for in a stock?'),
    ];
  }

  /// Get prompts for advanced users
  static List<PromptSuggestion> getAdvancedPrompts() {
    return [
      const PromptSuggestion('📊', 'Can you do a DCF analysis on [stock]?'),
      const PromptSuggestion('⚡', 'What\'s your take on options strategies?'),
      const PromptSuggestion('🌍', 'How do you view international diversification?'),
      const PromptSuggestion('🔍', 'What technical indicators do you use?'),
      const PromptSuggestion('💹', 'What\'s your sector rotation strategy?'),
    ];
  }
}
