/// Lead Providers
/// Copyright (c) 2025 Echo Inc.
/// 
/// Riverpod providers for lead-related services and state management.

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/network/api_client.dart';
import '../../../core/providers/app_providers.dart';
import '../data/repositories/lead_repository.dart';
import '../data/models/lead_entity.dart';
import '../services/lead_scoring_service.dart';
import '../services/lead_export_service.dart';
import '../presentation/screens/lead_analytics_dashboard.dart';

// Repository Providers

/// Lead repository provider
final leadRepositoryProvider = Provider<LeadRepository>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return LeadRepository(apiClient);
});

/// Lead scoring service provider
final leadScoringServiceProvider = Provider<LeadScoringService>((ref) {
  return LeadScoringService();
});

/// Lead export service provider
final leadExportServiceProvider = Provider<LeadExportService>((ref) {
  return LeadExportService();
});

// Data Providers

/// All leads provider
final allLeadsProvider = FutureProvider<List<LeadEntity>>((ref) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getAllLeads();
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Lead by ID provider
final leadByIdProvider = FutureProvider.family<LeadEntity?, String>((ref, leadId) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getLeadById(leadId);
  return result.fold(
    onSuccess: (lead) => lead,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Recent leads provider
final recentLeadsProvider = FutureProvider.family<List<LeadEntity>, int>((ref, limit) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getRecentLeads(limit: limit);
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Qualified leads provider
final qualifiedLeadsProvider = FutureProvider<List<LeadEntity>>((ref) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getQualifiedLeads();
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

/// High score leads provider
final highScoreLeadsProvider = FutureProvider.family<List<LeadEntity>, double>((ref, minScore) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getLeadsByScoreRange(minScore: minScore);
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Leads by status provider
final leadsByStatusProvider = FutureProvider.family<List<LeadEntity>, LeadStatus>((ref, status) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getLeadsByStatus(status);
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Lead search provider
final leadSearchProvider = FutureProvider.family<List<LeadEntity>, String>((ref, query) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.searchLeads(query);
  return result.fold(
    onSuccess: (leads) => leads,
    onError: (message, exception) => throw Exception(message),
  );
});

// Analytics Providers

/// Lead statistics provider
final leadStatisticsProvider = FutureProvider.family<Map<String, dynamic>, DateRange>((ref, dateRange) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getLeadStatistics(
    startDate: dateRange.startDate,
    endDate: DateTime.now(),
  );
  return result.fold(
    onSuccess: (stats) => stats,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Lead conversion metrics provider
final leadConversionMetricsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.watch(leadRepositoryProvider);
  final result = await repository.getConversionMetrics();
  return result.fold(
    onSuccess: (metrics) => metrics,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Lead scoring statistics provider
final leadScoringStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  return LeadScoringService.getScoringStatistics();
});

// Scoring Providers

/// Calculate lead score provider
final calculateLeadScoreProvider = FutureProvider.family<LeadScore, LeadEntity>((ref, lead) async {
  final result = LeadScoringService.calculateLeadScore(lead);
  return result.fold(
    onSuccess: (score) => score,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Bulk score calculation provider
final bulkScoreCalculationProvider = FutureProvider.family<List<LeadScore>, List<LeadEntity>>((ref, leads) async {
  final scores = <LeadScore>[];
  
  for (final lead in leads) {
    final result = LeadScoringService.calculateLeadScore(lead);
    if (result.isSuccess) {
      scores.add(result.data!);
    }
  }
  
  return scores;
});

// Export Providers

/// Export leads to CSV provider
final exportLeadsCSVProvider = FutureProvider.family<String, ExportOptions>((ref, options) async {
  final result = await LeadExportService.exportToCSV(
    leads: options.leads,
    includeScores: options.includeScores,
    includeMetadata: options.includeMetadata,
  );
  return result.fold(
    onSuccess: (filePath) => filePath,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Export leads to JSON provider
final exportLeadsJSONProvider = FutureProvider.family<String, ExportOptions>((ref, options) async {
  final result = await LeadExportService.exportToJSON(
    leads: options.leads,
    includeScores: options.includeScores,
    includeMetadata: options.includeMetadata,
    prettyPrint: options.prettyPrint,
  );
  return result.fold(
    onSuccess: (filePath) => filePath,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Export analytics summary provider
final exportAnalyticsSummaryProvider = FutureProvider.family<String, AnalyticsExportOptions>((ref, options) async {
  final result = await LeadExportService.exportAnalyticsSummary(
    leads: options.leads,
    statistics: options.statistics,
  );
  return result.fold(
    onSuccess: (filePath) => filePath,
    onError: (message, exception) => throw Exception(message),
  );
});

// State Providers

/// Selected lead provider
final selectedLeadProvider = StateProvider<LeadEntity?>((ref) => null);

/// Lead filter settings provider
final leadFilterSettingsProvider = StateProvider<LeadFilterSettings>((ref) {
  return const LeadFilterSettings();
});

/// Lead sort settings provider
final leadSortSettingsProvider = StateProvider<LeadSortSettings>((ref) {
  return const LeadSortSettings();
});

/// Lead search query provider
final leadSearchQueryProvider = StateProvider<String>((ref) => '');

// State Notifiers

/// Lead list notifier
final leadListNotifierProvider = StateNotifierProvider<LeadListNotifier, AsyncValue<List<LeadEntity>>>((ref) {
  final repository = ref.watch(leadRepositoryProvider);
  return LeadListNotifier(repository);
});

/// Lead filter notifier
final leadFilterNotifierProvider = StateNotifierProvider<LeadFilterNotifier, LeadFilterSettings>((ref) {
  return LeadFilterNotifier();
});

// State Notifier Classes

/// Lead list state notifier
class LeadListNotifier extends StateNotifier<AsyncValue<List<LeadEntity>>> {
  final LeadRepository _repository;

  LeadListNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadLeads();
  }

  Future<void> loadLeads() async {
    state = const AsyncValue.loading();
    
    final result = await _repository.getAllLeads();
    state = result.fold(
      onSuccess: (leads) => AsyncValue.data(leads),
      onError: (message, exception) => AsyncValue.error(Exception(message), StackTrace.current),
    );
  }

  Future<void> refreshLeads() async {
    await loadLeads();
  }

  Future<void> addLead(LeadEntity lead) async {
    final result = await _repository.saveLead(lead);
    if (result.isSuccess) {
      await loadLeads(); // Reload to get updated list
    }
  }

  Future<void> updateLead(LeadEntity lead) async {
    final result = await _repository.updateLead(lead);
    if (result.isSuccess) {
      await loadLeads(); // Reload to get updated list
    }
  }

  Future<void> deleteLead(String leadId) async {
    final result = await _repository.deleteLead(leadId);
    if (result.isSuccess) {
      await loadLeads(); // Reload to get updated list
    }
  }
}

/// Lead filter state notifier
class LeadFilterNotifier extends StateNotifier<LeadFilterSettings> {
  LeadFilterNotifier() : super(const LeadFilterSettings());

  void updateFilter(LeadFilterSettings newSettings) {
    state = newSettings;
  }

  void clearFilters() {
    state = const LeadFilterSettings();
  }

  void setStatusFilter(List<LeadStatus> statuses) {
    state = state.copyWith(statuses: statuses);
  }

  void setScoreRange(double? minScore, double? maxScore) {
    state = state.copyWith(minScore: minScore, maxScore: maxScore);
  }

  void setExperienceFilter(List<InvestmentExperience> experiences) {
    state = state.copyWith(experiences: experiences);
  }

  void setPortfolioSizeFilter(List<PortfolioSize> portfolioSizes) {
    state = state.copyWith(portfolioSizes: portfolioSizes);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }
}

// Settings Classes

/// Lead filter settings
class LeadFilterSettings {
  final List<LeadStatus> statuses;
  final List<InvestmentExperience> experiences;
  final List<PortfolioSize> portfolioSizes;
  final List<RiskTolerance> riskTolerances;
  final double? minScore;
  final double? maxScore;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String> tags;

  const LeadFilterSettings({
    this.statuses = const [],
    this.experiences = const [],
    this.portfolioSizes = const [],
    this.riskTolerances = const [],
    this.minScore,
    this.maxScore,
    this.startDate,
    this.endDate,
    this.tags = const [],
  });

  LeadFilterSettings copyWith({
    List<LeadStatus>? statuses,
    List<InvestmentExperience>? experiences,
    List<PortfolioSize>? portfolioSizes,
    List<RiskTolerance>? riskTolerances,
    double? minScore,
    double? maxScore,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? tags,
  }) {
    return LeadFilterSettings(
      statuses: statuses ?? this.statuses,
      experiences: experiences ?? this.experiences,
      portfolioSizes: portfolioSizes ?? this.portfolioSizes,
      riskTolerances: riskTolerances ?? this.riskTolerances,
      minScore: minScore ?? this.minScore,
      maxScore: maxScore ?? this.maxScore,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      tags: tags ?? this.tags,
    );
  }

  bool get hasActiveFilters {
    return statuses.isNotEmpty ||
           experiences.isNotEmpty ||
           portfolioSizes.isNotEmpty ||
           riskTolerances.isNotEmpty ||
           minScore != null ||
           maxScore != null ||
           startDate != null ||
           endDate != null ||
           tags.isNotEmpty;
  }
}

/// Lead sort settings
class LeadSortSettings {
  final LeadSortBy sortBy;
  final bool ascending;

  const LeadSortSettings({
    this.sortBy = LeadSortBy.createdAt,
    this.ascending = false,
  });

  LeadSortSettings copyWith({
    LeadSortBy? sortBy,
    bool? ascending,
  }) {
    return LeadSortSettings(
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }
}

/// Lead sort options
enum LeadSortBy {
  createdAt,
  lastContactAt,
  name,
  score,
  messageCount,
  status,
}

/// Export options
class ExportOptions {
  final List<LeadEntity> leads;
  final bool includeScores;
  final bool includeMetadata;
  final bool prettyPrint;

  const ExportOptions({
    required this.leads,
    this.includeScores = true,
    this.includeMetadata = false,
    this.prettyPrint = true,
  });
}

/// Analytics export options
class AnalyticsExportOptions {
  final List<LeadEntity> leads;
  final Map<String, dynamic> statistics;

  const AnalyticsExportOptions({
    required this.leads,
    required this.statistics,
  });
}
