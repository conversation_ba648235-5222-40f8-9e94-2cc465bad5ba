/// Chat Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for managing chat functionality and AI interactions.

import '../../../core/utils/logger.dart';
import '../../../core/utils/result.dart';

/// Chat service for managing conversations and AI interactions
class ChatService {
  static bool _isInitialized = false;

  /// Initialize chat service
  static Future<void> initialize() async {
    try {
      if (_isInitialized) {
        return;
      }

      Logger.info('Initializing chat service...');
      
      // Initialize any chat-specific configurations
      
      _isInitialized = true;
      Logger.info('Chat service initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize chat service', e, stackTrace);
      rethrow;
    }
  }

  /// Check if service is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
