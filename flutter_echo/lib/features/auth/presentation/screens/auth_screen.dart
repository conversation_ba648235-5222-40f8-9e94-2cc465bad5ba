/// Authentication Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// Screen for biometric authentication and session creation.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/colors.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../shared/widgets/loading_overlay.dart';

/// Authentication screen for biometric auth and session creation
class AuthScreen extends ConsumerStatefulWidget {
  const AuthScreen({super.key});

  @override
  ConsumerState<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends ConsumerState<AuthScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  bool _biometricAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkBiometricAvailability();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoadingStateWrapper(
        isLoading: _isLoading,
        loadingMessage: 'Authenticating...',
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                _buildHeader(),
                
                const SizedBox(height: AppConstants.largePadding * 2),
                
                // Authentication options
                _buildAuthOptions(),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Error message
                if (_errorMessage != null) _buildErrorMessage(),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Skip option (development only)
                if (AppConstants.isDevelopment) _buildSkipOption(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.fingerprint,
          size: 80,
          color: AppColors.primary,
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        Text(
          'Secure Authentication',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          'Use biometric authentication to securely access your account',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAuthOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Biometric authentication
        if (_biometricAvailable) ...[
          LoadingButton(
            text: 'Authenticate with Biometrics',
            isLoading: _isLoading,
            onPressed: _authenticateWithBiometrics,
            icon: const Icon(Icons.fingerprint),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          Row(
            children: [
              const Expanded(child: Divider()),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'OR',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ),
              const Expanded(child: Divider()),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
        ],
        
        // Continue without biometrics
        OutlinedButton(
          onPressed: _isLoading ? null : _continueWithoutBiometrics,
          child: Text(_biometricAvailable 
              ? 'Continue without Biometrics' 
              : 'Continue to Echo'),
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.errorLight,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkipOption() {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: AppConstants.defaultPadding),
        Text(
          'Development Mode',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textTertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        TextButton(
          onPressed: _skipAuthentication,
          child: const Text('Skip Authentication'),
        ),
      ],
    );
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      final biometricService = ref.read(biometricAuthServiceProvider);
      final isAvailable = await biometricService.isBiometricAvailable();
      
      if (mounted) {
        setState(() {
          _biometricAvailable = isAvailable;
        });
      }
    } catch (e) {
      // Biometric not available
      setState(() {
        _biometricAvailable = false;
      });
    }
  }

  Future<void> _authenticateWithBiometrics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final biometricService = ref.read(biometricAuthServiceProvider);
      
      final result = await biometricService.authenticateWithBiometrics(
        reason: 'Authenticate to access Echo platform',
        sensitiveTransaction: true,
      );
      
      if (result.isSuccess) {
        await _createSession();
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'Biometric authentication failed';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Authentication error: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _continueWithoutBiometrics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _createSession();
    } catch (e) {
      setState(() {
        _errorMessage = 'Session creation failed: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _skipAuthentication() async {
    // Development only - skip authentication
    setState(() {
      _isLoading = true;
    });

    await Future.delayed(const Duration(milliseconds: 500));

    // Set authenticated state
    ref.read(sessionStateProvider.notifier).setSession(
      sessionId: 'dev_session_${DateTime.now().millisecondsSinceEpoch}',
      accessToken: 'dev_token',
    );

    if (mounted) {
      context.go('/');
    }
  }

  Future<void> _createSession() async {
    try {
      // In a real app, this would call the mobile session API
      // For now, simulate session creation
      await Future.delayed(const Duration(seconds: 1));
      
      final sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
      final accessToken = 'token_${DateTime.now().millisecondsSinceEpoch}';
      
      // Update session state
      ref.read(sessionStateProvider.notifier).setSession(
        sessionId: sessionId,
        accessToken: accessToken,
      );
      
      // Navigate to main app
      if (mounted) {
        context.go('/');
      }
    } catch (e) {
      throw Exception('Failed to create session: $e');
    }
  }
}
