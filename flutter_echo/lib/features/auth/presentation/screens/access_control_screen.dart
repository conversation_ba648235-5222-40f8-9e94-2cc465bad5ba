/// Access Control Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// Screen for access password verification before app access.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/colors.dart';
import '../../../../shared/widgets/loading_overlay.dart';

/// Access control screen for password verification
class AccessControlScreen extends ConsumerStatefulWidget {
  const AccessControlScreen({super.key});

  @override
  ConsumerState<AccessControlScreen> createState() => _AccessControlScreenState();
}

class _AccessControlScreenState extends ConsumerState<AccessControlScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoadingStateWrapper(
        isLoading: _isLoading,
        loadingMessage: 'Verifying access...',
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo and title
                _buildHeader(),
                
                const SizedBox(height: AppConstants.largePadding * 2),
                
                // Access form
                _buildAccessForm(),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Help text
                _buildHelpText(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App logo/icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppColors.primaryGradient,
          ),
          child: const Icon(
            Icons.graphic_eq,
            size: 40,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        Text(
          AppConstants.appDisplayName,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          'Voice-driven investment discovery platform',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAccessForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Enter Access Password',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Access Password',
              hintText: 'Enter your access password',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              errorText: _errorMessage,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the access password';
              }
              return null;
            },
            onFieldSubmitted: (_) => _verifyAccess(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          LoadingButton(
            text: 'Access Echo',
            isLoading: _isLoading,
            onPressed: _verifyAccess,
            icon: const Icon(Icons.arrow_forward),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpText() {
    return Column(
      children: [
        Text(
          'This is a private investment platform.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textTertiary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          'Contact support if you need access.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textTertiary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        TextButton(
          onPressed: _showContactInfo,
          child: const Text('Contact Support'),
        ),
      ],
    );
  }

  Future<void> _verifyAccess() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final password = _passwordController.text.trim();
      
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Check password (in real app, this would be an API call)
      if (password == AppConstants.accessPassword) {
        // Success - update session state and navigate
        ref.read(sessionStateProvider.notifier).setAccessPermission(true);
        
        if (mounted) {
          context.go('/auth');
        }
      } else {
        setState(() {
          _errorMessage = 'Invalid access password';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Access verification failed. Please try again.';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showContactInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'For access to Echo platform, please contact:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                const Icon(Icons.email_outlined, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppConstants.supportEmail,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Row(
              children: [
                const Icon(Icons.web_outlined, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppConstants.companyWebsite,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Import the session state provider (this would be in the actual providers file)
import '../../../../core/providers/app_providers.dart';
