/// Onboarding Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// User onboarding flow with tutorial screens and feature introduction.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/accessibility/accessibility_service.dart';
import '../../../core/haptics/haptic_feedback_service.dart';
import '../../../core/analytics/analytics_service.dart';
import '../../../core/feature_flags/feature_flags_service.dart';

/// Onboarding screen with step-by-step introduction
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentPage = 0;
  bool _isLastPage = false;

  @override
  void initState() {
    super.initState();
    
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    
    // Track onboarding start
    AnalyticsService.instance.trackEvent('onboarding_started');
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onboardingSteps = _getOnboardingSteps();
    
    return Scaffold(
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // Skip button
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'Skip',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ),
              ),
              
              // Page content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: onboardingSteps.length,
                  itemBuilder: (context, index) {
                    return _buildOnboardingPage(onboardingSteps[index]);
                  },
                ),
              ),
              
              // Bottom navigation
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    // Page indicator
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        onboardingSteps.length,
                        (index) => _buildPageIndicator(index),
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Navigation buttons
                    Row(
                      children: [
                        // Back button
                        if (_currentPage > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousPage,
                              child: const Text('Back'),
                            ),
                          ),
                        
                        if (_currentPage > 0) const SizedBox(width: 16),
                        
                        // Next/Get Started button
                        Expanded(
                          flex: _currentPage == 0 ? 1 : 1,
                          child: ElevatedButton(
                            onPressed: _isLastPage ? _completeOnboarding : _nextPage,
                            child: Text(_isLastPage ? 'Get Started' : 'Next'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingStep step) {
    final theme = Theme.of(context);
    final accessibility = AccessibilityService.instance;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration
          Container(
            height: 300,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: theme.colorScheme.surfaceVariant,
            ),
            child: step.illustration ?? Icon(
              step.icon,
              size: 120,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 48),
          
          // Title
          Text(
            step.title,
            style: accessibility.getAccessibleTextTheme(theme.textTheme).headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            step.description,
            style: accessibility.getAccessibleTextTheme(theme.textTheme).bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Additional content
          if (step.additionalContent != null) ...[
            const SizedBox(height: 24),
            step.additionalContent!,
          ],
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final theme = Theme.of(context);
    final isActive = index == _currentPage;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      width: isActive ? 24.0 : 8.0,
      height: 8.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.0),
        color: isActive 
            ? theme.colorScheme.primary 
            : theme.colorScheme.outline.withOpacity(0.3),
      ),
    );
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
      _isLastPage = page == _getOnboardingSteps().length - 1;
    });
    
    // Haptic feedback
    HapticFeedbackService.instance.selection();
    
    // Track page view
    AnalyticsService.instance.trackEvent('onboarding_page_viewed', {
      'page_index': page,
      'page_title': _getOnboardingSteps()[page].title,
    });
  }

  void _nextPage() {
    if (_currentPage < _getOnboardingSteps().length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    // Track skip action
    AnalyticsService.instance.trackEvent('onboarding_skipped', {
      'page_index': _currentPage,
    });
    
    _completeOnboarding();
  }

  void _completeOnboarding() {
    // Track completion
    AnalyticsService.instance.trackEvent('onboarding_completed', {
      'completed_pages': _currentPage + 1,
      'total_pages': _getOnboardingSteps().length,
    });
    
    // Haptic feedback
    HapticFeedbackService.instance.success();
    
    // Navigate to main app
    context.go('/chat');
  }

  List<OnboardingStep> _getOnboardingSteps() {
    final featureFlags = FeatureFlagsService.instance;
    final variant = featureFlags.onboardingFlowVariant;
    
    switch (variant) {
      case 'simplified':
        return _getSimplifiedSteps();
      case 'guided':
        return _getGuidedSteps();
      default:
        return _getDefaultSteps();
    }
  }

  List<OnboardingStep> _getDefaultSteps() {
    return [
      OnboardingStep(
        title: 'Welcome to Echo',
        description: 'Your AI-powered investment discovery platform for elite investors.',
        icon: Icons.waving_hand,
        additionalContent: _buildWelcomeContent(),
      ),
      OnboardingStep(
        title: 'Voice-Driven Conversations',
        description: 'Speak naturally about your investment interests and let our AI understand your preferences.',
        icon: Icons.mic,
        additionalContent: _buildVoiceFeatureDemo(),
      ),
      OnboardingStep(
        title: 'Smart Stock Detection',
        description: 'Our AI automatically detects stock mentions in conversations and provides instant insights.',
        icon: Icons.trending_up,
        additionalContent: _buildStockDetectionDemo(),
      ),
      OnboardingStep(
        title: 'Lead Qualification',
        description: 'Advanced scoring system to identify and prioritize high-value investment opportunities.',
        icon: Icons.analytics,
        additionalContent: _buildLeadScoringDemo(),
      ),
      OnboardingStep(
        title: 'Ready to Start',
        description: 'You\'re all set! Start your first conversation to discover investment opportunities.',
        icon: Icons.rocket_launch,
        additionalContent: _buildGetStartedContent(),
      ),
    ];
  }

  List<OnboardingStep> _getSimplifiedSteps() {
    return [
      OnboardingStep(
        title: 'Welcome to Echo',
        description: 'AI-powered investment discovery made simple.',
        icon: Icons.waving_hand,
      ),
      OnboardingStep(
        title: 'Talk About Investments',
        description: 'Use voice or text to discuss stocks and investment ideas.',
        icon: Icons.chat,
      ),
      OnboardingStep(
        title: 'Get Smart Insights',
        description: 'Receive AI-powered analysis and recommendations.',
        icon: Icons.lightbulb,
      ),
    ];
  }

  List<OnboardingStep> _getGuidedSteps() {
    return [
      OnboardingStep(
        title: 'Welcome to Echo',
        description: 'Let\'s take a guided tour of your new investment platform.',
        icon: Icons.tour,
        additionalContent: _buildGuidedTourStart(),
      ),
      OnboardingStep(
        title: 'Voice Features',
        description: 'Try recording a voice message about your investment interests.',
        icon: Icons.mic,
        additionalContent: _buildInteractiveVoiceDemo(),
      ),
      OnboardingStep(
        title: 'Stock Discovery',
        description: 'See how Echo detects and analyzes stock mentions.',
        icon: Icons.search,
        additionalContent: _buildInteractiveStockDemo(),
      ),
      OnboardingStep(
        title: 'Your Dashboard',
        description: 'Explore your personalized investment dashboard.',
        icon: Icons.dashboard,
        additionalContent: _buildDashboardPreview(),
      ),
      OnboardingStep(
        title: 'Start Investing',
        description: 'You\'re ready to begin your investment journey with Echo.',
        icon: Icons.trending_up,
        additionalContent: _buildInvestmentGoalsSetup(),
      ),
    ];
  }

  Widget _buildWelcomeContent() {
    return Column(
      children: [
        const Icon(Icons.handshake, size: 48, color: Colors.blue),
        const SizedBox(height: 16),
        Text(
          'Built for sophisticated investors who value efficiency and insight.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontStyle: FontStyle.italic,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildVoiceFeatureDemo() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        children: [
          Icon(Icons.mic, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 12),
          const Expanded(
            child: Text('"I\'m interested in tech stocks with strong growth potential"'),
          ),
        ],
      ),
    );
  }

  Widget _buildStockDetectionDemo() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          const Text('Mention: "I think AAPL is undervalued"'),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.trending_up, size: 16),
                SizedBox(width: 4),
                Text('AAPL detected'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeadScoringDemo() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        children: [
          CircularProgressIndicator(
            value: 0.85,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Lead Score: 85/100'),
                Text('High Priority', style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGetStartedContent() {
    return ElevatedButton.icon(
      onPressed: () {
        // Quick action to start first conversation
        _completeOnboarding();
      },
      icon: const Icon(Icons.chat),
      label: const Text('Start First Conversation'),
    );
  }

  Widget _buildGuidedTourStart() {
    return const Text('We\'ll walk you through each feature step by step.');
  }

  Widget _buildInteractiveVoiceDemo() {
    return ElevatedButton.icon(
      onPressed: () {
        // Demo voice recording
        HapticFeedbackService.instance.voiceStart();
      },
      icon: const Icon(Icons.mic),
      label: const Text('Try Voice Recording'),
    );
  }

  Widget _buildInteractiveStockDemo() {
    return const Text('Type a stock symbol to see instant analysis.');
  }

  Widget _buildDashboardPreview() {
    return const Text('Your personalized dashboard awaits.');
  }

  Widget _buildInvestmentGoalsSetup() {
    return const Text('Set your investment goals to get started.');
  }
}

/// Onboarding step model
class OnboardingStep {
  final String title;
  final String description;
  final IconData icon;
  final Widget? illustration;
  final Widget? additionalContent;

  OnboardingStep({
    required this.title,
    required this.description,
    required this.icon,
    this.illustration,
    this.additionalContent,
  });
}
