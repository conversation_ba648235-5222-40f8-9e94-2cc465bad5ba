/// Voice Providers
/// Copyright (c) 2025 Echo Inc.
/// 
/// Riverpod providers for voice-related services and state management.

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/network/api_client.dart';
import '../../../core/network/websocket_client.dart';
import '../../../core/providers/app_providers.dart';
import '../data/repositories/voice_repository.dart';
import '../data/models/audio_recording_entity.dart';
import '../data/models/audio_playback_entity.dart';
import '../services/audio_recording_service.dart';
import '../services/speech_to_text_service.dart';
import '../services/text_to_speech_service.dart';
import '../services/voice_activity_detection_service.dart';
import '../services/voice_streaming_service.dart';

// Repository Providers

/// Voice repository provider
final voiceRepositoryProvider = Provider<VoiceRepository>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return VoiceRepository(apiClient);
});

// Service Providers

/// Audio recording service provider
final audioRecordingServiceProvider = Provider<AudioRecordingService>((ref) {
  return AudioRecordingService();
});

/// Speech-to-text service provider
final speechToTextServiceProvider = Provider<SpeechToTextService>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  final dio = ref.watch(dioProvider);
  return SpeechToTextService(apiClient, dio);
});

/// Text-to-speech service provider
final textToSpeechServiceProvider = Provider<TextToSpeechService>((ref) {
  return TextToSpeechService();
});

/// Voice activity detection service provider
final voiceActivityDetectionServiceProvider = Provider<VoiceActivityDetectionService>((ref) {
  return VoiceActivityDetectionService();
});

/// Voice streaming service provider
final voiceStreamingServiceProvider = Provider<VoiceStreamingService>((ref) {
  final webSocketClient = ref.watch(webSocketClientProvider);
  return VoiceStreamingService(webSocketClient);
});

// State Providers

/// Audio recording state provider
final audioRecordingStateProvider = StateNotifierProvider<AudioRecordingStateNotifier, AudioRecordingState>((ref) {
  return AudioRecordingStateNotifier();
});

/// Audio playback state provider
final audioPlaybackStateProvider = StateNotifierProvider<AudioPlaybackStateNotifier, AudioPlaybackState>((ref) {
  return AudioPlaybackStateNotifier();
});

/// Voice activity detection state provider
final voiceActivityStateProvider = StateNotifierProvider<VoiceActivityStateNotifier, VoiceActivityState>((ref) {
  return VoiceActivityStateNotifier();
});

/// Voice streaming state provider
final voiceStreamingStateProvider = StateNotifierProvider<VoiceStreamingStateNotifier, VoiceStreamState>((ref) {
  return VoiceStreamingStateNotifier();
});

// Data Providers

/// All audio recordings provider
final audioRecordingsProvider = FutureProvider<List<AudioRecordingEntity>>((ref) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getAllRecordings();
  return result.fold(
    onSuccess: (recordings) => recordings,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Audio recordings by conversation provider
final audioRecordingsByConversationProvider = FutureProvider.family<List<AudioRecordingEntity>, String>((ref, conversationId) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getRecordingsByConversation(conversationId);
  return result.fold(
    onSuccess: (recordings) => recordings,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Unprocessed audio recordings provider
final unprocessedRecordingsProvider = FutureProvider<List<AudioRecordingEntity>>((ref) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getUnprocessedRecordings();
  return result.fold(
    onSuccess: (recordings) => recordings,
    onError: (message, exception) => throw Exception(message),
  );
});

/// All audio playbacks provider
final audioPlaybacksProvider = FutureProvider<List<AudioPlaybackEntity>>((ref) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getAllPlaybacks();
  return result.fold(
    onSuccess: (playbacks) => playbacks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Audio playbacks by conversation provider
final audioPlaybacksByConversationProvider = FutureProvider.family<List<AudioPlaybackEntity>, String>((ref, conversationId) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getPlaybacksByConversation(conversationId);
  return result.fold(
    onSuccess: (playbacks) => playbacks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Voice storage statistics provider
final voiceStorageStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.watch(voiceRepositoryProvider);
  final result = await repository.getStorageStatistics();
  return result.fold(
    onSuccess: (stats) => stats,
    onError: (message, exception) => throw Exception(message),
  );
});

// State Notifiers

/// Audio recording state notifier
class AudioRecordingStateNotifier extends StateNotifier<AudioRecordingState> {
  AudioRecordingStateNotifier() : super(AudioRecordingState.stopped);

  void updateState(AudioRecordingState newState) {
    state = newState;
  }

  void startRecording() {
    state = AudioRecordingState.recording;
  }

  void pauseRecording() {
    state = AudioRecordingState.paused;
  }

  void stopRecording() {
    state = AudioRecordingState.stopped;
  }

  void setProcessing() {
    state = AudioRecordingState.processing;
  }

  void setCompleted() {
    state = AudioRecordingState.completed;
  }

  void setError() {
    state = AudioRecordingState.error;
  }
}

/// Audio playback state notifier
class AudioPlaybackStateNotifier extends StateNotifier<AudioPlaybackState> {
  AudioPlaybackStateNotifier() : super(AudioPlaybackState.stopped);

  void updateState(AudioPlaybackState newState) {
    state = newState;
  }

  void startPlaying() {
    state = AudioPlaybackState.playing;
  }

  void pausePlaying() {
    state = AudioPlaybackState.paused;
  }

  void stopPlaying() {
    state = AudioPlaybackState.stopped;
  }

  void setLoading() {
    state = AudioPlaybackState.loading;
  }

  void setCompleted() {
    state = AudioPlaybackState.completed;
  }

  void setError() {
    state = AudioPlaybackState.error;
  }
}

/// Voice activity detection state notifier
class VoiceActivityStateNotifier extends StateNotifier<VoiceActivityState> {
  VoiceActivityStateNotifier() : super(VoiceActivityState.silence);

  void updateState(VoiceActivityState newState) {
    state = newState;
  }

  void startMonitoring() {
    state = VoiceActivityState.monitoring;
  }

  void detectVoice() {
    state = VoiceActivityState.voice;
  }

  void detectSilence() {
    state = VoiceActivityState.silence;
  }

  void setPossibleSilence() {
    state = VoiceActivityState.possibleSilence;
  }
}

/// Voice streaming state notifier
class VoiceStreamingStateNotifier extends StateNotifier<VoiceStreamState> {
  VoiceStreamingStateNotifier() : super(VoiceStreamState.disconnected);

  void updateState(VoiceStreamState newState) {
    state = newState;
  }

  void connect() {
    state = VoiceStreamState.connected;
  }

  void startStreaming() {
    state = VoiceStreamState.streaming;
  }

  void pauseStreaming() {
    state = VoiceStreamState.paused;
  }

  void disconnect() {
    state = VoiceStreamState.disconnected;
  }

  void setError() {
    state = VoiceStreamState.error;
  }
}

// Stream Providers

/// Audio recording amplitude stream provider
final audioRecordingAmplitudeProvider = StreamProvider<double>((ref) {
  return AudioRecordingService.amplitudeStream;
});

/// Audio recording duration stream provider
final audioRecordingDurationProvider = StreamProvider<Duration>((ref) {
  return AudioRecordingService.durationStream;
});

/// Voice activity events stream provider
final voiceActivityEventsProvider = StreamProvider<VoiceActivityEvent>((ref) {
  return VoiceActivityDetectionService.eventStream;
});

/// Voice streaming events stream provider
final voiceStreamingEventsProvider = StreamProvider<VoiceStreamEvent>((ref) {
  return VoiceStreamingService.eventStream;
});

// Utility Providers

/// Current recording provider
final currentRecordingProvider = StateProvider<AudioRecordingEntity?>((ref) => null);

/// Current playback provider
final currentPlaybackProvider = StateProvider<AudioPlaybackEntity?>((ref) => null);

/// Recording settings provider
final recordingSettingsProvider = StateProvider<RecordingSettings>((ref) {
  return const RecordingSettings();
});

/// Playback settings provider
final playbackSettingsProvider = StateProvider<PlaybackSettings>((ref) {
  return const PlaybackSettings();
});

// Settings Classes

/// Recording settings
class RecordingSettings {
  final Duration maxDuration;
  final bool autoStop;
  final bool noiseReduction;
  final double silenceThreshold;
  final bool vadEnabled;

  const RecordingSettings({
    this.maxDuration = const Duration(minutes: 10),
    this.autoStop = true,
    this.noiseReduction = true,
    this.silenceThreshold = -40.0,
    this.vadEnabled = true,
  });

  RecordingSettings copyWith({
    Duration? maxDuration,
    bool? autoStop,
    bool? noiseReduction,
    double? silenceThreshold,
    bool? vadEnabled,
  }) {
    return RecordingSettings(
      maxDuration: maxDuration ?? this.maxDuration,
      autoStop: autoStop ?? this.autoStop,
      noiseReduction: noiseReduction ?? this.noiseReduction,
      silenceThreshold: silenceThreshold ?? this.silenceThreshold,
      vadEnabled: vadEnabled ?? this.vadEnabled,
    );
  }
}

/// Playback settings
class PlaybackSettings {
  final double speed;
  final TTSVoice voice;
  final TTSModel model;
  final bool autoPlay;
  final bool cacheEnabled;

  const PlaybackSettings({
    this.speed = 1.0,
    this.voice = TTSVoice.alloy,
    this.model = TTSModel.tts1,
    this.autoPlay = false,
    this.cacheEnabled = true,
  });

  PlaybackSettings copyWith({
    double? speed,
    TTSVoice? voice,
    TTSModel? model,
    bool? autoPlay,
    bool? cacheEnabled,
  }) {
    return PlaybackSettings(
      speed: speed ?? this.speed,
      voice: voice ?? this.voice,
      model: model ?? this.model,
      autoPlay: autoPlay ?? this.autoPlay,
      cacheEnabled: cacheEnabled ?? this.cacheEnabled,
    );
  }
}
