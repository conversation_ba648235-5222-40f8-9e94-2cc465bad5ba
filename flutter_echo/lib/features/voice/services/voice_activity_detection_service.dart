/// Voice Activity Detection Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for detecting voice activity and managing automatic recording.

import 'dart:async';
import 'dart:math';
import 'package:flutter_sound/flutter_sound.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/constants/app_constants.dart';

/// Service for voice activity detection
class VoiceActivityDetectionService {
  static FlutterSoundRecorder? _recorder;
  static StreamSubscription<RecordingDisposition>? _vadSubscription;
  static StreamController<VoiceActivityEvent>? _eventController;
  
  static bool _isInitialized = false;
  static bool _isMonitoring = false;
  static VoiceActivityState _currentState = VoiceActivityState.silence;
  
  // VAD configuration
  static double _silenceThreshold = -40.0; // dB
  static double _voiceThreshold = -30.0; // dB
  static Duration _silenceTimeout = const Duration(seconds: 2);
  static Duration _voiceMinDuration = const Duration(milliseconds: 500);
  static Duration _noiseGateDelay = const Duration(milliseconds: 100);
  
  // State tracking
  static DateTime? _voiceStartTime;
  static DateTime? _silenceStartTime;
  static Timer? _silenceTimer;
  static Timer? _voiceTimer;
  static List<double> _amplitudeBuffer = [];
  static const int _bufferSize = 10;

  /// Initialize voice activity detection
  static Future<Result<void>> initialize({
    double? silenceThreshold,
    double? voiceThreshold,
    Duration? silenceTimeout,
    Duration? voiceMinDuration,
  }) async {
    try {
      if (_isInitialized) {
        return Result.success(null);
      }

      Logger.info('Initializing voice activity detection...');
      
      // Configure thresholds
      if (silenceThreshold != null) _silenceThreshold = silenceThreshold;
      if (voiceThreshold != null) _voiceThreshold = voiceThreshold;
      if (silenceTimeout != null) _silenceTimeout = silenceTimeout;
      if (voiceMinDuration != null) _voiceMinDuration = voiceMinDuration;
      
      // Initialize recorder for monitoring
      _recorder = FlutterSoundRecorder();
      await _recorder!.openRecorder();
      
      // Initialize event controller
      _eventController = StreamController<VoiceActivityEvent>.broadcast();
      
      _isInitialized = true;
      Logger.info('Voice activity detection initialized');
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize voice activity detection', e, stackTrace);
      return Result.error('VAD initialization failed', 
                         RepositoryException('VAD init error', originalError: e));
    }
  }

  /// Start voice activity monitoring
  static Future<Result<void>> startMonitoring() async {
    try {
      if (!_isInitialized || _recorder == null) {
        return Result.error('VAD service not initialized');
      }

      if (_isMonitoring) {
        return Result.error('VAD monitoring already active');
      }

      Logger.debug('Starting voice activity monitoring...');
      
      // Start recording for monitoring (no file output)
      await _recorder!.startRecorder(
        toStream: _vadSubscription?.cancel,
        codec: Codec.pcm16,
        sampleRate: AppConstants.voiceRecordingSampleRate,
        numChannels: 1,
      );

      // Subscribe to audio stream
      _vadSubscription = _recorder!.onProgress!.listen(
        _processAudioData,
        onError: _handleVADError,
      );

      _isMonitoring = true;
      _currentState = VoiceActivityState.monitoring;
      _emitEvent(VoiceActivityEvent.monitoringStarted);
      
      Logger.debug('Voice activity monitoring started');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to start VAD monitoring', e, stackTrace);
      return Result.error('VAD monitoring start failed', 
                         RepositoryException('VAD start error', originalError: e));
    }
  }

  /// Stop voice activity monitoring
  static Future<Result<void>> stopMonitoring() async {
    try {
      if (!_isMonitoring) {
        return Result.success(null);
      }

      Logger.debug('Stopping voice activity monitoring...');
      
      await _recorder!.stopRecorder();
      await _vadSubscription?.cancel();
      _vadSubscription = null;
      
      _cancelTimers();
      _resetState();
      
      _isMonitoring = false;
      _emitEvent(VoiceActivityEvent.monitoringStopped);
      
      Logger.debug('Voice activity monitoring stopped');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to stop VAD monitoring', e, stackTrace);
      return Result.error('VAD monitoring stop failed', 
                         RepositoryException('VAD stop error', originalError: e));
    }
  }

  /// Process audio data for voice activity detection
  static void _processAudioData(RecordingDisposition disposition) {
    final amplitude = disposition.decibels ?? -60.0;
    
    // Add to buffer for smoothing
    _amplitudeBuffer.add(amplitude);
    if (_amplitudeBuffer.length > _bufferSize) {
      _amplitudeBuffer.removeAt(0);
    }
    
    // Calculate smoothed amplitude
    final smoothedAmplitude = _amplitudeBuffer.reduce((a, b) => a + b) / _amplitudeBuffer.length;
    
    // Detect voice activity
    _detectVoiceActivity(smoothedAmplitude);
    
    // Emit amplitude event
    _emitEvent(VoiceActivityEvent.amplitudeUpdate, data: {'amplitude': smoothedAmplitude});
  }

  /// Detect voice activity based on amplitude
  static void _detectVoiceActivity(double amplitude) {
    final now = DateTime.now();
    
    switch (_currentState) {
      case VoiceActivityState.monitoring:
      case VoiceActivityState.silence:
        if (amplitude > _voiceThreshold) {
          _onVoiceDetected(now);
        }
        break;
        
      case VoiceActivityState.voice:
        if (amplitude < _silenceThreshold) {
          _onSilenceDetected(now);
        } else {
          // Reset silence timer if voice continues
          _silenceTimer?.cancel();
          _silenceStartTime = null;
        }
        break;
        
      case VoiceActivityState.possibleSilence:
        if (amplitude > _voiceThreshold) {
          // Voice resumed, cancel silence detection
          _silenceTimer?.cancel();
          _currentState = VoiceActivityState.voice;
          _silenceStartTime = null;
        }
        break;
    }
  }

  /// Handle voice detection
  static void _onVoiceDetected(DateTime timestamp) {
    if (_currentState == VoiceActivityState.silence || 
        _currentState == VoiceActivityState.monitoring) {
      
      _voiceStartTime = timestamp;
      _currentState = VoiceActivityState.voice;
      
      // Start voice timer to ensure minimum duration
      _voiceTimer = Timer(_voiceMinDuration, () {
        if (_currentState == VoiceActivityState.voice) {
          _emitEvent(VoiceActivityEvent.voiceStarted, data: {
            'startTime': _voiceStartTime!.toIso8601String(),
          });
        }
      });
      
      Logger.debug('Voice activity detected');
    }
  }

  /// Handle silence detection
  static void _onSilenceDetected(DateTime timestamp) {
    if (_currentState == VoiceActivityState.voice) {
      _silenceStartTime = timestamp;
      _currentState = VoiceActivityState.possibleSilence;
      
      // Start silence timer
      _silenceTimer = Timer(_silenceTimeout, () {
        _onSilenceConfirmed();
      });
      
      Logger.debug('Possible silence detected');
    }
  }

  /// Handle confirmed silence
  static void _onSilenceConfirmed() {
    if (_voiceStartTime != null && _silenceStartTime != null) {
      final voiceDuration = _silenceStartTime!.difference(_voiceStartTime!);
      
      if (voiceDuration >= _voiceMinDuration) {
        _emitEvent(VoiceActivityEvent.voiceEnded, data: {
          'startTime': _voiceStartTime!.toIso8601String(),
          'endTime': _silenceStartTime!.toIso8601String(),
          'duration': voiceDuration.inMilliseconds,
        });
      }
    }
    
    _currentState = VoiceActivityState.silence;
    _voiceStartTime = null;
    _silenceStartTime = null;
    
    Logger.debug('Silence confirmed');
  }

  /// Handle VAD error
  static void _handleVADError(dynamic error) {
    Logger.error('Voice activity detection error', error);
    _emitEvent(VoiceActivityEvent.error, data: {'error': error.toString()});
  }

  /// Emit VAD event
  static void _emitEvent(VoiceActivityEvent event, {Map<String, dynamic>? data}) {
    _eventController?.add(event);
    
    // Log significant events
    switch (event) {
      case VoiceActivityEvent.voiceStarted:
      case VoiceActivityEvent.voiceEnded:
        Logger.debug('VAD Event: ${event.name}${data != null ? ' - $data' : ''}');
        break;
      default:
        break;
    }
  }

  /// Cancel all timers
  static void _cancelTimers() {
    _silenceTimer?.cancel();
    _voiceTimer?.cancel();
    _silenceTimer = null;
    _voiceTimer = null;
  }

  /// Reset VAD state
  static void _resetState() {
    _currentState = VoiceActivityState.silence;
    _voiceStartTime = null;
    _silenceStartTime = null;
    _amplitudeBuffer.clear();
  }

  /// Configure VAD sensitivity
  static void configureSensitivity({
    double? silenceThreshold,
    double? voiceThreshold,
    Duration? silenceTimeout,
    Duration? voiceMinDuration,
  }) {
    if (silenceThreshold != null) {
      _silenceThreshold = silenceThreshold.clamp(-60.0, 0.0);
      Logger.debug('VAD silence threshold set to: $_silenceThreshold dB');
    }
    
    if (voiceThreshold != null) {
      _voiceThreshold = voiceThreshold.clamp(-60.0, 0.0);
      Logger.debug('VAD voice threshold set to: $_voiceThreshold dB');
    }
    
    if (silenceTimeout != null) {
      _silenceTimeout = silenceTimeout;
      Logger.debug('VAD silence timeout set to: ${_silenceTimeout.inMilliseconds}ms');
    }
    
    if (voiceMinDuration != null) {
      _voiceMinDuration = voiceMinDuration;
      Logger.debug('VAD voice min duration set to: ${_voiceMinDuration.inMilliseconds}ms');
    }
  }

  /// Get current VAD state
  static VoiceActivityState get currentState => _currentState;

  /// Get VAD event stream
  static Stream<VoiceActivityEvent> get eventStream => 
      _eventController?.stream ?? const Stream.empty();

  /// Check if monitoring is active
  static bool get isMonitoring => _isMonitoring;

  /// Get current configuration
  static Map<String, dynamic> get configuration => {
    'silenceThreshold': _silenceThreshold,
    'voiceThreshold': _voiceThreshold,
    'silenceTimeout': _silenceTimeout.inMilliseconds,
    'voiceMinDuration': _voiceMinDuration.inMilliseconds,
    'bufferSize': _bufferSize,
  };

  /// Dispose VAD service
  static Future<void> dispose() async {
    try {
      await stopMonitoring();
      await _recorder?.closeRecorder();
      await _eventController?.close();
      
      _recorder = null;
      _eventController = null;
      _isInitialized = false;
      
      Logger.info('Voice activity detection service disposed');
    } catch (e, stackTrace) {
      Logger.error('Failed to dispose VAD service', e, stackTrace);
    }
  }
}

/// Voice activity state enumeration
enum VoiceActivityState {
  monitoring,
  silence,
  voice,
  possibleSilence,
}

/// Voice activity event enumeration
enum VoiceActivityEvent {
  monitoringStarted,
  monitoringStopped,
  voiceStarted,
  voiceEnded,
  amplitudeUpdate,
  error,
}

/// Extension for voice activity state
extension VoiceActivityStateExtension on VoiceActivityState {
  String get displayName {
    switch (this) {
      case VoiceActivityState.monitoring:
        return 'Monitoring';
      case VoiceActivityState.silence:
        return 'Silence';
      case VoiceActivityState.voice:
        return 'Voice Detected';
      case VoiceActivityState.possibleSilence:
        return 'Possible Silence';
    }
  }

  bool get isVoiceActive => this == VoiceActivityState.voice;
  bool get isSilent => this == VoiceActivityState.silence;
}
