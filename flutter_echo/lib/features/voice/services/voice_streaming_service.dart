/// Voice Streaming Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for real-time voice streaming via WebSocket.

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_sound/flutter_sound.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/network/websocket_client.dart';
import '../../../core/constants/app_constants.dart';

/// Service for real-time voice streaming
class VoiceStreamingService {
  final WebSocketClient _webSocketClient;
  
  static FlutterSoundRecorder? _recorder;
  static StreamSubscription<Food>? _streamSubscription;
  static StreamController<VoiceStreamEvent>? _eventController;
  
  static bool _isInitialized = false;
  static bool _isStreaming = false;
  static VoiceStreamState _currentState = VoiceStreamState.disconnected;
  static String? _currentSessionId;
  
  // Streaming configuration
  static int _chunkSize = 4096; // bytes
  static Duration _chunkInterval = const Duration(milliseconds: 100);
  static Timer? _chunkTimer;
  static List<int> _audioBuffer = [];

  VoiceStreamingService(this._webSocketClient);

  /// Initialize voice streaming service
  static Future<Result<void>> initialize(WebSocketClient webSocketClient) async {
    try {
      if (_isInitialized) {
        return Result.success(null);
      }

      Logger.info('Initializing voice streaming service...');
      
      // Initialize recorder for streaming
      _recorder = FlutterSoundRecorder();
      await _recorder!.openRecorder();
      
      // Initialize event controller
      _eventController = StreamController<VoiceStreamEvent>.broadcast();
      
      _isInitialized = true;
      Logger.info('Voice streaming service initialized');
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize voice streaming service', e, stackTrace);
      return Result.error('Voice streaming initialization failed', 
                         RepositoryException('Streaming init error', originalError: e));
    }
  }

  /// Start voice streaming
  Future<Result<void>> startStreaming({
    String? sessionId,
    int? chunkSize,
    Duration? chunkInterval,
  }) async {
    try {
      if (!_isInitialized || _recorder == null) {
        return Result.error('Voice streaming service not initialized');
      }

      if (_isStreaming) {
        return Result.error('Voice streaming already active');
      }

      // Configure streaming parameters
      if (chunkSize != null) _chunkSize = chunkSize;
      if (chunkInterval != null) _chunkInterval = chunkInterval;
      _currentSessionId = sessionId ?? DateTime.now().millisecondsSinceEpoch.toString();

      Logger.info('Starting voice streaming (session: $_currentSessionId)...');
      
      // Check WebSocket connection
      if (!_webSocketClient.isConnected) {
        final connectResult = await _webSocketClient.connect();
        if (connectResult.isError) {
          return Result.error('Failed to connect WebSocket: ${connectResult.errorMessage}');
        }
      }

      // Start recording to stream
      await _recorder!.startRecorder(
        toStream: _handleAudioStream,
        codec: Codec.pcm16,
        sampleRate: AppConstants.voiceRecordingSampleRate,
        numChannels: 1,
      );

      // Start chunk timer for buffered streaming
      _startChunkTimer();
      
      _isStreaming = true;
      _currentState = VoiceStreamState.streaming;
      _emitEvent(VoiceStreamEvent.streamingStarted);
      
      // Send streaming start message
      await _sendStreamingMessage({
        'type': 'voice_stream_start',
        'sessionId': _currentSessionId,
        'config': {
          'sampleRate': AppConstants.voiceRecordingSampleRate,
          'channels': 1,
          'format': 'pcm16',
          'chunkSize': _chunkSize,
        },
      });

      Logger.info('Voice streaming started');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to start voice streaming', e, stackTrace);
      _currentState = VoiceStreamState.error;
      return Result.error('Voice streaming start failed', 
                         RepositoryException('Streaming start error', originalError: e));
    }
  }

  /// Stop voice streaming
  Future<Result<void>> stopStreaming() async {
    try {
      if (!_isStreaming) {
        return Result.success(null);
      }

      Logger.info('Stopping voice streaming...');
      
      // Stop recording
      await _recorder!.stopRecorder();
      
      // Stop chunk timer
      _chunkTimer?.cancel();
      _chunkTimer = null;
      
      // Send remaining buffered data
      if (_audioBuffer.isNotEmpty) {
        await _sendAudioChunk(_audioBuffer);
        _audioBuffer.clear();
      }
      
      // Send streaming stop message
      await _sendStreamingMessage({
        'type': 'voice_stream_stop',
        'sessionId': _currentSessionId,
      });
      
      _isStreaming = false;
      _currentState = VoiceStreamState.connected;
      _currentSessionId = null;
      _emitEvent(VoiceStreamEvent.streamingStopped);
      
      Logger.info('Voice streaming stopped');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to stop voice streaming', e, stackTrace);
      return Result.error('Voice streaming stop failed', 
                         RepositoryException('Streaming stop error', originalError: e));
    }
  }

  /// Handle audio stream data
  static void _handleAudioStream(Food audioData) {
    if (!_isStreaming) return;
    
    try {
      // Add audio data to buffer
      _audioBuffer.addAll(audioData.data!);
      
      // Emit audio data event
      _emitEvent(VoiceStreamEvent.audioData, data: {
        'size': audioData.data!.length,
        'bufferSize': _audioBuffer.length,
      });
      
    } catch (e) {
      Logger.error('Error handling audio stream data', e);
      _emitEvent(VoiceStreamEvent.error, data: {'error': e.toString()});
    }
  }

  /// Start chunk timer for buffered streaming
  static void _startChunkTimer() {
    _chunkTimer = Timer.periodic(_chunkInterval, (timer) async {
      if (_audioBuffer.length >= _chunkSize) {
        // Extract chunk from buffer
        final chunk = _audioBuffer.take(_chunkSize).toList();
        _audioBuffer.removeRange(0, _chunkSize);
        
        // Send chunk
        await _sendAudioChunk(chunk);
      }
    });
  }

  /// Send audio chunk via WebSocket
  static Future<void> _sendAudioChunk(List<int> audioData) async {
    try {
      final message = {
        'type': 'voice_data',
        'sessionId': _currentSessionId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'data': base64Encode(audioData),
        'size': audioData.length,
      };
      
      // Send via WebSocket (this would need to be implemented in WebSocketClient)
      // await _webSocketClient.sendMessage(jsonEncode(message));
      
      _emitEvent(VoiceStreamEvent.chunkSent, data: {
        'size': audioData.length,
        'timestamp': message['timestamp'],
      });
      
    } catch (e) {
      Logger.error('Failed to send audio chunk', e);
      _emitEvent(VoiceStreamEvent.error, data: {'error': e.toString()});
    }
  }

  /// Send streaming control message
  Future<void> _sendStreamingMessage(Map<String, dynamic> message) async {
    try {
      await _webSocketClient.sendMessage(jsonEncode(message));
    } catch (e) {
      Logger.error('Failed to send streaming message', e);
      throw RepositoryException('Streaming message error', originalError: e);
    }
  }

  /// Pause voice streaming
  Future<Result<void>> pauseStreaming() async {
    try {
      if (!_isStreaming) {
        return Result.error('Voice streaming not active');
      }

      await _recorder!.pauseRecorder();
      _chunkTimer?.cancel();
      
      _currentState = VoiceStreamState.paused;
      _emitEvent(VoiceStreamEvent.streamingPaused);
      
      await _sendStreamingMessage({
        'type': 'voice_stream_pause',
        'sessionId': _currentSessionId,
      });
      
      Logger.debug('Voice streaming paused');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to pause voice streaming', e, stackTrace);
      return Result.error('Voice streaming pause failed', 
                         RepositoryException('Streaming pause error', originalError: e));
    }
  }

  /// Resume voice streaming
  Future<Result<void>> resumeStreaming() async {
    try {
      if (_currentState != VoiceStreamState.paused) {
        return Result.error('Voice streaming not paused');
      }

      await _recorder!.resumeRecorder();
      _startChunkTimer();
      
      _currentState = VoiceStreamState.streaming;
      _emitEvent(VoiceStreamEvent.streamingResumed);
      
      await _sendStreamingMessage({
        'type': 'voice_stream_resume',
        'sessionId': _currentSessionId,
      });
      
      Logger.debug('Voice streaming resumed');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to resume voice streaming', e, stackTrace);
      return Result.error('Voice streaming resume failed', 
                         RepositoryException('Streaming resume error', originalError: e));
    }
  }

  /// Configure streaming parameters
  static void configureStreaming({
    int? chunkSize,
    Duration? chunkInterval,
  }) {
    if (chunkSize != null) {
      _chunkSize = chunkSize.clamp(1024, 16384); // 1KB to 16KB
      Logger.debug('Voice streaming chunk size set to: $_chunkSize bytes');
    }
    
    if (chunkInterval != null) {
      _chunkInterval = chunkInterval;
      Logger.debug('Voice streaming chunk interval set to: ${_chunkInterval.inMilliseconds}ms');
    }
  }

  /// Emit streaming event
  static void _emitEvent(VoiceStreamEvent event, {Map<String, dynamic>? data}) {
    _eventController?.add(event);
    
    // Log significant events
    switch (event) {
      case VoiceStreamEvent.streamingStarted:
      case VoiceStreamEvent.streamingStopped:
      case VoiceStreamEvent.streamingPaused:
      case VoiceStreamEvent.streamingResumed:
      case VoiceStreamEvent.error:
        Logger.debug('Voice Stream Event: ${event.name}${data != null ? ' - $data' : ''}');
        break;
      default:
        break;
    }
  }

  /// Get current streaming state
  static VoiceStreamState get currentState => _currentState;

  /// Get streaming event stream
  static Stream<VoiceStreamEvent> get eventStream => 
      _eventController?.stream ?? const Stream.empty();

  /// Check if streaming is active
  static bool get isStreaming => _isStreaming;

  /// Get current session ID
  static String? get currentSessionId => _currentSessionId;

  /// Get streaming statistics
  static Map<String, dynamic> get statistics => {
    'isStreaming': _isStreaming,
    'currentState': _currentState.name,
    'sessionId': _currentSessionId,
    'chunkSize': _chunkSize,
    'chunkInterval': _chunkInterval.inMilliseconds,
    'bufferSize': _audioBuffer.length,
  };

  /// Dispose voice streaming service
  static Future<void> dispose() async {
    try {
      await stopStreaming();
      await _recorder?.closeRecorder();
      await _eventController?.close();
      
      _recorder = null;
      _eventController = null;
      _isInitialized = false;
      
      Logger.info('Voice streaming service disposed');
    } catch (e, stackTrace) {
      Logger.error('Failed to dispose voice streaming service', e, stackTrace);
    }
  }
}

/// Voice streaming state enumeration
enum VoiceStreamState {
  disconnected,
  connected,
  streaming,
  paused,
  error,
}

/// Voice streaming event enumeration
enum VoiceStreamEvent {
  streamingStarted,
  streamingStopped,
  streamingPaused,
  streamingResumed,
  audioData,
  chunkSent,
  error,
}

/// Extension for voice streaming state
extension VoiceStreamStateExtension on VoiceStreamState {
  String get displayName {
    switch (this) {
      case VoiceStreamState.disconnected:
        return 'Disconnected';
      case VoiceStreamState.connected:
        return 'Connected';
      case VoiceStreamState.streaming:
        return 'Streaming';
      case VoiceStreamState.paused:
        return 'Paused';
      case VoiceStreamState.error:
        return 'Error';
    }
  }

  bool get isActive => this == VoiceStreamState.streaming;
  bool get canStream => this == VoiceStreamState.connected || this == VoiceStreamState.paused;
}
