/// Text-to-Speech Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for converting text to speech using OpenAI TTS API and audio playback.

import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/constants/app_constants.dart';
import '../data/models/audio_playback_entity.dart';

/// Service for text-to-speech conversion and playback
class TextToSpeechService {
  static FlutterSoundPlayer? _player;
  static bool _isInitialized = false;
  static AudioPlaybackState _currentState = AudioPlaybackState.stopped;
  static String? _currentAudioPath;
  static Duration? _currentDuration;
  static Duration _currentPosition = Duration.zero;

  /// Initialize text-to-speech service
  static Future<Result<void>> initialize() async {
    try {
      if (_isInitialized) {
        return Result.success(null);
      }

      Logger.info('Initializing text-to-speech service...');
      
      _player = FlutterSoundPlayer();
      await _player!.openPlayer();
      
      // Configure platform-specific settings
      await _configurePlatformSpecificSettings();
      
      _isInitialized = true;
      Logger.info('Text-to-speech service initialized successfully');
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize text-to-speech service', e, stackTrace);
      return Result.error('TTS initialization failed', 
                         RepositoryException('TTS init error', originalError: e));
    }
  }

  /// Generate speech from text using OpenAI TTS
  static Future<Result<AudioPlaybackEntity>> generateSpeech({
    required String text,
    TTSVoice voice = TTSVoice.alloy,
    TTSModel model = TTSModel.tts1,
    double speed = 1.0,
    String? fileName,
  }) async {
    try {
      Logger.info('Generating speech for text: ${text.substring(0, text.length > 50 ? 50 : text.length)}...');
      
      // Validate input
      if (text.trim().isEmpty) {
        return Result.error('Text cannot be empty');
      }

      if (text.length > 4096) {
        return Result.error('Text too long (max 4096 characters)');
      }

      // Prepare request data
      final requestData = {
        'model': model.value,
        'input': text,
        'voice': voice.value,
        'speed': speed.clamp(0.25, 4.0),
        'response_format': 'mp3',
      };

      // Make API request
      final audioData = await _makeTTSRequest(requestData);
      if (audioData.isError) {
        return Result.error(audioData.errorMessage!);
      }

      // Save audio file
      final audioPath = await _saveAudioFile(audioData.data!, fileName);
      if (audioPath.isError) {
        return Result.error(audioPath.errorMessage!);
      }

      // Create audio playback entity
      final entity = AudioPlaybackEntity(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        filePath: audioPath.data!,
        fileName: audioPath.data!.split('/').last,
        text: text,
        voice: voice,
        model: model,
        speed: speed,
        createdAt: DateTime.now(),
        fileSize: audioData.data!.length,
      );

      Logger.info('Speech generated successfully: ${entity.fileName} (${entity.fileSizeKB} KB)');
      return Result.success(entity);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to generate speech', e, stackTrace);
      return Result.error('Speech generation failed', 
                         NetworkException('TTS generation error', originalError: e));
    }
  }

  /// Play audio from file path
  static Future<Result<void>> playAudio(String audioPath) async {
    try {
      if (!_isInitialized || _player == null) {
        return Result.error('TTS service not initialized');
      }

      if (_currentState == AudioPlaybackState.playing) {
        await stopAudio();
      }

      _currentAudioPath = audioPath;
      _currentState = AudioPlaybackState.loading;

      await _player!.startPlayer(
        fromURI: audioPath,
        codec: Codec.mp3,
        whenFinished: () {
          _currentState = AudioPlaybackState.completed;
          _currentPosition = Duration.zero;
        },
      );

      _currentState = AudioPlaybackState.playing;
      
      // Get audio duration
      _currentDuration = await _getAudioDuration(audioPath);
      
      Logger.debug('Audio playback started: $audioPath');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to play audio', e, stackTrace);
      _currentState = AudioPlaybackState.error;
      return Result.error('Audio playback failed', 
                         RepositoryException('Playback error', originalError: e));
    }
  }

  /// Play text directly (generate and play)
  static Future<Result<void>> speakText({
    required String text,
    TTSVoice voice = TTSVoice.alloy,
    TTSModel model = TTSModel.tts1,
    double speed = 1.0,
    bool cache = true,
  }) async {
    try {
      // Check cache first if enabled
      if (cache) {
        final cachedAudio = await _getCachedAudio(text, voice, speed);
        if (cachedAudio.isSuccess && cachedAudio.data != null) {
          return await playAudio(cachedAudio.data!);
        }
      }

      // Generate speech
      final audioResult = await generateSpeech(
        text: text,
        voice: voice,
        model: model,
        speed: speed,
      );

      if (audioResult.isError) {
        return Result.error(audioResult.errorMessage!);
      }

      // Play generated audio
      return await playAudio(audioResult.data!.filePath);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to speak text', e, stackTrace);
      return Result.error('Text-to-speech failed', 
                         RepositoryException('Speak error', originalError: e));
    }
  }

  /// Pause audio playback
  static Future<Result<void>> pauseAudio() async {
    try {
      if (_currentState != AudioPlaybackState.playing) {
        return Result.error('No audio playing');
      }

      await _player!.pausePlayer();
      _currentState = AudioPlaybackState.paused;
      
      Logger.debug('Audio playback paused');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to pause audio', e, stackTrace);
      return Result.error('Audio pause failed', 
                         RepositoryException('Pause error', originalError: e));
    }
  }

  /// Resume audio playback
  static Future<Result<void>> resumeAudio() async {
    try {
      if (_currentState != AudioPlaybackState.paused) {
        return Result.error('Audio not paused');
      }

      await _player!.resumePlayer();
      _currentState = AudioPlaybackState.playing;
      
      Logger.debug('Audio playback resumed');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to resume audio', e, stackTrace);
      return Result.error('Audio resume failed', 
                         RepositoryException('Resume error', originalError: e));
    }
  }

  /// Stop audio playback
  static Future<Result<void>> stopAudio() async {
    try {
      if (_currentState == AudioPlaybackState.stopped) {
        return Result.success(null);
      }

      await _player!.stopPlayer();
      _currentState = AudioPlaybackState.stopped;
      _currentPosition = Duration.zero;
      _currentAudioPath = null;
      _currentDuration = null;
      
      Logger.debug('Audio playback stopped');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to stop audio', e, stackTrace);
      return Result.error('Audio stop failed', 
                         RepositoryException('Stop error', originalError: e));
    }
  }

  /// Seek to position
  static Future<Result<void>> seekTo(Duration position) async {
    try {
      if (_currentState == AudioPlaybackState.stopped) {
        return Result.error('No audio loaded');
      }

      await _player!.seekToPlayer(position);
      _currentPosition = position;
      
      Logger.debug('Audio seeked to: ${position.inSeconds}s');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to seek audio', e, stackTrace);
      return Result.error('Audio seek failed', 
                         RepositoryException('Seek error', originalError: e));
    }
  }

  /// Get current playback state
  static AudioPlaybackState get currentState => _currentState;

  /// Get current position
  static Duration get currentPosition => _currentPosition;

  /// Get current duration
  static Duration? get currentDuration => _currentDuration;

  /// Check if audio is playing
  static bool get isPlaying => _currentState == AudioPlaybackState.playing;

  /// Check if audio is paused
  static bool get isPaused => _currentState == AudioPlaybackState.paused;

  /// Make request to OpenAI TTS API
  static Future<Result<Uint8List>> _makeTTSRequest(Map<String, dynamic> requestData) async {
    try {
      final dio = Dio();
      
      // Configure request
      dio.options.headers = {
        'Authorization': 'Bearer ${AppConstants.openaiApiKey}',
        'Content-Type': 'application/json',
      };
      
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(minutes: 2);
      dio.options.responseType = ResponseType.bytes;
      
      final response = await dio.post(
        'https://api.openai.com/v1/audio/speech',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return Result.success(Uint8List.fromList(response.data));
      } else {
        return Result.error('TTS API error: ${response.statusCode}');
      }
      
    } on DioException catch (e) {
      Logger.error('TTS API request failed', e);
      
      if (e.type == DioExceptionType.connectionTimeout) {
        return Result.error('Connection timeout to TTS API');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        return Result.error('TTS API response timeout');
      } else if (e.response?.statusCode == 429) {
        return Result.error('TTS API rate limit exceeded');
      } else {
        return Result.error('TTS API error: ${e.message}');
      }
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in TTS request', e, stackTrace);
      return Result.error('TTS request failed', 
                         NetworkException('Request error', originalError: e));
    }
  }

  /// Save audio data to file
  static Future<Result<String>> _saveAudioFile(Uint8List audioData, String? fileName) async {
    try {
      final directory = await getTemporaryDirectory();
      final audioDir = Directory('${directory.path}/tts_audio');
      await audioDir.create(recursive: true);
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final name = fileName ?? 'tts_$timestamp';
      final filePath = '${audioDir.path}/$name.mp3';
      
      final file = File(filePath);
      await file.writeAsBytes(audioData);
      
      return Result.success(filePath);
    } catch (e, stackTrace) {
      Logger.error('Failed to save audio file', e, stackTrace);
      return Result.error('Audio file save failed', 
                         StorageException('File save error', originalError: e));
    }
  }

  /// Get cached audio file
  static Future<Result<String?>> _getCachedAudio(String text, TTSVoice voice, double speed) async {
    try {
      // Simple cache key based on text hash, voice, and speed
      final cacheKey = '${text.hashCode}_${voice.value}_${speed.toString().replaceAll('.', '_')}';
      
      final directory = await getTemporaryDirectory();
      final cacheDir = Directory('${directory.path}/tts_cache');
      final cachedFile = File('${cacheDir.path}/$cacheKey.mp3');
      
      if (await cachedFile.exists()) {
        Logger.debug('Using cached TTS audio: $cacheKey');
        return Result.success(cachedFile.path);
      }
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to check TTS cache', e, stackTrace);
      return Result.success(null); // Don't fail on cache errors
    }
  }

  /// Get audio duration
  static Future<Duration?> _getAudioDuration(String audioPath) async {
    try {
      // This would require additional audio analysis
      // For now, return null and let the player determine duration
      return null;
    } catch (e) {
      Logger.error('Failed to get audio duration', e);
      return null;
    }
  }

  /// Configure platform-specific settings
  static Future<void> _configurePlatformSpecificSettings() async {
    if (Platform.isIOS) {
      await _configureIOSAudioSession();
    } else if (Platform.isAndroid) {
      await _configureAndroidAudioSession();
    }
  }

  /// Configure iOS audio session
  static Future<void> _configureIOSAudioSession() async {
    // iOS-specific audio session configuration
    Logger.debug('Configured iOS audio session for TTS');
  }

  /// Configure Android audio session
  static Future<void> _configureAndroidAudioSession() async {
    // Android-specific audio configuration
    Logger.debug('Configured Android audio session for TTS');
  }

  /// Dispose service
  static Future<void> dispose() async {
    try {
      await stopAudio();
      await _player?.closePlayer();
      
      _player = null;
      _isInitialized = false;
      
      Logger.info('Text-to-speech service disposed');
    } catch (e, stackTrace) {
      Logger.error('Failed to dispose text-to-speech service', e, stackTrace);
    }
  }
}

/// TTS voice options
enum TTSVoice {
  alloy('alloy'),
  echo('echo'),
  fable('fable'),
  onyx('onyx'),
  nova('nova'),
  shimmer('shimmer');

  const TTSVoice(this.value);
  final String value;
}

/// TTS model options
enum TTSModel {
  tts1('tts-1'),
  tts1Hd('tts-1-hd');

  const TTSModel(this.value);
  final String value;
}

/// Audio playback state
enum AudioPlaybackState {
  stopped,
  loading,
  playing,
  paused,
  completed,
  error,
}
