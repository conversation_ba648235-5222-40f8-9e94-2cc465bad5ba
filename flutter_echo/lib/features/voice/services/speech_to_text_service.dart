/// Speech-to-Text Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for converting audio recordings to text using OpenAI Whisper API.

import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/network/api_client.dart';
import '../data/models/audio_recording_entity.dart';

/// Service for speech-to-text conversion
class SpeechToTextService {
  final ApiClient _apiClient;
  final Dio _dio;

  SpeechToTextService(this._apiClient, this._dio);

  /// Convert audio recording to text using OpenAI Whisper
  static Future<Result<SpeechToTextResult>> transcribeAudio({
    required AudioRecordingEntity recording,
    String model = 'whisper-1',
    String? language,
    String? prompt,
    double temperature = 0.0,
    TranscriptionFormat format = TranscriptionFormat.json,
    bool includeTimestamps = true,
  }) async {
    try {
      Logger.info('Starting speech-to-text transcription for: ${recording.fileName}');
      
      // Validate audio file
      final file = File(recording.filePath);
      if (!await file.exists()) {
        return Result.error('Audio file not found: ${recording.filePath}');
      }

      // Prepare form data
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          recording.filePath,
          filename: recording.fileName,
          contentType: DioMediaType.parse(_getContentType(recording.codec)),
        ),
        'model': model,
        'response_format': format.value,
        'temperature': temperature,
        if (language != null) 'language': language,
        if (prompt != null) 'prompt': prompt,
        if (includeTimestamps && format == TranscriptionFormat.json) 
          'timestamp_granularities[]': 'word',
      });

      // Make API request
      final response = await _makeWhisperRequest(formData);
      if (response.isError) {
        return Result.error(response.errorMessage!);
      }

      // Parse response based on format
      final result = await _parseTranscriptionResponse(
        response.data!,
        format,
        recording,
      );

      if (result.isSuccess) {
        Logger.info('Speech-to-text completed: ${result.data!.text.length} characters transcribed');
      }

      return result;
      
    } catch (e, stackTrace) {
      Logger.error('Failed to transcribe audio', e, stackTrace);
      return Result.error('Speech-to-text failed', 
                         NetworkException('Transcription error', originalError: e));
    }
  }

  /// Convert audio recording to text with local fallback
  static Future<Result<SpeechToTextResult>> transcribeAudioWithFallback({
    required AudioRecordingEntity recording,
    bool useLocalFallback = true,
  }) async {
    // Try OpenAI Whisper first
    final whisperResult = await transcribeAudio(recording: recording);
    
    if (whisperResult.isSuccess) {
      return whisperResult;
    }

    // Fallback to local speech recognition if enabled
    if (useLocalFallback) {
      Logger.info('OpenAI Whisper failed, trying local speech recognition...');
      return await _transcribeWithLocalSpeechRecognition(recording);
    }

    return whisperResult;
  }

  /// Make request to OpenAI Whisper API
  static Future<Result<Map<String, dynamic>>> _makeWhisperRequest(FormData formData) async {
    try {
      final dio = Dio();
      
      // Configure request
      dio.options.headers = {
        'Authorization': 'Bearer ${AppConstants.openaiApiKey}',
        'Content-Type': 'multipart/form-data',
      };
      
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(minutes: 5); // Whisper can take time
      
      final response = await dio.post(
        'https://api.openai.com/v1/audio/transcriptions',
        data: formData,
      );

      if (response.statusCode == 200) {
        return Result.success(response.data as Map<String, dynamic>);
      } else {
        return Result.error('Whisper API error: ${response.statusCode}');
      }
      
    } on DioException catch (e) {
      Logger.error('Whisper API request failed', e);
      
      if (e.type == DioExceptionType.connectionTimeout) {
        return Result.error('Connection timeout to Whisper API');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        return Result.error('Whisper API response timeout');
      } else if (e.response?.statusCode == 413) {
        return Result.error('Audio file too large for Whisper API');
      } else if (e.response?.statusCode == 429) {
        return Result.error('Whisper API rate limit exceeded');
      } else {
        return Result.error('Whisper API error: ${e.message}');
      }
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in Whisper request', e, stackTrace);
      return Result.error('Whisper request failed', 
                         NetworkException('Request error', originalError: e));
    }
  }

  /// Parse transcription response
  static Future<Result<SpeechToTextResult>> _parseTranscriptionResponse(
    Map<String, dynamic> response,
    TranscriptionFormat format,
    AudioRecordingEntity recording,
  ) async {
    try {
      switch (format) {
        case TranscriptionFormat.json:
          return _parseJsonResponse(response, recording);
        case TranscriptionFormat.text:
          return _parseTextResponse(response, recording);
        case TranscriptionFormat.srt:
          return _parseSrtResponse(response, recording);
        case TranscriptionFormat.vtt:
          return _parseVttResponse(response, recording);
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to parse transcription response', e, stackTrace);
      return Result.error('Failed to parse transcription', 
                         RepositoryException('Parse error', originalError: e));
    }
  }

  /// Parse JSON response with detailed information
  static Result<SpeechToTextResult> _parseJsonResponse(
    Map<String, dynamic> response,
    AudioRecordingEntity recording,
  ) {
    final text = response['text'] as String? ?? '';
    final language = response['language'] as String?;
    final duration = (response['duration'] as num?)?.toDouble();
    
    // Parse segments if available
    final segments = <AudioSegment>[];
    if (response['segments'] != null) {
      for (final segmentData in response['segments'] as List) {
        final segment = AudioSegment(
          startTime: ((segmentData['start'] as num) * 1000).round(),
          endTime: ((segmentData['end'] as num) * 1000).round(),
          text: segmentData['text'] as String,
          confidence: (segmentData['avg_logprob'] as num?)?.toDouble() ?? 0.0,
          words: _parseWords(segmentData['words']),
        );
        segments.add(segment);
      }
    }

    return Result.success(SpeechToTextResult(
      text: text,
      confidence: _calculateOverallConfidence(segments),
      language: language,
      duration: duration != null ? Duration(milliseconds: (duration * 1000).round()) : null,
      segments: segments.isNotEmpty ? segments : null,
      format: TranscriptionFormat.json,
      model: 'whisper-1',
      processingTime: DateTime.now().difference(recording.createdAt),
    ));
  }

  /// Parse words from segment data
  static List<AudioWord>? _parseWords(dynamic wordsData) {
    if (wordsData == null) return null;
    
    final words = <AudioWord>[];
    for (final wordData in wordsData as List) {
      final word = AudioWord(
        startTime: ((wordData['start'] as num) * 1000).round(),
        endTime: ((wordData['end'] as num) * 1000).round(),
        word: wordData['word'] as String,
        confidence: (wordData['probability'] as num?)?.toDouble() ?? 0.0,
      );
      words.add(word);
    }
    
    return words.isNotEmpty ? words : null;
  }

  /// Parse text response
  static Result<SpeechToTextResult> _parseTextResponse(
    Map<String, dynamic> response,
    AudioRecordingEntity recording,
  ) {
    final text = response['text'] as String? ?? '';
    
    return Result.success(SpeechToTextResult(
      text: text,
      confidence: 0.8, // Default confidence for text format
      format: TranscriptionFormat.text,
      model: 'whisper-1',
      processingTime: DateTime.now().difference(recording.createdAt),
    ));
  }

  /// Parse SRT response
  static Result<SpeechToTextResult> _parseSrtResponse(
    Map<String, dynamic> response,
    AudioRecordingEntity recording,
  ) {
    // SRT parsing implementation would go here
    final text = response['text'] as String? ?? '';
    
    return Result.success(SpeechToTextResult(
      text: text,
      confidence: 0.8,
      format: TranscriptionFormat.srt,
      model: 'whisper-1',
      processingTime: DateTime.now().difference(recording.createdAt),
    ));
  }

  /// Parse VTT response
  static Result<SpeechToTextResult> _parseVttResponse(
    Map<String, dynamic> response,
    AudioRecordingEntity recording,
  ) {
    // VTT parsing implementation would go here
    final text = response['text'] as String? ?? '';
    
    return Result.success(SpeechToTextResult(
      text: text,
      confidence: 0.8,
      format: TranscriptionFormat.vtt,
      model: 'whisper-1',
      processingTime: DateTime.now().difference(recording.createdAt),
    ));
  }

  /// Fallback to local speech recognition
  static Future<Result<SpeechToTextResult>> _transcribeWithLocalSpeechRecognition(
    AudioRecordingEntity recording,
  ) async {
    try {
      // This would use platform-specific speech recognition
      // For now, return a placeholder result
      Logger.warning('Local speech recognition not yet implemented');
      
      return Result.error('Local speech recognition not available');
    } catch (e, stackTrace) {
      Logger.error('Local speech recognition failed', e, stackTrace);
      return Result.error('Local transcription failed', 
                         RepositoryException('Local STT error', originalError: e));
    }
  }

  /// Calculate overall confidence from segments
  static double _calculateOverallConfidence(List<AudioSegment> segments) {
    if (segments.isEmpty) return 0.0;
    
    final totalConfidence = segments.fold<double>(0.0, (sum, segment) => sum + segment.confidence);
    return totalConfidence / segments.length;
  }

  /// Get content type for audio codec
  static String _getContentType(String codec) {
    switch (codec.toLowerCase()) {
      case 'aac':
        return 'audio/aac';
      case 'm4a':
        return 'audio/mp4';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'flac':
        return 'audio/flac';
      default:
        return 'audio/mpeg';
    }
  }
}

/// Speech-to-text result
class SpeechToTextResult {
  final String text;
  final double confidence;
  final String? language;
  final Duration? duration;
  final List<AudioSegment>? segments;
  final TranscriptionFormat format;
  final String model;
  final Duration processingTime;

  SpeechToTextResult({
    required this.text,
    required this.confidence,
    this.language,
    this.duration,
    this.segments,
    required this.format,
    required this.model,
    required this.processingTime,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'confidence': confidence,
      'language': language,
      'duration': duration?.inMilliseconds,
      'segments': segments?.map((e) => e.toJson()).toList(),
      'format': format.value,
      'model': model,
      'processingTime': processingTime.inMilliseconds,
    };
  }

  @override
  String toString() {
    return 'SpeechToTextResult(text: ${text.substring(0, text.length > 50 ? 50 : text.length)}..., confidence: $confidence)';
  }
}

/// Transcription format enumeration
enum TranscriptionFormat {
  json('json'),
  text('text'),
  srt('srt'),
  vtt('vtt');

  const TranscriptionFormat(this.value);
  final String value;
}
