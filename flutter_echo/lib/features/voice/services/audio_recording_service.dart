/// Audio Recording Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for audio recording with platform-specific optimizations.

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/constants/app_constants.dart';
import '../data/models/audio_recording_entity.dart';

/// Service for audio recording functionality
class AudioRecordingService {
  static FlutterSoundRecorder? _recorder;
  static StreamSubscription<RecordingDisposition>? _recordingSubscription;
  static StreamController<AudioRecordingState>? _stateController;
  static StreamController<double>? _amplitudeController;
  static StreamController<Duration>? _durationController;
  
  static bool _isInitialized = false;
  static AudioRecordingState _currentState = AudioRecordingState.stopped;
  static String? _currentRecordingPath;
  static DateTime? _recordingStartTime;

  /// Initialize audio recording service
  static Future<Result<void>> initialize() async {
    try {
      if (_isInitialized) {
        return Result.success(null);
      }

      Logger.info('Initializing audio recording service...');
      
      // Request microphone permission
      final permissionResult = await _requestMicrophonePermission();
      if (permissionResult.isError) {
        return permissionResult;
      }

      // Initialize recorder
      _recorder = FlutterSoundRecorder();
      await _recorder!.openRecorder();
      
      // Configure platform-specific settings
      await _configurePlatformSpecificSettings();
      
      // Initialize stream controllers
      _stateController = StreamController<AudioRecordingState>.broadcast();
      _amplitudeController = StreamController<double>.broadcast();
      _durationController = StreamController<Duration>.broadcast();
      
      _isInitialized = true;
      Logger.info('Audio recording service initialized successfully');
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize audio recording service', e, stackTrace);
      return Result.error('Audio recording initialization failed', 
                         RepositoryException('Audio init error', originalError: e));
    }
  }

  /// Start recording audio
  static Future<Result<String>> startRecording({
    String? fileName,
    Duration? maxDuration,
  }) async {
    try {
      if (!_isInitialized || _recorder == null) {
        return Result.error('Audio recording service not initialized');
      }

      if (_currentState == AudioRecordingState.recording) {
        return Result.error('Recording already in progress');
      }

      // Generate file path
      final recordingPath = await _generateRecordingPath(fileName);
      
      // Configure recording settings
      final codec = Platform.isIOS ? Codec.aacMP4 : Codec.aacADTS;
      
      await _recorder!.startRecorder(
        toFile: recordingPath,
        codec: codec,
        bitRate: AppConstants.voiceRecordingBitRate,
        sampleRate: AppConstants.voiceRecordingSampleRate,
        numChannels: 1, // Mono recording
      );

      // Start monitoring
      _recordingSubscription = _recorder!.onProgress!.listen(
        _handleRecordingProgress,
        onError: _handleRecordingError,
      );

      _currentRecordingPath = recordingPath;
      _recordingStartTime = DateTime.now();
      _updateState(AudioRecordingState.recording);
      
      // Set max duration timer if specified
      if (maxDuration != null) {
        Timer(maxDuration, () {
          if (_currentState == AudioRecordingState.recording) {
            stopRecording();
          }
        });
      }

      Logger.info('Audio recording started: $recordingPath');
      return Result.success(recordingPath);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to start audio recording', e, stackTrace);
      _updateState(AudioRecordingState.error);
      return Result.error('Failed to start recording', 
                         RepositoryException('Recording start error', originalError: e));
    }
  }

  /// Stop recording audio
  static Future<Result<AudioRecordingEntity>> stopRecording() async {
    try {
      if (!_isInitialized || _recorder == null) {
        return Result.error('Audio recording service not initialized');
      }

      if (_currentState != AudioRecordingState.recording) {
        return Result.error('No recording in progress');
      }

      _updateState(AudioRecordingState.processing);
      
      // Stop recording
      final recordingPath = await _recorder!.stopRecorder();
      await _recordingSubscription?.cancel();
      _recordingSubscription = null;

      if (recordingPath == null || _currentRecordingPath == null) {
        return Result.error('Recording path not available');
      }

      // Create recording entity
      final file = File(_currentRecordingPath!);
      final fileSize = await file.length();
      final duration = DateTime.now().difference(_recordingStartTime!);
      
      final recording = AudioRecordingEntity(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        filePath: _currentRecordingPath!,
        fileName: _currentRecordingPath!.split('/').last,
        duration: duration,
        fileSize: fileSize,
        sampleRate: AppConstants.voiceRecordingSampleRate,
        bitRate: AppConstants.voiceRecordingBitRate,
        codec: Platform.isIOS ? 'aac' : 'aac',
        createdAt: _recordingStartTime!,
        isProcessed: false,
      );

      _updateState(AudioRecordingState.completed);
      _currentRecordingPath = null;
      _recordingStartTime = null;

      Logger.info('Audio recording completed: ${recording.fileName} (${recording.duration.inSeconds}s, ${recording.fileSize} bytes)');
      return Result.success(recording);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to stop audio recording', e, stackTrace);
      _updateState(AudioRecordingState.error);
      return Result.error('Failed to stop recording', 
                         RepositoryException('Recording stop error', originalError: e));
    }
  }

  /// Pause recording
  static Future<Result<void>> pauseRecording() async {
    try {
      if (_currentState != AudioRecordingState.recording) {
        return Result.error('No recording in progress');
      }

      await _recorder!.pauseRecorder();
      _updateState(AudioRecordingState.paused);
      
      Logger.debug('Audio recording paused');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to pause audio recording', e, stackTrace);
      return Result.error('Failed to pause recording', 
                         RepositoryException('Recording pause error', originalError: e));
    }
  }

  /// Resume recording
  static Future<Result<void>> resumeRecording() async {
    try {
      if (_currentState != AudioRecordingState.paused) {
        return Result.error('Recording not paused');
      }

      await _recorder!.resumeRecorder();
      _updateState(AudioRecordingState.recording);
      
      Logger.debug('Audio recording resumed');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to resume audio recording', e, stackTrace);
      return Result.error('Failed to resume recording', 
                         RepositoryException('Recording resume error', originalError: e));
    }
  }

  /// Cancel recording
  static Future<Result<void>> cancelRecording() async {
    try {
      if (_currentState == AudioRecordingState.stopped) {
        return Result.success(null);
      }

      await _recorder!.stopRecorder();
      await _recordingSubscription?.cancel();
      _recordingSubscription = null;

      // Delete the recording file
      if (_currentRecordingPath != null) {
        final file = File(_currentRecordingPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      _updateState(AudioRecordingState.stopped);
      _currentRecordingPath = null;
      _recordingStartTime = null;

      Logger.debug('Audio recording cancelled');
      return Result.success(null);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to cancel audio recording', e, stackTrace);
      return Result.error('Failed to cancel recording', 
                         RepositoryException('Recording cancel error', originalError: e));
    }
  }

  /// Get current recording state
  static AudioRecordingState get currentState => _currentState;

  /// Get state stream
  static Stream<AudioRecordingState> get stateStream => 
      _stateController?.stream ?? const Stream.empty();

  /// Get amplitude stream
  static Stream<double> get amplitudeStream => 
      _amplitudeController?.stream ?? const Stream.empty();

  /// Get duration stream
  static Stream<Duration> get durationStream => 
      _durationController?.stream ?? const Stream.empty();

  /// Check if recording is active
  static bool get isRecording => _currentState == AudioRecordingState.recording;

  /// Check if recording is paused
  static bool get isPaused => _currentState == AudioRecordingState.paused;

  /// Request microphone permission
  static Future<Result<void>> _requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      
      if (status.isGranted) {
        return Result.success(null);
      } else if (status.isDenied) {
        return Result.error('Microphone permission denied');
      } else if (status.isPermanentlyDenied) {
        return Result.error('Microphone permission permanently denied. Please enable in settings.');
      } else {
        return Result.error('Microphone permission not granted');
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to request microphone permission', e, stackTrace);
      return Result.error('Permission request failed', 
                         RepositoryException('Permission error', originalError: e));
    }
  }

  /// Configure platform-specific settings
  static Future<void> _configurePlatformSpecificSettings() async {
    if (Platform.isIOS) {
      await _configureIOSAudioSession();
    } else if (Platform.isAndroid) {
      await _configureAndroidAudioSession();
    }
  }

  /// Configure iOS audio session
  static Future<void> _configureIOSAudioSession() async {
    // iOS-specific audio session configuration will be handled by flutter_sound
    Logger.debug('Configured iOS audio session');
  }

  /// Configure Android audio session
  static Future<void> _configureAndroidAudioSession() async {
    // Android-specific audio configuration will be handled by flutter_sound
    Logger.debug('Configured Android audio session');
  }

  /// Generate recording file path
  static Future<String> _generateRecordingPath(String? fileName) async {
    final directory = await getTemporaryDirectory();
    final recordingsDir = Directory('${directory.path}/recordings');
    await recordingsDir.create(recursive: true);
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final name = fileName ?? 'recording_$timestamp';
    final extension = Platform.isIOS ? 'm4a' : 'aac';
    
    return '${recordingsDir.path}/$name.$extension';
  }

  /// Handle recording progress
  static void _handleRecordingProgress(RecordingDisposition disposition) {
    final amplitude = disposition.decibels ?? 0.0;
    final duration = disposition.duration;
    
    _amplitudeController?.add(amplitude);
    _durationController?.add(duration);
  }

  /// Handle recording error
  static void _handleRecordingError(dynamic error) {
    Logger.error('Recording error', error);
    _updateState(AudioRecordingState.error);
  }

  /// Update recording state
  static void _updateState(AudioRecordingState newState) {
    _currentState = newState;
    _stateController?.add(newState);
  }

  /// Dispose service
  static Future<void> dispose() async {
    try {
      await cancelRecording();
      await _recorder?.closeRecorder();
      await _stateController?.close();
      await _amplitudeController?.close();
      await _durationController?.close();
      
      _recorder = null;
      _stateController = null;
      _amplitudeController = null;
      _durationController = null;
      _isInitialized = false;
      
      Logger.info('Audio recording service disposed');
    } catch (e, stackTrace) {
      Logger.error('Failed to dispose audio recording service', e, stackTrace);
    }
  }
}

/// Audio recording state enumeration
enum AudioRecordingState {
  stopped,
  recording,
  paused,
  processing,
  completed,
  error,
}
