/// Voice Recording Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Widget for voice recording with visual feedback and controls.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../providers/voice_providers.dart';
import '../../services/audio_recording_service.dart';
import '../../services/voice_activity_detection_service.dart';

/// Voice recording widget with controls and visual feedback
class VoiceRecordingWidget extends ConsumerStatefulWidget {
  final VoidCallback? onRecordingComplete;
  final VoidCallback? onRecordingCancelled;
  final bool enableVAD;
  final Duration? maxDuration;

  const VoiceRecordingWidget({
    super.key,
    this.onRecordingComplete,
    this.onRecordingCancelled,
    this.enableVAD = true,
    this.maxDuration,
  });

  @override
  ConsumerState<VoiceRecordingWidget> createState() => _VoiceRecordingWidgetState();
}

class _VoiceRecordingWidgetState extends ConsumerState<VoiceRecordingWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final recordingState = ref.watch(audioRecordingStateProvider);
    final amplitudeAsync = ref.watch(audioRecordingAmplitudeProvider);
    final durationAsync = ref.watch(audioRecordingDurationProvider);

    // Update animations based on recording state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAnimations(recordingState);
    });

    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Recording status
          _buildRecordingStatus(recordingState),
          
          const SizedBox(height: 24.0),
          
          // Visual feedback
          _buildVisualFeedback(recordingState, amplitudeAsync),
          
          const SizedBox(height: 24.0),
          
          // Duration display
          _buildDurationDisplay(durationAsync),
          
          const SizedBox(height: 24.0),
          
          // Control buttons
          _buildControlButtons(recordingState),
        ],
      ),
    );
  }

  Widget _buildRecordingStatus(AudioRecordingState state) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (state) {
      case AudioRecordingState.stopped:
        statusText = 'Ready to record';
        statusColor = AppColors.textSecondary;
        statusIcon = Icons.mic_none;
        break;
      case AudioRecordingState.recording:
        statusText = 'Recording...';
        statusColor = AppColors.error;
        statusIcon = Icons.mic;
        break;
      case AudioRecordingState.paused:
        statusText = 'Recording paused';
        statusColor = AppColors.warning;
        statusIcon = Icons.pause;
        break;
      case AudioRecordingState.processing:
        statusText = 'Processing...';
        statusColor = AppColors.primary;
        statusIcon = Icons.hourglass_empty;
        break;
      case AudioRecordingState.completed:
        statusText = 'Recording complete';
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        break;
      case AudioRecordingState.error:
        statusText = 'Recording error';
        statusColor = AppColors.error;
        statusIcon = Icons.error;
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          statusIcon,
          color: statusColor,
          size: 20.0,
        ),
        const SizedBox(width: 8.0),
        Text(
          statusText,
          style: AppTheme.textTheme.bodyMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildVisualFeedback(AudioRecordingState state, AsyncValue<double> amplitudeAsync) {
    return SizedBox(
      height: 120.0,
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([_pulseController, _waveController]),
          builder: (context, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                // Outer pulse ring
                if (state == AudioRecordingState.recording)
                  Container(
                    width: 100.0 * _pulseAnimation.value,
                    height: 100.0 * _pulseAnimation.value,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.error.withOpacity(0.3),
                        width: 2.0,
                      ),
                    ),
                  ),
                
                // Amplitude visualization
                if (state == AudioRecordingState.recording)
                  amplitudeAsync.when(
                    data: (amplitude) => _buildAmplitudeVisualization(amplitude),
                    loading: () => const SizedBox.shrink(),
                    error: (_, __) => const SizedBox.shrink(),
                  ),
                
                // Main recording button
                GestureDetector(
                  onTap: () => _handleRecordingTap(state),
                  child: Container(
                    width: 80.0,
                    height: 80.0,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getRecordingButtonColor(state),
                      boxShadow: [
                        BoxShadow(
                          color: _getRecordingButtonColor(state).withOpacity(0.3),
                          blurRadius: 8.0,
                          spreadRadius: 2.0,
                        ),
                      ],
                    ),
                    child: Icon(
                      _getRecordingButtonIcon(state),
                      color: Colors.white,
                      size: 32.0,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildAmplitudeVisualization(double amplitude) {
    // Convert amplitude to visual scale (0.0 to 1.0)
    final normalizedAmplitude = ((amplitude + 60.0) / 60.0).clamp(0.0, 1.0);
    
    return Container(
      width: 60.0 + (normalizedAmplitude * 40.0),
      height: 60.0 + (normalizedAmplitude * 40.0),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.error.withOpacity(0.2 + (normalizedAmplitude * 0.3)),
      ),
    );
  }

  Widget _buildDurationDisplay(AsyncValue<Duration> durationAsync) {
    return durationAsync.when(
      data: (duration) {
        final minutes = duration.inMinutes;
        final seconds = duration.inSeconds % 60;
        return Text(
          '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
            fontFeatures: [const FontFeature.tabularFigures()],
          ),
        );
      },
      loading: () => Text(
        '00:00',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w600,
        ),
      ),
      error: (_, __) => Text(
        '--:--',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          color: AppColors.error,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildControlButtons(AudioRecordingState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Cancel button
        if (state == AudioRecordingState.recording || state == AudioRecordingState.paused)
          _buildControlButton(
            icon: Icons.close,
            label: 'Cancel',
            color: AppColors.error,
            onPressed: _handleCancel,
          ),
        
        // Pause/Resume button
        if (state == AudioRecordingState.recording)
          _buildControlButton(
            icon: Icons.pause,
            label: 'Pause',
            color: AppColors.warning,
            onPressed: _handlePause,
          )
        else if (state == AudioRecordingState.paused)
          _buildControlButton(
            icon: Icons.play_arrow,
            label: 'Resume',
            color: AppColors.primary,
            onPressed: _handleResume,
          ),
        
        // Stop button
        if (state == AudioRecordingState.recording || state == AudioRecordingState.paused)
          _buildControlButton(
            icon: Icons.stop,
            label: 'Stop',
            color: AppColors.success,
            onPressed: _handleStop,
          ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon),
          color: color,
          iconSize: 24.0,
          style: IconButton.styleFrom(
            backgroundColor: color.withOpacity(0.1),
            padding: const EdgeInsets.all(12.0),
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          label,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _updateAnimations(AudioRecordingState state) {
    switch (state) {
      case AudioRecordingState.recording:
        _pulseController.repeat(reverse: true);
        _waveController.repeat();
        break;
      case AudioRecordingState.stopped:
      case AudioRecordingState.completed:
      case AudioRecordingState.error:
        _pulseController.stop();
        _waveController.stop();
        _pulseController.reset();
        _waveController.reset();
        break;
      case AudioRecordingState.paused:
      case AudioRecordingState.processing:
        _pulseController.stop();
        _waveController.stop();
        break;
    }
  }

  Color _getRecordingButtonColor(AudioRecordingState state) {
    switch (state) {
      case AudioRecordingState.stopped:
        return AppColors.primary;
      case AudioRecordingState.recording:
        return AppColors.error;
      case AudioRecordingState.paused:
        return AppColors.warning;
      case AudioRecordingState.processing:
        return AppColors.textSecondary;
      case AudioRecordingState.completed:
        return AppColors.success;
      case AudioRecordingState.error:
        return AppColors.error;
    }
  }

  IconData _getRecordingButtonIcon(AudioRecordingState state) {
    switch (state) {
      case AudioRecordingState.stopped:
        return Icons.mic;
      case AudioRecordingState.recording:
        return Icons.stop;
      case AudioRecordingState.paused:
        return Icons.play_arrow;
      case AudioRecordingState.processing:
        return Icons.hourglass_empty;
      case AudioRecordingState.completed:
        return Icons.check;
      case AudioRecordingState.error:
        return Icons.error;
    }
  }

  void _handleRecordingTap(AudioRecordingState state) {
    switch (state) {
      case AudioRecordingState.stopped:
        _startRecording();
        break;
      case AudioRecordingState.recording:
        _handleStop();
        break;
      case AudioRecordingState.paused:
        _handleResume();
        break;
      default:
        break;
    }
  }

  void _startRecording() async {
    final result = await AudioRecordingService.startRecording(
      maxDuration: widget.maxDuration,
    );
    
    if (result.isError) {
      _showError(result.errorMessage!);
    } else {
      ref.read(audioRecordingStateProvider.notifier).startRecording();
    }
  }

  void _handlePause() async {
    final result = await AudioRecordingService.pauseRecording();
    
    if (result.isError) {
      _showError(result.errorMessage!);
    } else {
      ref.read(audioRecordingStateProvider.notifier).pauseRecording();
    }
  }

  void _handleResume() async {
    final result = await AudioRecordingService.resumeRecording();
    
    if (result.isError) {
      _showError(result.errorMessage!);
    } else {
      ref.read(audioRecordingStateProvider.notifier).startRecording();
    }
  }

  void _handleStop() async {
    ref.read(audioRecordingStateProvider.notifier).setProcessing();
    
    final result = await AudioRecordingService.stopRecording();
    
    if (result.isError) {
      ref.read(audioRecordingStateProvider.notifier).setError();
      _showError(result.errorMessage!);
    } else {
      ref.read(audioRecordingStateProvider.notifier).setCompleted();
      widget.onRecordingComplete?.call();
    }
  }

  void _handleCancel() async {
    final result = await AudioRecordingService.cancelRecording();
    
    if (result.isError) {
      _showError(result.errorMessage!);
    } else {
      ref.read(audioRecordingStateProvider.notifier).stopRecording();
      widget.onRecordingCancelled?.call();
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
