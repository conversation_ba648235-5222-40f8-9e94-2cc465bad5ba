/// Stock Detection Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for intelligent stock detection in conversations with pattern matching and confidence scoring.

import 'dart:convert';
import 'package:flutter/services.dart';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../data/models/stock_entity.dart';
import '../data/models/stock_mention.dart';
import '../data/repositories/stock_repository.dart';

/// Service for detecting stock mentions in text
class StockDetectionService {
  final StockRepository _stockRepository;
  
  // Stock detection patterns
  static final RegExp _tickerPattern = RegExp(
    r'\b([A-Z]{1,5})\b',
    caseSensitive: true,
  );
  
  static final RegExp _dollarTickerPattern = RegExp(
    r'\$([A-Z]{1,5})\b',
    caseSensitive: true,
  );
  
  static final RegExp _companyPattern = RegExp(
    r'\b([A-Z][a-z]+(?: [A-Z][a-z]+)*(?:,? (?:Inc\.?|Corp\.?|LLC|Ltd\.?|Co\.?))?)\b',
  );
  
  // Common stock-related keywords
  static const List<String> _stockKeywords = [
    'stock', 'stocks', 'share', 'shares', 'equity', 'investment',
    'portfolio', 'trading', 'buy', 'sell', 'hold', 'bullish', 'bearish',
    'earnings', 'dividend', 'market cap', 'P/E ratio', 'revenue',
    'growth', 'valuation', 'analyst', 'upgrade', 'downgrade',
  ];
  
  // Cache for stock data
  Map<String, StockEntity>? _stockCache;
  Map<String, List<String>>? _companyKeywordsCache;
  DateTime? _cacheLastUpdated;
  static const Duration _cacheExpiry = Duration(hours: 1);

  StockDetectionService(this._stockRepository);

  /// Detect stock mentions in text
  Future<Result<List<StockMention>>> detectStocks(String text) async {
    try {
      Logger.debug('Detecting stocks in text: ${text.substring(0, text.length > 100 ? 100 : text.length)}...');
      
      // Ensure stock cache is loaded
      final cacheResult = await _ensureStockCache();
      if (cacheResult.isError) {
        return Result.error(cacheResult.errorMessage!);
      }
      
      final mentions = <StockMention>[];
      final processedText = text.toLowerCase();
      
      // 1. Detect ticker symbols (highest confidence)
      final tickerMentions = await _detectTickerMentions(text);
      mentions.addAll(tickerMentions);
      
      // 2. Detect company names (medium confidence)
      final companyMentions = await _detectCompanyMentions(text);
      mentions.addAll(companyMentions);
      
      // 3. Detect keyword-based mentions (lower confidence)
      final keywordMentions = await _detectKeywordMentions(processedText);
      mentions.addAll(keywordMentions);
      
      // 4. Remove duplicates and sort by confidence
      final uniqueMentions = _deduplicateAndSort(mentions);
      
      Logger.info('Detected ${uniqueMentions.length} stock mentions');
      return Result.success(uniqueMentions);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to detect stocks', e, stackTrace);
      return Result.error('Stock detection failed', 
                         RepositoryException('Detection error', originalError: e));
    }
  }

  /// Detect ticker symbol mentions
  Future<List<StockMention>> _detectTickerMentions(String text) async {
    final mentions = <StockMention>[];
    
    // Check dollar-prefixed tickers first (e.g., $AAPL)
    final dollarMatches = _dollarTickerPattern.allMatches(text);
    for (final match in dollarMatches) {
      final ticker = match.group(1)!;
      final stock = _stockCache![ticker.toUpperCase()];
      
      if (stock != null) {
        mentions.add(StockMention(
          ticker: stock.ticker,
          companyName: stock.companyName,
          matchedText: '\$${ticker}',
          startIndex: match.start,
          endIndex: match.end,
          confidence: 0.95, // Very high confidence for dollar-prefixed
          matchType: StockMentionType.ticker,
          context: _extractContext(text, match.start, match.end),
        ));
      }
    }
    
    // Check regular ticker patterns
    final tickerMatches = _tickerPattern.allMatches(text);
    for (final match in tickerMatches) {
      final ticker = match.group(1)!;
      final stock = _stockCache![ticker.toUpperCase()];
      
      if (stock != null) {
        // Skip if already found as dollar-prefixed
        final alreadyFound = mentions.any((m) => 
          m.ticker == stock.ticker && 
          (match.start >= m.startIndex - 1 && match.start <= m.endIndex + 1));
        
        if (!alreadyFound) {
          // Lower confidence for bare tickers (could be false positives)
          final confidence = _calculateTickerConfidence(text, match.start, match.end, ticker);
          
          if (confidence > 0.3) { // Minimum threshold
            mentions.add(StockMention(
              ticker: stock.ticker,
              companyName: stock.companyName,
              matchedText: ticker,
              startIndex: match.start,
              endIndex: match.end,
              confidence: confidence,
              matchType: StockMentionType.ticker,
              context: _extractContext(text, match.start, match.end),
            ));
          }
        }
      }
    }
    
    return mentions;
  }

  /// Detect company name mentions
  Future<List<StockMention>> _detectCompanyMentions(String text) async {
    final mentions = <StockMention>[];
    
    for (final stock in _stockCache!.values) {
      final companyName = stock.companyName.toLowerCase();
      final textLower = text.toLowerCase();
      
      // Exact company name match
      int index = textLower.indexOf(companyName);
      while (index != -1) {
        mentions.add(StockMention(
          ticker: stock.ticker,
          companyName: stock.companyName,
          matchedText: text.substring(index, index + companyName.length),
          startIndex: index,
          endIndex: index + companyName.length,
          confidence: 0.85, // High confidence for exact company name
          matchType: StockMentionType.companyName,
          context: _extractContext(text, index, index + companyName.length),
        ));
        
        index = textLower.indexOf(companyName, index + 1);
      }
      
      // Check for company keywords/aliases
      final keywords = _companyKeywordsCache![stock.ticker] ?? [];
      for (final keyword in keywords) {
        int keywordIndex = textLower.indexOf(keyword.toLowerCase());
        while (keywordIndex != -1) {
          // Avoid duplicates with exact company name matches
          final overlaps = mentions.any((m) => 
            m.ticker == stock.ticker &&
            (keywordIndex < m.endIndex && keywordIndex + keyword.length > m.startIndex));
          
          if (!overlaps) {
            mentions.add(StockMention(
              ticker: stock.ticker,
              companyName: stock.companyName,
              matchedText: text.substring(keywordIndex, keywordIndex + keyword.length),
              startIndex: keywordIndex,
              endIndex: keywordIndex + keyword.length,
              confidence: 0.7, // Medium confidence for keywords
              matchType: StockMentionType.keyword,
              context: _extractContext(text, keywordIndex, keywordIndex + keyword.length),
            ));
          }
          
          keywordIndex = textLower.indexOf(keyword.toLowerCase(), keywordIndex + 1);
        }
      }
    }
    
    return mentions;
  }

  /// Detect keyword-based mentions
  Future<List<StockMention>> _detectKeywordMentions(String text) async {
    final mentions = <StockMention>[];
    
    // Look for stock-related keywords near potential tickers
    for (final keyword in _stockKeywords) {
      int index = text.indexOf(keyword);
      while (index != -1) {
        // Look for nearby ticker-like patterns
        final contextStart = (index - 50).clamp(0, text.length);
        final contextEnd = (index + keyword.length + 50).clamp(0, text.length);
        final context = text.substring(contextStart, contextEnd);
        
        final nearbyTickers = _tickerPattern.allMatches(context);
        for (final tickerMatch in nearbyTickers) {
          final ticker = tickerMatch.group(1)!;
          final stock = _stockCache![ticker.toUpperCase()];
          
          if (stock != null) {
            // Check if this is a new mention (not already detected)
            final actualStart = contextStart + tickerMatch.start;
            final actualEnd = contextStart + tickerMatch.end;
            
            final alreadyDetected = mentions.any((m) => 
              m.ticker == stock.ticker &&
              (actualStart >= m.startIndex && actualStart <= m.endIndex));
            
            if (!alreadyDetected) {
              mentions.add(StockMention(
                ticker: stock.ticker,
                companyName: stock.companyName,
                matchedText: ticker,
                startIndex: actualStart,
                endIndex: actualEnd,
                confidence: 0.5, // Lower confidence for keyword-based
                matchType: StockMentionType.contextual,
                context: _extractContext(text, actualStart, actualEnd),
              ));
            }
          }
        }
        
        index = text.indexOf(keyword, index + 1);
      }
    }
    
    return mentions;
  }

  /// Calculate confidence for ticker mentions based on context
  double _calculateTickerConfidence(String text, int start, int end, String ticker) {
    final context = _extractContext(text, start, end, contextLength: 30);
    final contextLower = context.toLowerCase();
    
    double confidence = 0.6; // Base confidence for valid ticker
    
    // Increase confidence if surrounded by stock-related keywords
    for (final keyword in _stockKeywords) {
      if (contextLower.contains(keyword)) {
        confidence += 0.1;
      }
    }
    
    // Increase confidence if preceded by $ or followed by stock-related terms
    if (start > 0 && text[start - 1] == '\$') {
      confidence += 0.3;
    }
    
    // Decrease confidence if it looks like an acronym in a sentence
    if (start > 0 && end < text.length) {
      final prevChar = text[start - 1];
      final nextChar = text[end];
      
      if (prevChar.toLowerCase() != prevChar || nextChar.toLowerCase() != nextChar) {
        confidence -= 0.2; // Likely part of a word or acronym
      }
    }
    
    // Decrease confidence for very common false positive tickers
    const commonFalsePositives = ['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BY', 'UP', 'DO', 'NO', 'IF', 'MY', 'SO', 'ON', 'AS', 'WE', 'HE', 'BE', 'TO', 'OF', 'IT', 'IS', 'IN', 'AT', 'OR'];
    if (commonFalsePositives.contains(ticker.toUpperCase())) {
      confidence -= 0.4;
    }
    
    return confidence.clamp(0.0, 1.0);
  }

  /// Extract context around a match
  String _extractContext(String text, int start, int end, {int contextLength = 50}) {
    final contextStart = (start - contextLength).clamp(0, text.length);
    final contextEnd = (end + contextLength).clamp(0, text.length);
    return text.substring(contextStart, contextEnd);
  }

  /// Remove duplicate mentions and sort by confidence
  List<StockMention> _deduplicateAndSort(List<StockMention> mentions) {
    final Map<String, StockMention> bestMentions = {};
    
    for (final mention in mentions) {
      final key = '${mention.ticker}_${mention.startIndex}';
      final existing = bestMentions[key];
      
      if (existing == null || mention.confidence > existing.confidence) {
        bestMentions[key] = mention;
      }
    }
    
    final result = bestMentions.values.toList();
    result.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    return result;
  }

  /// Ensure stock cache is loaded and up-to-date
  Future<Result<void>> _ensureStockCache() async {
    if (_stockCache != null && 
        _cacheLastUpdated != null && 
        DateTime.now().difference(_cacheLastUpdated!).compareTo(_cacheExpiry) < 0) {
      return Result.success(null);
    }
    
    try {
      Logger.debug('Loading stock cache...');
      
      // Load stocks from repository
      final stocksResult = await _stockRepository.getAllStocks();
      if (stocksResult.isError) {
        return Result.error(stocksResult.errorMessage!);
      }
      
      // Build ticker cache
      _stockCache = {};
      _companyKeywordsCache = {};
      
      for (final stock in stocksResult.data!) {
        _stockCache![stock.ticker] = stock;
        
        // Build keywords cache for each stock
        final keywords = <String>[];
        
        // Add company name variations
        keywords.add(stock.companyName);
        
        // Add common abbreviations
        if (stock.companyName.contains(' ')) {
          final words = stock.companyName.split(' ');
          if (words.length >= 2) {
            keywords.add(words.first); // First word
            if (words.length >= 3) {
              keywords.add('${words.first} ${words[1]}'); // First two words
            }
          }
        }
        
        // Add keywords from stock metadata
        if (stock.keywords != null) {
          keywords.addAll(stock.keywords!);
        }
        
        _companyKeywordsCache![stock.ticker] = keywords;
      }
      
      _cacheLastUpdated = DateTime.now();
      Logger.info('Stock cache loaded: ${_stockCache!.length} stocks');
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to load stock cache', e, stackTrace);
      return Result.error('Failed to load stock data', 
                         RepositoryException('Cache load error', originalError: e));
    }
  }

  /// Clear the stock cache (force reload)
  void clearCache() {
    _stockCache = null;
    _companyKeywordsCache = null;
    _cacheLastUpdated = null;
    Logger.debug('Stock cache cleared');
  }

  /// Get detection statistics
  Map<String, dynamic> getDetectionStatistics() {
    return {
      'cacheSize': _stockCache?.length ?? 0,
      'cacheLastUpdated': _cacheLastUpdated?.toIso8601String(),
      'cacheExpiry': _cacheExpiry.inMinutes,
      'keywordCount': _stockKeywords.length,
    };
  }
}
