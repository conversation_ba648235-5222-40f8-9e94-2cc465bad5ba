/// Stock Metrics Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive stock metrics display with financial ratios and key indicators.

import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../data/models/stock_entity.dart';

/// Stock metrics widget with comprehensive financial data
class StockMetricsWidget extends StatelessWidget {
  final StockEntity stock;

  const StockMetricsWidget({
    super.key,
    required this.stock,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Valuation metrics
          _buildMetricsSection(
            'Valuation Metrics',
            [
              MetricItem('Market Cap', _formatMarketCap(stock.marketCap)),
              MetricItem('P/E Ratio', stock.peRatio?.toStringAsFixed(1) ?? 'N/A'),
              MetricItem('Price Target', stock.priceTarget != null 
                  ? '\$${stock.priceTarget!.toStringAsFixed(2)}' 
                  : 'N/A'),
              MetricItem('Book Value', 'N/A'), // Would be added to StockEntity
              MetricItem('P/B Ratio', 'N/A'),
              MetricItem('EV/EBITDA', 'N/A'),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Dividend metrics
          _buildMetricsSection(
            'Dividend Information',
            [
              MetricItem('Dividend Yield', stock.dividendYield != null 
                  ? '${stock.dividendYield!.toStringAsFixed(2)}%' 
                  : 'N/A'),
              MetricItem('Dividend Per Share', 'N/A'),
              MetricItem('Payout Ratio', 'N/A'),
              MetricItem('Ex-Dividend Date', 'N/A'),
              MetricItem('Dividend Growth', 'N/A'),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Financial health
          _buildMetricsSection(
            'Financial Health',
            [
              MetricItem('Debt-to-Equity', 'N/A'),
              MetricItem('Current Ratio', 'N/A'),
              MetricItem('Quick Ratio', 'N/A'),
              MetricItem('ROE', 'N/A'),
              MetricItem('ROA', 'N/A'),
              MetricItem('Profit Margin', 'N/A'),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Growth metrics
          _buildMetricsSection(
            'Growth Metrics',
            [
              MetricItem('Revenue Growth (YoY)', 'N/A'),
              MetricItem('Earnings Growth (YoY)', 'N/A'),
              MetricItem('Revenue Growth (QoQ)', 'N/A'),
              MetricItem('Earnings Growth (QoQ)', 'N/A'),
              MetricItem('Book Value Growth', 'N/A'),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Trading metrics
          _buildMetricsSection(
            'Trading Information',
            [
              MetricItem('52-Week High', 'N/A'),
              MetricItem('52-Week Low', 'N/A'),
              MetricItem('Average Volume', 'N/A'),
              MetricItem('Beta', 'N/A'),
              MetricItem('Shares Outstanding', 'N/A'),
              MetricItem('Float', 'N/A'),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Company information
          _buildCompanyInfoSection(),
        ],
      ),
    );
  }

  Widget _buildMetricsSection(String title, List<MetricItem> metrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            ...metrics.map((metric) => _buildMetricRow(metric)),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(MetricItem metric) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              metric.label,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              metric.value,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Information',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildInfoRow('Company Name', stock.companyName),
            _buildInfoRow('Ticker Symbol', stock.ticker),
            _buildInfoRow('Sector', stock.sector),
            _buildInfoRow('Industry', stock.industry),
            if (stock.keywords != null && stock.keywords!.isNotEmpty)
              _buildInfoRow('Keywords', stock.keywords!.join(', ')),
            _buildInfoRow('Last Updated', _formatDate(stock.lastUpdated)),
            if (stock.analystRating != null)
              _buildAnalystRatingRow(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.0,
            child: Text(
              label,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalystRatingRow() {
    final rating = stock.analystRating!;
    Color ratingColor;
    String ratingText;
    
    switch (rating) {
      case AnalystRating.strongBuy:
        ratingColor = AppColors.success;
        ratingText = 'Strong Buy';
        break;
      case AnalystRating.buy:
        ratingColor = AppColors.success.withOpacity(0.7);
        ratingText = 'Buy';
        break;
      case AnalystRating.hold:
        ratingColor = AppColors.warning;
        ratingText = 'Hold';
        break;
      case AnalystRating.sell:
        ratingColor = AppColors.error.withOpacity(0.7);
        ratingText = 'Sell';
        break;
      case AnalystRating.strongSell:
        ratingColor = AppColors.error;
        ratingText = 'Strong Sell';
        break;
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.0,
            child: Text(
              'Analyst Rating',
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 6.0,
              ),
              decoration: BoxDecoration(
                color: ratingColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: ratingColor.withOpacity(0.3),
                  width: 1.0,
                ),
              ),
              child: Text(
                ratingText,
                style: AppTheme.textTheme.bodyMedium?.copyWith(
                  color: ratingColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatMarketCap(double? marketCap) {
    if (marketCap == null) return 'N/A';
    
    if (marketCap >= 1e12) {
      return '\$${(marketCap / 1e12).toStringAsFixed(1)}T';
    } else if (marketCap >= 1e9) {
      return '\$${(marketCap / 1e9).toStringAsFixed(1)}B';
    } else if (marketCap >= 1e6) {
      return '\$${(marketCap / 1e6).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Metric item data class
class MetricItem {
  final String label;
  final String value;

  const MetricItem(this.label, this.value);
}
