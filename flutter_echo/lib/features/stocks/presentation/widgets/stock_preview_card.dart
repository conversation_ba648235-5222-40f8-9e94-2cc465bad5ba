/// Stock Preview Card Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Animated stock preview card with flip animations and comprehensive information display.

import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../data/models/stock_entity.dart';

/// Stock preview card with flip animation
class StockPreviewCard extends StatefulWidget {
  final StockEntity stock;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool isFavorite;
  final bool showFlipAnimation;
  final Duration animationDuration;

  const StockPreviewCard({
    super.key,
    required this.stock,
    this.onTap,
    this.onFavorite,
    this.isFavorite = false,
    this.showFlipAnimation = true,
    this.animationDuration = const Duration(milliseconds: 600),
  });

  @override
  State<StockPreviewCard> createState() => _StockPreviewCardState();
}

class _StockPreviewCardState extends State<StockPreviewCard>
    with TickerProviderStateMixin {
  late AnimationController _flipController;
  late AnimationController _hoverController;
  late Animation<double> _flipAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isFlipped = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _flipController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _flipController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (widget.showFlipAnimation) {
      _toggleFlip();
    }
    widget.onTap?.call();
  }

  void _toggleFlip() {
    setState(() {
      _isFlipped = !_isFlipped;
    });
    
    if (_isFlipped) {
      _flipController.forward();
    } else {
      _flipController.reverse();
    }
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: GestureDetector(
        onTap: _handleTap,
        child: AnimatedBuilder(
          animation: Listenable.merge([_flipController, _hoverController]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                height: 200.0,
                margin: const EdgeInsets.all(8.0),
                child: AnimatedBuilder(
                  animation: _flipAnimation,
                  builder: (context, child) {
                    final isShowingFront = _flipAnimation.value < 0.5;
                    return Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001)
                        ..rotateY(_flipAnimation.value * math.pi),
                      child: isShowingFront
                          ? _buildFrontCard()
                          : Transform(
                              alignment: Alignment.center,
                              transform: Matrix4.identity()..rotateY(math.pi),
                              child: _buildBackCard(),
                            ),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFrontCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.surface,
            AppColors.surface.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(_isHovered ? 0.15 : 0.1),
            blurRadius: _isHovered ? 12.0 : 8.0,
            offset: Offset(0, _isHovered ? 6.0 : 4.0),
          ),
        ],
        border: Border.all(
          color: AppColors.border.withOpacity(0.2),
          width: 1.0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with ticker and favorite
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Ticker and company name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.stock.ticker,
                        style: AppTheme.textTheme.headlineSmall?.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 2.0),
                      Text(
                        widget.stock.companyName,
                        style: AppTheme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Favorite button
                IconButton(
                  onPressed: widget.onFavorite,
                  icon: Icon(
                    widget.isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: widget.isFavorite ? AppColors.error : AppColors.textSecondary,
                  ),
                  iconSize: 20.0,
                ),
              ],
            ),
            
            const SizedBox(height: 16.0),
            
            // Price and change
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${widget.stock.price.toStringAsFixed(2)}',
                  style: AppTheme.textTheme.headlineMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8.0),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                  decoration: BoxDecoration(
                    color: widget.stock.changePercent >= 0 
                        ? AppColors.success.withOpacity(0.1)
                        : AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.stock.changePercent >= 0 
                            ? Icons.trending_up 
                            : Icons.trending_down,
                        color: widget.stock.changePercent >= 0 
                            ? AppColors.success 
                            : AppColors.error,
                        size: 16.0,
                      ),
                      const SizedBox(width: 4.0),
                      Text(
                        '${widget.stock.changePercent >= 0 ? '+' : ''}${widget.stock.changePercent.toStringAsFixed(2)}%',
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: widget.stock.changePercent >= 0 
                              ? AppColors.success 
                              : AppColors.error,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const Spacer(),
            
            // Sector and industry
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sector',
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        widget.stock.sector,
                        style: AppTheme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16.0),
                if (widget.stock.analystRating != null)
                  _buildAnalystRating(widget.stock.analystRating!),
              ],
            ),
            
            const SizedBox(height: 8.0),
            
            // Flip indicator
            if (widget.showFlipAnimation)
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.flip,
                        color: AppColors.primary,
                        size: 14.0,
                      ),
                      const SizedBox(width: 4.0),
                      Text(
                        'Tap to flip',
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: AppColors.primary,
                          fontSize: 11.0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(_isHovered ? 0.15 : 0.1),
            blurRadius: _isHovered ? 12.0 : 8.0,
            offset: Offset(0, _isHovered ? 6.0 : 4.0),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1.0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Details',
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 20.0,
                ),
              ],
            ),
            
            const SizedBox(height: 16.0),
            
            // Key metrics
            Expanded(
              child: Column(
                children: [
                  _buildMetricRow('Market Cap', _formatMarketCap(widget.stock.marketCap)),
                  const SizedBox(height: 8.0),
                  _buildMetricRow('P/E Ratio', widget.stock.peRatio?.toStringAsFixed(1) ?? 'N/A'),
                  const SizedBox(height: 8.0),
                  _buildMetricRow('Dividend Yield', widget.stock.dividendYield != null 
                      ? '${widget.stock.dividendYield!.toStringAsFixed(2)}%' 
                      : 'N/A'),
                  const SizedBox(height: 8.0),
                  _buildMetricRow('Price Target', widget.stock.priceTarget != null 
                      ? '\$${widget.stock.priceTarget!.toStringAsFixed(2)}' 
                      : 'N/A'),
                ],
              ),
            ),
            
            // Investment thesis preview
            if (widget.stock.thesis != null && widget.stock.thesis!.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: AppColors.surface.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Investment Thesis',
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      widget.stock.thesis!,
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalystRating(AnalystRating rating) {
    Color ratingColor;
    String ratingText;
    
    switch (rating) {
      case AnalystRating.strongBuy:
        ratingColor = AppColors.success;
        ratingText = 'Strong Buy';
        break;
      case AnalystRating.buy:
        ratingColor = AppColors.success.withOpacity(0.7);
        ratingText = 'Buy';
        break;
      case AnalystRating.hold:
        ratingColor = AppColors.warning;
        ratingText = 'Hold';
        break;
      case AnalystRating.sell:
        ratingColor = AppColors.error.withOpacity(0.7);
        ratingText = 'Sell';
        break;
      case AnalystRating.strongSell:
        ratingColor = AppColors.error;
        ratingText = 'Strong Sell';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: ratingColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: ratingColor.withOpacity(0.3),
          width: 1.0,
        ),
      ),
      child: Text(
        ratingText,
        style: AppTheme.textTheme.bodySmall?.copyWith(
          color: ratingColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          value,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  String _formatMarketCap(double? marketCap) {
    if (marketCap == null) return 'N/A';
    
    if (marketCap >= 1e12) {
      return '\$${(marketCap / 1e12).toStringAsFixed(1)}T';
    } else if (marketCap >= 1e9) {
      return '\$${(marketCap / 1e9).toStringAsFixed(1)}B';
    } else if (marketCap >= 1e6) {
      return '\$${(marketCap / 1e6).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }
}
