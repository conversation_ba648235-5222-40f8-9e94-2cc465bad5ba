/// Stock Chart Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Interactive stock chart widget with price history and technical indicators.

import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../data/models/stock_entity.dart';

/// Stock chart widget with interactive price chart
class StockChartWidget extends StatefulWidget {
  final StockEntity stock;
  final Duration timeframe;
  final bool showVolume;
  final bool showTechnicalIndicators;

  const StockChartWidget({
    super.key,
    required this.stock,
    this.timeframe = const Duration(days: 30),
    this.showVolume = true,
    this.showTechnicalIndicators = false,
  });

  @override
  State<StockChartWidget> createState() => _StockChartWidgetState();
}

class _StockChartWidgetState extends State<StockChartWidget> {
  ChartTimeframe _selectedTimeframe = ChartTimeframe.month;
  bool _showVolume = true;
  bool _showTechnicalIndicators = false;

  @override
  void initState() {
    super.initState();
    _showVolume = widget.showVolume;
    _showTechnicalIndicators = widget.showTechnicalIndicators;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Chart controls
        _buildChartControls(),
        
        const SizedBox(height: 16.0),
        
        // Chart area
        Expanded(
          child: _buildChart(),
        ),
        
        const SizedBox(height: 16.0),
        
        // Chart options
        _buildChartOptions(),
      ],
    );
  }

  Widget _buildChartControls() {
    return Row(
      children: [
        Text(
          'Price Chart',
          style: AppTheme.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        // Timeframe selector
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: AppColors.border.withOpacity(0.2),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: ChartTimeframe.values.map((timeframe) {
              final isSelected = _selectedTimeframe == timeframe;
              return GestureDetector(
                onTap: () => _selectTimeframe(timeframe),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12.0,
                    vertical: 6.0,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primary 
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Text(
                    timeframe.displayName,
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: isSelected 
                          ? Colors.white 
                          : AppColors.textSecondary,
                      fontWeight: isSelected 
                          ? FontWeight.w600 
                          : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildChart() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: AppColors.border.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 64.0,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: 16.0),
            Text(
              'Interactive Chart',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Chart integration with financial data provider\nwould be implemented here',
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16.0),
            // Mock chart data visualization
            _buildMockChart(),
          ],
        ),
      ),
    );
  }

  Widget _buildMockChart() {
    return Container(
      height: 200.0,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: CustomPaint(
        painter: MockChartPainter(
          stock: widget.stock,
          timeframe: _selectedTimeframe,
        ),
      ),
    );
  }

  Widget _buildChartOptions() {
    return Row(
      children: [
        // Volume toggle
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Checkbox(
              value: _showVolume,
              onChanged: (value) {
                setState(() {
                  _showVolume = value ?? false;
                });
              },
              activeColor: AppColors.primary,
            ),
            Text(
              'Show Volume',
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        
        const SizedBox(width: 24.0),
        
        // Technical indicators toggle
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Checkbox(
              value: _showTechnicalIndicators,
              onChanged: (value) {
                setState(() {
                  _showTechnicalIndicators = value ?? false;
                });
              },
              activeColor: AppColors.primary,
            ),
            Text(
              'Technical Indicators',
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _selectTimeframe(ChartTimeframe timeframe) {
    setState(() {
      _selectedTimeframe = timeframe;
    });
  }
}

/// Chart timeframe enumeration
enum ChartTimeframe {
  day,
  week,
  month,
  quarter,
  year,
  fiveYears,
}

/// Extension for chart timeframe
extension ChartTimeframeExtension on ChartTimeframe {
  String get displayName {
    switch (this) {
      case ChartTimeframe.day:
        return '1D';
      case ChartTimeframe.week:
        return '1W';
      case ChartTimeframe.month:
        return '1M';
      case ChartTimeframe.quarter:
        return '3M';
      case ChartTimeframe.year:
        return '1Y';
      case ChartTimeframe.fiveYears:
        return '5Y';
    }
  }

  Duration get duration {
    switch (this) {
      case ChartTimeframe.day:
        return const Duration(days: 1);
      case ChartTimeframe.week:
        return const Duration(days: 7);
      case ChartTimeframe.month:
        return const Duration(days: 30);
      case ChartTimeframe.quarter:
        return const Duration(days: 90);
      case ChartTimeframe.year:
        return const Duration(days: 365);
      case ChartTimeframe.fiveYears:
        return const Duration(days: 1825);
    }
  }
}

/// Mock chart painter for demonstration
class MockChartPainter extends CustomPainter {
  final StockEntity stock;
  final ChartTimeframe timeframe;

  MockChartPainter({
    required this.stock,
    required this.timeframe,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = stock.changePercent >= 0 ? AppColors.success : AppColors.error
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final path = Path();
    
    // Generate mock price data
    final points = _generateMockPriceData(size);
    
    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
    }
    
    canvas.drawPath(path, paint);
    
    // Draw grid lines
    final gridPaint = Paint()
      ..color = AppColors.border.withOpacity(0.2)
      ..strokeWidth = 1.0;
    
    // Horizontal grid lines
    for (int i = 1; i < 5; i++) {
      final y = size.height * i / 5;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // Vertical grid lines
    for (int i = 1; i < 5; i++) {
      final x = size.width * i / 5;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  List<Offset> _generateMockPriceData(Size size) {
    final points = <Offset>[];
    final dataPoints = 50;
    final basePrice = stock.price;
    final volatility = stock.changePercent.abs() / 100;
    
    for (int i = 0; i < dataPoints; i++) {
      final x = size.width * i / (dataPoints - 1);
      
      // Generate mock price movement
      final priceVariation = (i - dataPoints / 2) * volatility * 0.1;
      final randomVariation = (i % 3 - 1) * volatility * 0.05;
      final mockPrice = basePrice + (basePrice * (priceVariation + randomVariation));
      
      // Normalize to chart height
      final minPrice = basePrice * (1 - volatility);
      final maxPrice = basePrice * (1 + volatility);
      final normalizedPrice = (mockPrice - minPrice) / (maxPrice - minPrice);
      final y = size.height * (1 - normalizedPrice);
      
      points.add(Offset(x, y.clamp(0, size.height)));
    }
    
    return points;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! MockChartPainter ||
           oldDelegate.stock != stock ||
           oldDelegate.timeframe != timeframe;
  }
}
