/// Stock News Widget
/// Copyright (c) 2025 Echo Inc.
/// 
/// Stock-related news and updates widget with real-time news feed.

import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';

/// Stock news widget with news feed and updates
class StockNewsWidget extends StatefulWidget {
  final String ticker;

  const StockNewsWidget({
    super.key,
    required this.ticker,
  });

  @override
  State<StockNewsWidget> createState() => _StockNewsWidgetState();
}

class _StockNewsWidgetState extends State<StockNewsWidget> {
  final List<NewsItem> _mockNews = [
    NewsItem(
      title: 'Q4 Earnings Beat Expectations',
      summary: 'Company reports strong quarterly results with revenue growth of 15% year-over-year.',
      source: 'Financial Times',
      publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
      sentiment: NewsSentiment.positive,
    ),
    NewsItem(
      title: 'New Product Launch Announcement',
      summary: 'Company unveils innovative product line expected to drive future growth.',
      source: 'TechCrunch',
      publishedAt: DateTime.now().subtract(const Duration(hours: 6)),
      sentiment: NewsSentiment.positive,
    ),
    NewsItem(
      title: 'Analyst Upgrades Price Target',
      summary: 'Major investment bank raises price target citing strong fundamentals.',
      source: 'Bloomberg',
      publishedAt: DateTime.now().subtract(const Duration(days: 1)),
      sentiment: NewsSentiment.positive,
    ),
    NewsItem(
      title: 'Market Volatility Concerns',
      summary: 'Broader market concerns may impact stock performance in near term.',
      source: 'Reuters',
      publishedAt: DateTime.now().subtract(const Duration(days: 2)),
      sentiment: NewsSentiment.neutral,
    ),
    NewsItem(
      title: 'Regulatory Review Update',
      summary: 'Company provides update on ongoing regulatory review process.',
      source: 'Wall Street Journal',
      publishedAt: DateTime.now().subtract(const Duration(days: 3)),
      sentiment: NewsSentiment.neutral,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Latest News',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: _refreshNews,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.refresh,
                    size: 16.0,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    'Refresh',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16.0),
        
        // News list
        Expanded(
          child: _mockNews.isNotEmpty
              ? ListView.separated(
                  itemCount: _mockNews.length,
                  separatorBuilder: (context, index) => const SizedBox(height: 12.0),
                  itemBuilder: (context, index) {
                    return _buildNewsItem(_mockNews[index]);
                  },
                )
              : _buildEmptyState(),
        ),
      ],
    );
  }

  Widget _buildNewsItem(NewsItem news) {
    return Card(
      child: InkWell(
        onTap: () => _openNewsItem(news),
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and sentiment
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      news.title,
                      style: AppTheme.textTheme.titleSmall?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  _buildSentimentIndicator(news.sentiment),
                ],
              ),
              
              const SizedBox(height: 8.0),
              
              // Summary
              Text(
                news.summary,
                style: AppTheme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12.0),
              
              // Source and time
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    news.source,
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _formatTimeAgo(news.publishedAt),
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSentimentIndicator(NewsSentiment sentiment) {
    Color color;
    IconData icon;
    
    switch (sentiment) {
      case NewsSentiment.positive:
        color = AppColors.success;
        icon = Icons.trending_up;
        break;
      case NewsSentiment.negative:
        color = AppColors.error;
        icon = Icons.trending_down;
        break;
      case NewsSentiment.neutral:
        color = AppColors.warning;
        icon = Icons.trending_flat;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Icon(
        icon,
        size: 16.0,
        color: color,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64.0,
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: 16.0),
          Text(
            'No News Available',
            style: AppTheme.textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'News updates will appear here when available',
            style: AppTheme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _refreshNews() {
    // Implement news refresh
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('News refresh functionality not yet implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _openNewsItem(NewsItem news) {
    // Implement news item opening
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening: ${news.title}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}

/// News item data class
class NewsItem {
  final String title;
  final String summary;
  final String source;
  final DateTime publishedAt;
  final NewsSentiment sentiment;
  final String? url;
  final String? imageUrl;

  const NewsItem({
    required this.title,
    required this.summary,
    required this.source,
    required this.publishedAt,
    required this.sentiment,
    this.url,
    this.imageUrl,
  });
}

/// News sentiment enumeration
enum NewsSentiment {
  positive,
  negative,
  neutral,
}

/// Extension for news sentiment
extension NewsSentimentExtension on NewsSentiment {
  String get displayName {
    switch (this) {
      case NewsSentiment.positive:
        return 'Positive';
      case NewsSentiment.negative:
        return 'Negative';
      case NewsSentiment.neutral:
        return 'Neutral';
    }
  }

  Color get color {
    switch (this) {
      case NewsSentiment.positive:
        return AppColors.success;
      case NewsSentiment.negative:
        return AppColors.error;
      case NewsSentiment.neutral:
        return AppColors.warning;
    }
  }
}
