/// Stock Detail Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// Comprehensive stock detail screen with charts, analyst ratings, and investment thesis.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/colors.dart';
import '../../../../shared/widgets/error_boundary.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../../data/models/stock_entity.dart';
import '../../providers/stock_providers.dart';
import '../widgets/stock_chart_widget.dart';
import '../widgets/stock_metrics_widget.dart';
import '../widgets/stock_news_widget.dart';

/// Stock detail screen with comprehensive information
class StockDetailScreen extends ConsumerStatefulWidget {
  final String ticker;

  const StockDetailScreen({
    super.key,
    required this.ticker,
  });

  @override
  ConsumerState<StockDetailScreen> createState() => _StockDetailScreenState();
}

class _StockDetailScreenState extends ConsumerState<StockDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  
  bool _isAppBarExpanded = true;

  @override
  void initState() {
    super.initState();
    
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();
    
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isExpanded = _scrollController.offset < 100;
    if (isExpanded != _isAppBarExpanded) {
      setState(() {
        _isAppBarExpanded = isExpanded;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final stockAsync = ref.watch(stockByTickerProvider(widget.ticker));
    
    return ErrorBoundary(
      child: Scaffold(
        body: stockAsync.when(
          data: (stock) => stock != null 
              ? _buildStockDetail(stock)
              : _buildNotFound(),
          loading: () => _buildLoading(),
          error: (error, stackTrace) => _buildError(error.toString()),
        ),
      ),
    );
  }

  Widget _buildStockDetail(StockEntity stock) {
    return NestedScrollView(
      controller: _scrollController,
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor: AppColors.surface,
            foregroundColor: AppColors.textPrimary,
            flexibleSpace: FlexibleSpaceBar(
              title: AnimatedOpacity(
                opacity: _isAppBarExpanded ? 0.0 : 1.0,
                duration: const Duration(milliseconds: 200),
                child: Text(
                  stock.ticker,
                  style: AppTheme.textTheme.titleLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primary.withOpacity(0.1),
                      AppColors.surface,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          stock.ticker,
                          style: AppTheme.textTheme.headlineLarge?.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          stock.companyName,
                          style: AppTheme.textTheme.titleMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 16.0),
                        Row(
                          children: [
                            Text(
                              '\$${stock.price.toStringAsFixed(2)}',
                              style: AppTheme.textTheme.headlineMedium?.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 12.0),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 4.0,
                              ),
                              decoration: BoxDecoration(
                                color: stock.changePercent >= 0
                                    ? AppColors.success.withOpacity(0.1)
                                    : AppColors.error.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    stock.changePercent >= 0
                                        ? Icons.trending_up
                                        : Icons.trending_down,
                                    color: stock.changePercent >= 0
                                        ? AppColors.success
                                        : AppColors.error,
                                    size: 16.0,
                                  ),
                                  const SizedBox(width: 4.0),
                                  Text(
                                    '${stock.changePercent >= 0 ? '+' : ''}${stock.changePercent.toStringAsFixed(2)}%',
                                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                                      color: stock.changePercent >= 0
                                          ? AppColors.success
                                          : AppColors.error,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                onPressed: () => _toggleFavorite(stock),
                icon: Icon(
                  Icons.favorite_border, // This would be dynamic based on favorite status
                  color: AppColors.textSecondary,
                ),
              ),
              IconButton(
                onPressed: () => _shareStock(stock),
                icon: Icon(
                  Icons.share,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ];
      },
      body: Column(
        children: [
          // Tab bar
          Container(
            color: AppColors.surface,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
              tabs: const [
                Tab(text: 'Overview'),
                Tab(text: 'Chart'),
                Tab(text: 'Metrics'),
                Tab(text: 'News'),
              ],
            ),
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(stock),
                _buildChartTab(stock),
                _buildMetricsTab(stock),
                _buildNewsTab(stock),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(StockEntity stock) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key metrics summary
          _buildKeyMetrics(stock),
          
          const SizedBox(height: 24.0),
          
          // Investment thesis
          if (stock.thesis != null && stock.thesis!.isNotEmpty)
            _buildInvestmentThesis(stock),
          
          const SizedBox(height: 24.0),
          
          // Analyst rating
          if (stock.analystRating != null)
            _buildAnalystRating(stock),
          
          const SizedBox(height: 24.0),
          
          // Company info
          _buildCompanyInfo(stock),
        ],
      ),
    );
  }

  Widget _buildChartTab(StockEntity stock) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: StockChartWidget(stock: stock),
    );
  }

  Widget _buildMetricsTab(StockEntity stock) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: StockMetricsWidget(stock: stock),
    );
  }

  Widget _buildNewsTab(StockEntity stock) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: StockNewsWidget(ticker: stock.ticker),
    );
  }

  Widget _buildKeyMetrics(StockEntity stock) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Key Metrics',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'Market Cap',
                    _formatMarketCap(stock.marketCap),
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'P/E Ratio',
                    stock.peRatio?.toStringAsFixed(1) ?? 'N/A',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'Dividend Yield',
                    stock.dividendYield != null
                        ? '${stock.dividendYield!.toStringAsFixed(2)}%'
                        : 'N/A',
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'Price Target',
                    stock.priceTarget != null
                        ? '\$${stock.priceTarget!.toStringAsFixed(2)}'
                        : 'N/A',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          value,
          style: AppTheme.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInvestmentThesis(StockEntity stock) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Investment Thesis',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              stock.thesis!,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalystRating(StockEntity stock) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analyst Rating',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12.0),
            // Rating would be displayed here
            Text(
              stock.analystRating!.name.toUpperCase(),
              style: AppTheme.textTheme.titleLarge?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo(StockEntity stock) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Information',
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildInfoRow('Sector', stock.sector),
            const SizedBox(height: 8.0),
            _buildInfoRow('Industry', stock.industry),
            if (stock.keywords != null && stock.keywords!.isNotEmpty) ...[
              const SizedBox(height: 8.0),
              _buildInfoRow('Keywords', stock.keywords!.join(', ')),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.0,
          child: Text(
            label,
            style: AppTheme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoading() {
    return const Scaffold(
      body: Center(
        child: LoadingIndicator(),
      ),
    );
  }

  Widget _buildError(String error) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.0,
                color: AppColors.error,
              ),
              const SizedBox(height: 16.0),
              Text(
                'Failed to load stock data',
                style: AppTheme.textTheme.titleLarge?.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8.0),
              Text(
                error,
                style: AppTheme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24.0),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotFound() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stock Not Found'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64.0,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 16.0),
              Text(
                'Stock "${widget.ticker}" not found',
                style: AppTheme.textTheme.titleLarge?.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 24.0),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatMarketCap(double? marketCap) {
    if (marketCap == null) return 'N/A';
    
    if (marketCap >= 1e12) {
      return '\$${(marketCap / 1e12).toStringAsFixed(1)}T';
    } else if (marketCap >= 1e9) {
      return '\$${(marketCap / 1e9).toStringAsFixed(1)}B';
    } else if (marketCap >= 1e6) {
      return '\$${(marketCap / 1e6).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  void _toggleFavorite(StockEntity stock) {
    // Implement favorite toggle
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Favorite functionality not yet implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _shareStock(StockEntity stock) {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Share functionality not yet implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }
}
