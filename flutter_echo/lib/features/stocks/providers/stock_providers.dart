/// Stock Providers
/// Copyright (c) 2025 Echo Inc.
/// 
/// Riverpod providers for stock-related services and state management.

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/network/api_client.dart';
import '../../../core/providers/app_providers.dart';
import '../data/repositories/stock_repository.dart';
import '../data/models/stock_entity.dart';
import '../data/models/stock_mention.dart';
import '../services/stock_detection_service.dart';

// Repository Providers

/// Stock repository provider
final stockRepositoryProvider = Provider<StockRepository>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return StockRepository(apiClient);
});

/// Stock detection service provider
final stockDetectionServiceProvider = Provider<StockDetectionService>((ref) {
  final stockRepository = ref.watch(stockRepositoryProvider);
  return StockDetectionService(stockRepository);
});

// Data Providers

/// All stocks provider
final allStocksProvider = FutureProvider<List<StockEntity>>((ref) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getAllStocks();
  return result.fold(
    onSuccess: (stocks) => stocks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stock by ticker provider
final stockByTickerProvider = FutureProvider.family<StockEntity?, String>((ref, ticker) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getStockByTicker(ticker);
  return result.fold(
    onSuccess: (stock) => stock,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stock search provider
final stockSearchProvider = FutureProvider.family<List<StockEntity>, String>((ref, query) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.searchStocks(query);
  return result.fold(
    onSuccess: (stocks) => stocks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stocks by sector provider
final stocksBySectorProvider = FutureProvider.family<List<StockEntity>, String>((ref, sector) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getStocksBySector(sector);
  return result.fold(
    onSuccess: (stocks) => stocks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Trending stocks provider
final trendingStocksProvider = FutureProvider.family<List<StockEntity>, int>((ref, limit) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getTrendingStocks(limit: limit);
  return result.fold(
    onSuccess: (stocks) => stocks,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stock storage statistics provider
final stockStorageStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getStorageStatistics();
  return result.fold(
    onSuccess: (stats) => stats,
    onError: (message, exception) => throw Exception(message),
  );
});

// Stock Detection Providers

/// Stock detection provider
final stockDetectionProvider = FutureProvider.family<List<StockMention>, String>((ref, text) async {
  final service = ref.watch(stockDetectionServiceProvider);
  final result = await service.detectStocks(text);
  return result.fold(
    onSuccess: (mentions) => mentions,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stock mention statistics provider
final stockMentionStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getStockMentionStatistics();
  return result.fold(
    onSuccess: (stats) => stats,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Recent stock mentions provider
final recentStockMentionsProvider = FutureProvider.family<List<StockMention>, int>((ref, limit) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getRecentStockMentions(limit: limit);
  return result.fold(
    onSuccess: (mentions) => mentions,
    onError: (message, exception) => throw Exception(message),
  );
});

/// Stock mentions by conversation provider
final stockMentionsByConversationProvider = FutureProvider.family<List<StockMention>, String>((ref, conversationId) async {
  final repository = ref.watch(stockRepositoryProvider);
  final result = await repository.getStockMentionsByConversation(conversationId);
  return result.fold(
    onSuccess: (mentions) => mentions,
    onError: (message, exception) => throw Exception(message),
  );
});

// State Providers

/// Selected stock provider
final selectedStockProvider = StateProvider<StockEntity?>((ref) => null);

/// Stock search query provider
final stockSearchQueryProvider = StateProvider<String>((ref) => '');

/// Stock filter settings provider
final stockFilterSettingsProvider = StateProvider<StockFilterSettings>((ref) {
  return const StockFilterSettings();
});

/// Favorite stocks provider
final favoriteStocksProvider = StateNotifierProvider<FavoriteStocksNotifier, Set<String>>((ref) {
  return FavoriteStocksNotifier();
});

/// Stock watchlist provider
final stockWatchlistProvider = StateNotifierProvider<StockWatchlistNotifier, List<String>>((ref) {
  return StockWatchlistNotifier();
});

// State Notifiers

/// Favorite stocks notifier
class FavoriteStocksNotifier extends StateNotifier<Set<String>> {
  FavoriteStocksNotifier() : super(<String>{});

  void addFavorite(String ticker) {
    state = {...state, ticker};
  }

  void removeFavorite(String ticker) {
    state = state.where((t) => t != ticker).toSet();
  }

  void toggleFavorite(String ticker) {
    if (state.contains(ticker)) {
      removeFavorite(ticker);
    } else {
      addFavorite(ticker);
    }
  }

  bool isFavorite(String ticker) {
    return state.contains(ticker);
  }

  void clearFavorites() {
    state = <String>{};
  }
}

/// Stock watchlist notifier
class StockWatchlistNotifier extends StateNotifier<List<String>> {
  StockWatchlistNotifier() : super(<String>[]);

  void addToWatchlist(String ticker) {
    if (!state.contains(ticker)) {
      state = [...state, ticker];
    }
  }

  void removeFromWatchlist(String ticker) {
    state = state.where((t) => t != ticker).toList();
  }

  void reorderWatchlist(int oldIndex, int newIndex) {
    final items = List<String>.from(state);
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final item = items.removeAt(oldIndex);
    items.insert(newIndex, item);
    state = items;
  }

  bool isInWatchlist(String ticker) {
    return state.contains(ticker);
  }

  void clearWatchlist() {
    state = <String>[];
  }
}

// Settings Classes

/// Stock filter settings
class StockFilterSettings {
  final List<String> sectors;
  final List<String> industries;
  final double? minPrice;
  final double? maxPrice;
  final double? minMarketCap;
  final double? maxMarketCap;
  final List<AnalystRating> analystRatings;
  final StockSortBy sortBy;
  final bool sortAscending;

  const StockFilterSettings({
    this.sectors = const [],
    this.industries = const [],
    this.minPrice,
    this.maxPrice,
    this.minMarketCap,
    this.maxMarketCap,
    this.analystRatings = const [],
    this.sortBy = StockSortBy.ticker,
    this.sortAscending = true,
  });

  StockFilterSettings copyWith({
    List<String>? sectors,
    List<String>? industries,
    double? minPrice,
    double? maxPrice,
    double? minMarketCap,
    double? maxMarketCap,
    List<AnalystRating>? analystRatings,
    StockSortBy? sortBy,
    bool? sortAscending,
  }) {
    return StockFilterSettings(
      sectors: sectors ?? this.sectors,
      industries: industries ?? this.industries,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minMarketCap: minMarketCap ?? this.minMarketCap,
      maxMarketCap: maxMarketCap ?? this.maxMarketCap,
      analystRatings: analystRatings ?? this.analystRatings,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  bool get hasActiveFilters {
    return sectors.isNotEmpty ||
           industries.isNotEmpty ||
           minPrice != null ||
           maxPrice != null ||
           minMarketCap != null ||
           maxMarketCap != null ||
           analystRatings.isNotEmpty;
  }

  bool matchesStock(StockEntity stock) {
    // Sector filter
    if (sectors.isNotEmpty && !sectors.contains(stock.sector)) {
      return false;
    }

    // Industry filter
    if (industries.isNotEmpty && !industries.contains(stock.industry)) {
      return false;
    }

    // Price filters
    if (minPrice != null && stock.price < minPrice!) {
      return false;
    }
    if (maxPrice != null && stock.price > maxPrice!) {
      return false;
    }

    // Market cap filters
    if (minMarketCap != null && (stock.marketCap ?? 0) < minMarketCap!) {
      return false;
    }
    if (maxMarketCap != null && (stock.marketCap ?? 0) > maxMarketCap!) {
      return false;
    }

    // Analyst rating filter
    if (analystRatings.isNotEmpty && 
        (stock.analystRating == null || !analystRatings.contains(stock.analystRating!))) {
      return false;
    }

    return true;
  }
}

/// Stock sort options
enum StockSortBy {
  ticker,
  companyName,
  price,
  changePercent,
  marketCap,
  sector,
  analystRating,
}

/// Extension for stock sort by
extension StockSortByExtension on StockSortBy {
  String get displayName {
    switch (this) {
      case StockSortBy.ticker:
        return 'Ticker';
      case StockSortBy.companyName:
        return 'Company Name';
      case StockSortBy.price:
        return 'Price';
      case StockSortBy.changePercent:
        return 'Change %';
      case StockSortBy.marketCap:
        return 'Market Cap';
      case StockSortBy.sector:
        return 'Sector';
      case StockSortBy.analystRating:
        return 'Analyst Rating';
    }
  }

  int compareStocks(StockEntity a, StockEntity b, bool ascending) {
    int comparison;
    
    switch (this) {
      case StockSortBy.ticker:
        comparison = a.ticker.compareTo(b.ticker);
        break;
      case StockSortBy.companyName:
        comparison = a.companyName.compareTo(b.companyName);
        break;
      case StockSortBy.price:
        comparison = a.price.compareTo(b.price);
        break;
      case StockSortBy.changePercent:
        comparison = a.changePercent.compareTo(b.changePercent);
        break;
      case StockSortBy.marketCap:
        comparison = (a.marketCap ?? 0).compareTo(b.marketCap ?? 0);
        break;
      case StockSortBy.sector:
        comparison = a.sector.compareTo(b.sector);
        break;
      case StockSortBy.analystRating:
        final aRating = a.analystRating?.index ?? -1;
        final bRating = b.analystRating?.index ?? -1;
        comparison = aRating.compareTo(bRating);
        break;
    }
    
    return ascending ? comparison : -comparison;
  }
}
