/// Conflict Resolution Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for resolving data conflicts during offline synchronization.

import 'dart:convert';

import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../chat/data/models/conversation_entity.dart';
import '../../chat/data/models/message_entity.dart';
import '../data/models/sync_queue_entity.dart';

/// Service for resolving sync conflicts
class ConflictResolutionService {
  /// Resolve conflict between local and remote data
  static Future<Result<ConflictResolution>> resolveConflict({
    required String entityType,
    required Map<String, dynamic> localData,
    required Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy = ConflictResolutionStrategy.lastWriteWins,
  }) async {
    try {
      Logger.debug('Resolving conflict for $entityType with strategy: $strategy');
      
      switch (entityType) {
        case 'conversation':
          return await _resolveConversationConflict(localData, remoteData, strategy);
        case 'message':
          return await _resolveMessageConflict(localData, remoteData, strategy);
        case 'lead':
          return await _resolveLeadConflict(localData, remoteData, strategy);
        case 'user':
          return await _resolveUserConflict(localData, remoteData, strategy);
        default:
          return await _resolveGenericConflict(localData, remoteData, strategy);
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to resolve conflict', e, stackTrace);
      return Result.error('Conflict resolution failed', 
                         RepositoryException('Conflict resolution error', originalError: e));
    }
  }

  /// Resolve conversation conflict
  static Future<Result<ConflictResolution>> _resolveConversationConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy,
  ) async {
    final localConv = ConversationEntity.fromJson(localData);
    final remoteConv = ConversationEntity.fromJson(remoteData);
    
    switch (strategy) {
      case ConflictResolutionStrategy.lastWriteWins:
        final winner = localConv.metadata.updatedAt.isAfter(remoteConv.metadata.updatedAt)
            ? localConv
            : remoteConv;
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: winner.toJson(),
          reason: 'Last write wins: ${winner == localConv ? 'local' : 'remote'} data is newer',
        ));
        
      case ConflictResolutionStrategy.mergeMessages:
        return await _mergeConversationMessages(localConv, remoteConv);
        
      case ConflictResolutionStrategy.preferLocal:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: localConv.toJson(),
          reason: 'Prefer local strategy applied',
        ));
        
      case ConflictResolutionStrategy.preferRemote:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useRemote,
          resolvedData: remoteConv.toJson(),
          reason: 'Prefer remote strategy applied',
        ));
        
      case ConflictResolutionStrategy.manual:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.requiresManualResolution,
          conflictData: {
            'local': localConv.toJson(),
            'remote': remoteConv.toJson(),
          },
          reason: 'Manual resolution required',
        ));
    }
  }

  /// Merge conversation messages
  static Future<Result<ConflictResolution>> _mergeConversationMessages(
    ConversationEntity localConv,
    ConversationEntity remoteConv,
  ) async {
    try {
      // Create a map of messages by ID for efficient lookup
      final localMessages = <String, MessageEntity>{};
      for (final message in localConv.messages) {
        localMessages[message.id] = message;
      }
      
      final remoteMessages = <String, MessageEntity>{};
      for (final message in remoteConv.messages) {
        remoteMessages[message.id] = message;
      }
      
      // Merge messages
      final mergedMessages = <MessageEntity>[];
      final allMessageIds = {...localMessages.keys, ...remoteMessages.keys};
      
      for (final messageId in allMessageIds) {
        final localMessage = localMessages[messageId];
        final remoteMessage = remoteMessages[messageId];
        
        if (localMessage != null && remoteMessage != null) {
          // Both exist, choose newer one
          final newerMessage = localMessage.timestamp.isAfter(remoteMessage.timestamp)
              ? localMessage
              : remoteMessage;
          mergedMessages.add(newerMessage);
        } else if (localMessage != null) {
          // Only local exists
          mergedMessages.add(localMessage);
        } else if (remoteMessage != null) {
          // Only remote exists
          mergedMessages.add(remoteMessage);
        }
      }
      
      // Sort messages by timestamp
      mergedMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      // Create merged conversation
      final mergedConv = localConv.copyWith(
        messages: mergedMessages,
        messageCount: mergedMessages.length,
        lastActivity: mergedMessages.isNotEmpty 
            ? mergedMessages.last.timestamp 
            : localConv.lastActivity,
        metadata: localConv.metadata.copyWith(
          updatedAt: DateTime.now(),
          version: localConv.metadata.version + 1,
        ),
      );
      
      return Result.success(ConflictResolution(
        resolution: ConflictResolutionType.merged,
        resolvedData: mergedConv.toJson(),
        reason: 'Messages merged successfully: ${mergedMessages.length} total messages',
      ));
      
    } catch (e, stackTrace) {
      Logger.error('Failed to merge conversation messages', e, stackTrace);
      return Result.error('Message merge failed', 
                         RepositoryException('Merge error', originalError: e));
    }
  }

  /// Resolve message conflict
  static Future<Result<ConflictResolution>> _resolveMessageConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy,
  ) async {
    final localMessage = MessageEntity.fromJson(localData);
    final remoteMessage = MessageEntity.fromJson(remoteData);
    
    // Messages are typically immutable, so conflicts are rare
    // Use timestamp to determine winner
    final winner = localMessage.timestamp.isAfter(remoteMessage.timestamp)
        ? localMessage
        : remoteMessage;
    
    return Result.success(ConflictResolution(
      resolution: ConflictResolutionType.useLocal,
      resolvedData: winner.toJson(),
      reason: 'Message conflict resolved by timestamp',
    ));
  }

  /// Resolve lead conflict
  static Future<Result<ConflictResolution>> _resolveLeadConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy,
  ) async {
    switch (strategy) {
      case ConflictResolutionStrategy.lastWriteWins:
        final localExtracted = DateTime.parse(localData['extractedAt'] as String);
        final remoteExtracted = DateTime.parse(remoteData['extractedAt'] as String);
        
        final winner = localExtracted.isAfter(remoteExtracted) ? localData : remoteData;
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: winner,
          reason: 'Lead conflict resolved by extraction time',
        ));
        
      case ConflictResolutionStrategy.mergeMessages:
        return await _mergeLeadData(localData, remoteData);
        
      default:
        return await _resolveGenericConflict(localData, remoteData, strategy);
    }
  }

  /// Merge lead data
  static Future<Result<ConflictResolution>> _mergeLeadData(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
  ) async {
    final merged = Map<String, dynamic>.from(localData);
    
    // Merge non-null fields from remote
    for (final entry in remoteData.entries) {
      if (entry.value != null && merged[entry.key] == null) {
        merged[entry.key] = entry.value;
      }
    }
    
    // Use higher qualification score
    final localScore = (localData['qualificationScore'] as num?)?.toDouble() ?? 0.0;
    final remoteScore = (remoteData['qualificationScore'] as num?)?.toDouble() ?? 0.0;
    merged['qualificationScore'] = localScore > remoteScore ? localScore : remoteScore;
    
    return Result.success(ConflictResolution(
      resolution: ConflictResolutionType.merged,
      resolvedData: merged,
      reason: 'Lead data merged with best available information',
    ));
  }

  /// Resolve user conflict
  static Future<Result<ConflictResolution>> _resolveUserConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy,
  ) async {
    // User data conflicts should prefer remote (server is authoritative)
    return Result.success(ConflictResolution(
      resolution: ConflictResolutionType.useRemote,
      resolvedData: remoteData,
      reason: 'User data conflicts resolved by preferring server data',
    ));
  }

  /// Resolve generic conflict
  static Future<Result<ConflictResolution>> _resolveGenericConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    ConflictResolutionStrategy strategy,
  ) async {
    switch (strategy) {
      case ConflictResolutionStrategy.lastWriteWins:
        // Try to find timestamp fields
        final localTime = _extractTimestamp(localData);
        final remoteTime = _extractTimestamp(remoteData);
        
        if (localTime != null && remoteTime != null) {
          final winner = localTime.isAfter(remoteTime) ? localData : remoteData;
          return Result.success(ConflictResolution(
            resolution: ConflictResolutionType.useLocal,
            resolvedData: winner,
            reason: 'Generic conflict resolved by timestamp',
          ));
        }
        
        // Fallback to prefer local
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: localData,
          reason: 'Generic conflict resolved by preferring local (no timestamps)',
        ));
        
      case ConflictResolutionStrategy.preferLocal:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: localData,
          reason: 'Prefer local strategy applied',
        ));
        
      case ConflictResolutionStrategy.preferRemote:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useRemote,
          resolvedData: remoteData,
          reason: 'Prefer remote strategy applied',
        ));
        
      case ConflictResolutionStrategy.manual:
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.requiresManualResolution,
          conflictData: {
            'local': localData,
            'remote': remoteData,
          },
          reason: 'Manual resolution required',
        ));
        
      case ConflictResolutionStrategy.mergeMessages:
        // Generic merge not supported
        return Result.success(ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          resolvedData: localData,
          reason: 'Generic merge not supported, using local data',
        ));
    }
  }

  /// Extract timestamp from data
  static DateTime? _extractTimestamp(Map<String, dynamic> data) {
    final timestampFields = ['updatedAt', 'timestamp', 'lastModified', 'createdAt'];
    
    for (final field in timestampFields) {
      final value = data[field];
      if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (_) {
          continue;
        }
      }
    }
    
    return null;
  }
}

/// Conflict resolution strategy
enum ConflictResolutionStrategy {
  lastWriteWins,
  preferLocal,
  preferRemote,
  mergeMessages,
  manual,
}

/// Conflict resolution type
enum ConflictResolutionType {
  useLocal,
  useRemote,
  merged,
  requiresManualResolution,
}

/// Conflict resolution result
class ConflictResolution {
  final ConflictResolutionType resolution;
  final Map<String, dynamic>? resolvedData;
  final Map<String, dynamic>? conflictData;
  final String reason;

  ConflictResolution({
    required this.resolution,
    this.resolvedData,
    this.conflictData,
    required this.reason,
  });

  bool get isResolved => resolution != ConflictResolutionType.requiresManualResolution;
  bool get requiresManualResolution => resolution == ConflictResolutionType.requiresManualResolution;

  Map<String, dynamic> toJson() {
    return {
      'resolution': resolution.name,
      'resolvedData': resolvedData,
      'conflictData': conflictData,
      'reason': reason,
    };
  }

  @override
  String toString() {
    return 'ConflictResolution(resolution: $resolution, reason: $reason)';
  }
}
