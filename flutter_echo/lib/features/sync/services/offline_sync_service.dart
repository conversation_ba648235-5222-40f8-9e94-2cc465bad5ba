/// Offline Sync Service
/// Copyright (c) 2025 Echo Inc.
/// 
/// Service for managing offline data synchronization with conflict resolution.

import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:uuid/uuid.dart';

import '../../../core/storage/hive_setup.dart';
import '../../../core/utils/result.dart';
import '../../../core/utils/logger.dart';
import '../../../core/network/api_client.dart';
import '../data/models/sync_queue_entity.dart';

/// Service for offline data synchronization
class OfflineSyncService {
  final ApiClient _apiClient;
  final Connectivity _connectivity;
  final _uuid = const Uuid();
  
  Timer? _syncTimer;
  bool _isSyncing = false;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  // Sync configuration
  static const Duration _syncInterval = Duration(minutes: 5);
  static const int _maxConcurrentSyncs = 3;
  static const int _maxRetryAttempts = 5;

  OfflineSyncService(this._apiClient, this._connectivity);

  /// Initialize sync service
  Future<void> initialize() async {
    try {
      Logger.info('Initializing offline sync service...');
      
      // Start periodic sync
      _startPeriodicSync();
      
      // Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
      );
      
      // Perform initial sync if connected
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        await syncPendingOperations();
      }
      
      Logger.info('Offline sync service initialized');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize offline sync service', e, stackTrace);
    }
  }

  /// Dispose sync service
  void dispose() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
    Logger.info('Offline sync service disposed');
  }

  /// Queue operation for sync
  Future<Result<void>> queueOperation({
    required String entityType,
    required String entityId,
    required SyncOperationType operation,
    required Map<String, dynamic> data,
    int priority = 5,
    DateTime? scheduledFor,
    String? dependsOn,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final syncOperation = SyncQueueEntity(
        id: _uuid.v4(),
        entityType: entityType,
        entityId: entityId,
        operation: operation,
        data: data,
        createdAt: DateTime.now(),
        priority: priority,
        scheduledFor: scheduledFor,
        dependsOn: dependsOn,
        metadata: metadata,
      );

      final box = HiveSetup.syncQueueBox;
      await box.put(syncOperation.id, syncOperation);
      
      Logger.debug('Queued sync operation: ${syncOperation.id} ($entityType:$operation)');
      
      // Trigger immediate sync if connected
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        unawaited(_syncSingleOperation(syncOperation));
      }
      
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to queue sync operation', e, stackTrace);
      return Result.error('Failed to queue operation', 
                         StorageException('Sync queue error', originalError: e));
    }
  }

  /// Sync all pending operations
  Future<Result<SyncResult>> syncPendingOperations() async {
    if (_isSyncing) {
      Logger.debug('Sync already in progress, skipping');
      return Result.success(SyncResult.empty());
    }

    try {
      _isSyncing = true;
      Logger.info('Starting sync of pending operations...');
      
      final box = HiveSetup.syncQueueBox;
      final pendingOperations = box.values
          .where((op) => op.isReadyToSync || op.shouldRetry)
          .toList();
      
      if (pendingOperations.isEmpty) {
        Logger.debug('No pending operations to sync');
        return Result.success(SyncResult.empty());
      }

      // Sort by priority and creation time
      pendingOperations.sort((a, b) {
        final priorityCompare = a.priority.compareTo(b.priority);
        if (priorityCompare != 0) return priorityCompare;
        return a.createdAt.compareTo(b.createdAt);
      });

      final result = SyncResult();
      final futures = <Future<void>>[];
      
      // Process operations with concurrency limit
      for (int i = 0; i < pendingOperations.length; i += _maxConcurrentSyncs) {
        final batch = pendingOperations.skip(i).take(_maxConcurrentSyncs);
        
        for (final operation in batch) {
          futures.add(_syncSingleOperation(operation).then((opResult) {
            if (opResult.isSuccess) {
              result.successful++;
            } else {
              result.failed++;
              result.errors.add(opResult.errorMessage ?? 'Unknown error');
            }
          }));
        }
        
        // Wait for current batch to complete before starting next
        await Future.wait(futures);
        futures.clear();
      }
      
      Logger.info('Sync completed: ${result.successful} successful, ${result.failed} failed');
      return Result.success(result);
      
    } catch (e, stackTrace) {
      Logger.error('Failed to sync pending operations', e, stackTrace);
      return Result.error('Sync failed', 
                         NetworkException('Sync error', originalError: e));
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync single operation
  Future<Result<void>> _syncSingleOperation(SyncQueueEntity operation) async {
    try {
      Logger.debug('Syncing operation: ${operation.id}');
      
      // Check dependencies
      if (operation.dependsOn != null) {
        final dependency = HiveSetup.syncQueueBox.get(operation.dependsOn!);
        if (dependency?.status != SyncStatus.completed) {
          Logger.debug('Operation ${operation.id} waiting for dependency ${operation.dependsOn}');
          return Result.success(null);
        }
      }

      operation.markInProgress();
      
      // Execute sync based on entity type and operation
      final result = await _executeSyncOperation(operation);
      
      if (result.isSuccess) {
        operation.markCompleted();
        Logger.debug('Operation ${operation.id} completed successfully');
      } else {
        operation.markFailed(result.errorMessage ?? 'Unknown error');
        Logger.warning('Operation ${operation.id} failed: ${result.errorMessage}');
      }
      
      return result;
      
    } catch (e, stackTrace) {
      operation.markFailed(e.toString());
      Logger.error('Failed to sync operation: ${operation.id}', e, stackTrace);
      return Result.error('Operation sync failed', 
                         NetworkException('Sync error', originalError: e));
    }
  }

  /// Execute specific sync operation
  Future<Result<void>> _executeSyncOperation(SyncQueueEntity operation) async {
    try {
      switch (operation.entityType) {
        case 'conversation':
          return await _syncConversation(operation);
        case 'message':
          return await _syncMessage(operation);
        case 'stock':
          return await _syncStock(operation);
        case 'lead':
          return await _syncLead(operation);
        case 'user':
          return await _syncUser(operation);
        default:
          return Result.error('Unknown entity type: ${operation.entityType}');
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to execute sync operation', e, stackTrace);
      return Result.error('Sync execution failed', 
                         NetworkException('Sync error', originalError: e));
    }
  }

  /// Sync conversation
  Future<Result<void>> _syncConversation(SyncQueueEntity operation) async {
    switch (operation.operation) {
      case SyncOperationType.create:
        return await _apiClient.post('/api/mobile/conversations', operation.data);
      case SyncOperationType.update:
        return await _apiClient.put('/api/mobile/conversations/${operation.entityId}', operation.data);
      case SyncOperationType.delete:
        return await _apiClient.delete('/api/mobile/conversations/${operation.entityId}');
      default:
        return Result.error('Unsupported conversation operation: ${operation.operation}');
    }
  }

  /// Sync message
  Future<Result<void>> _syncMessage(SyncQueueEntity operation) async {
    switch (operation.operation) {
      case SyncOperationType.create:
        return await _apiClient.post('/api/mobile/messages', operation.data);
      case SyncOperationType.update:
        return await _apiClient.put('/api/mobile/messages/${operation.entityId}', operation.data);
      case SyncOperationType.delete:
        return await _apiClient.delete('/api/mobile/messages/${operation.entityId}');
      default:
        return Result.error('Unsupported message operation: ${operation.operation}');
    }
  }

  /// Sync stock
  Future<Result<void>> _syncStock(SyncQueueEntity operation) async {
    // Stocks are typically read-only, but we might sync user preferences
    switch (operation.operation) {
      case SyncOperationType.sync:
        return await _apiClient.get('/api/mobile/stocks/${operation.entityId}');
      default:
        return Result.error('Unsupported stock operation: ${operation.operation}');
    }
  }

  /// Sync lead
  Future<Result<void>> _syncLead(SyncQueueEntity operation) async {
    switch (operation.operation) {
      case SyncOperationType.create:
        return await _apiClient.post('/api/mobile/leads', operation.data);
      case SyncOperationType.update:
        return await _apiClient.put('/api/mobile/leads/${operation.entityId}', operation.data);
      case SyncOperationType.delete:
        return await _apiClient.delete('/api/mobile/leads/${operation.entityId}');
      default:
        return Result.error('Unsupported lead operation: ${operation.operation}');
    }
  }

  /// Sync user
  Future<Result<void>> _syncUser(SyncQueueEntity operation) async {
    switch (operation.operation) {
      case SyncOperationType.update:
        return await _apiClient.put('/api/mobile/users/${operation.entityId}', operation.data);
      default:
        return Result.error('Unsupported user operation: ${operation.operation}');
    }
  }

  /// Start periodic sync
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(_syncInterval, (_) {
      unawaited(syncPendingOperations());
    });
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      Logger.info('Network connectivity restored, starting sync...');
      unawaited(syncPendingOperations());
    } else {
      Logger.info('Network connectivity lost');
    }
  }

  /// Get sync statistics
  Future<Result<Map<String, dynamic>>> getSyncStatistics() async {
    try {
      final box = HiveSetup.syncQueueBox;
      final operations = box.values.toList();
      
      final stats = {
        'totalOperations': operations.length,
        'pendingOperations': operations.where((op) => op.status == SyncStatus.pending).length,
        'inProgressOperations': operations.where((op) => op.status == SyncStatus.inProgress).length,
        'completedOperations': operations.where((op) => op.status == SyncStatus.completed).length,
        'failedOperations': operations.where((op) => op.status == SyncStatus.failed).length,
        'retryingOperations': operations.where((op) => op.status == SyncStatus.retrying).length,
        'oldestPendingOperation': operations
            .where((op) => op.status == SyncStatus.pending)
            .fold<DateTime?>(null, (oldest, op) => 
                oldest == null || op.createdAt.isBefore(oldest) ? op.createdAt : oldest)
            ?.toIso8601String(),
      };
      
      return Result.success(stats);
    } catch (e, stackTrace) {
      Logger.error('Failed to get sync statistics', e, stackTrace);
      return Result.error('Failed to get statistics', 
                         StorageException('Sync stats error', originalError: e));
    }
  }

  /// Clear completed operations
  Future<Result<void>> clearCompletedOperations() async {
    try {
      final box = HiveSetup.syncQueueBox;
      final completedKeys = box.values
          .where((op) => op.status == SyncStatus.completed)
          .map((op) => op.key)
          .toList();
      
      await box.deleteAll(completedKeys);
      
      Logger.info('Cleared ${completedKeys.length} completed sync operations');
      return Result.success(null);
    } catch (e, stackTrace) {
      Logger.error('Failed to clear completed operations', e, stackTrace);
      return Result.error('Failed to clear operations', 
                         StorageException('Sync clear error', originalError: e));
    }
  }
}

/// Sync result
class SyncResult {
  int successful = 0;
  int failed = 0;
  List<String> errors = [];

  SyncResult();
  
  SyncResult.empty();

  bool get hasErrors => errors.isNotEmpty;
  int get total => successful + failed;
}

/// Helper for unawaited futures
void unawaited(Future<void> future) {
  // Intentionally not awaited
}
