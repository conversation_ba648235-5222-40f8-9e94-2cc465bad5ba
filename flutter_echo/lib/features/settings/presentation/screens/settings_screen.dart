/// Settings Screen
/// Copyright (c) 2025 Echo Inc.
/// 
/// Application settings and preferences screen.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/colors.dart';
import '../../../../core/providers/app_providers.dart';

/// Settings screen for app preferences and configuration
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Voice Settings
          _buildSectionHeader('Voice Settings'),
          _buildVoiceSettings(),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Security Settings
          _buildSectionHeader('Security'),
          _buildSecuritySettings(),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // App Settings
          _buildSectionHeader('App Settings'),
          _buildAppSettings(),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // About
          _buildSectionHeader('About'),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildVoiceSettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Voice Recording'),
            subtitle: const Text('Enable voice input for conversations'),
            value: true,
            onChanged: (value) {
              // TODO: Update voice settings
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Voice Quality'),
            subtitle: const Text('High quality (16kHz)'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showVoiceQualityDialog();
            },
          ),
          
          const Divider(height: 1),
          
          SwitchListTile(
            title: const Text('Auto-play Responses'),
            subtitle: const Text('Automatically play AI voice responses'),
            value: false,
            onChanged: (value) {
              // TODO: Update auto-play setting
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Biometric Authentication'),
            subtitle: const Text('Use fingerprint or face unlock'),
            value: true,
            onChanged: (value) {
              // TODO: Update biometric setting
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Session Timeout'),
            subtitle: const Text('24 hours'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showSessionTimeoutDialog();
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Clear Session Data'),
            subtitle: const Text('Sign out and clear local data'),
            trailing: const Icon(Icons.logout, color: AppColors.error),
            onTap: () {
              _showClearDataDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings() {
    final themeMode = ref.watch(themeModeProvider);
    
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('Theme'),
            subtitle: Text(_getThemeModeText(themeMode)),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showThemeDialog();
            },
          ),
          
          const Divider(height: 1),
          
          SwitchListTile(
            title: const Text('Analytics'),
            subtitle: const Text('Help improve the app with usage data'),
            value: AppConstants.enableAnalytics,
            onChanged: (value) {
              // TODO: Update analytics setting
            },
          ),
          
          const Divider(height: 1),
          
          SwitchListTile(
            title: const Text('Crash Reporting'),
            subtitle: const Text('Automatically report crashes'),
            value: AppConstants.enableCrashReporting,
            onChanged: (value) {
              // TODO: Update crash reporting setting
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Storage'),
            subtitle: const Text('Manage app data and cache'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showStorageDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    final appConfig = ref.watch(appConfigProvider);
    
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('Version'),
            subtitle: Text('${appConfig.version} (${appConfig.buildNumber})'),
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Environment'),
            subtitle: Text(appConfig.environment),
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Privacy Policy'),
            trailing: const Icon(Icons.open_in_new, size: 16),
            onTap: () {
              // TODO: Open privacy policy
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Terms of Service'),
            trailing: const Icon(Icons.open_in_new, size: 16),
            onTap: () {
              // TODO: Open terms of service
            },
          ),
          
          const Divider(height: 1),
          
          ListTile(
            title: const Text('Contact Support'),
            trailing: const Icon(Icons.email_outlined, size: 16),
            onTap: () {
              _showContactDialog();
            },
          ),
        ],
      ),
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showVoiceQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Voice Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Standard (8kHz)'),
              subtitle: const Text('Lower quality, smaller files'),
              value: 'standard',
              groupValue: 'high',
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('High (16kHz)'),
              subtitle: const Text('Better quality, larger files'),
              value: 'high',
              groupValue: 'high',
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog() {
    final currentTheme = ref.read(themeModeProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('Light'),
              value: ThemeMode.light,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('Dark'),
              value: ThemeMode.dark,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('System'),
              value: ThemeMode.system,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSessionTimeoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Timeout'),
        content: const Text('Session timeout settings coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Management'),
        content: const Text('Storage management features coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Session Data'),
        content: const Text(
          'This will sign you out and clear all local data. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Clear session data
              Navigator.of(context).pop();
              ref.read(sessionStateProvider.notifier).clearSession();
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Clear Data'),
          ),
        ],
      ),
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Email: ${AppConstants.supportEmail}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Website: ${AppConstants.companyWebsite}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
