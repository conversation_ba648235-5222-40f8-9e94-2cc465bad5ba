name: echo
description: Echo Voice Investment Platform - AI-powered investment discovery through voice conversations
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  
  # Audio & Voice
  flutter_sound: ^9.2.13
  speech_to_text: ^6.6.0
  flutter_tts: ^3.8.3
  just_audio: ^0.9.36
  
  # Platform Integration
  local_auth: ^2.1.7
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.2
  permission_handler: ^11.1.0
  
  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # UI & Navigation
  go_router: ^12.1.3
  flutter_animate: ^4.3.0
  cached_network_image: ^3.3.0
  
  # Utilities
  intl: ^0.18.1
  crypto: ^3.0.3
  uuid: ^4.2.1
  logger: ^2.0.2+1
  
  # Platform-Specific
  siri_shortcuts: ^1.0.0  # iOS only
  android_intent_plus: ^4.0.3  # Android only

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Testing Framework
  mockito: ^5.4.4
  mocktail: ^1.0.2
  build_runner: ^2.4.7
  fake_async: ^1.3.1
  
  # Code Generation
  hive_generator: ^2.0.1
  riverpod_generator: ^2.3.9
  retrofit_generator: ^7.0.8
  json_serializable: ^6.7.1
  
  # Testing Tools
  integration_test:
    sdk: flutter
  patrol: ^2.6.0
  golden_toolkit: ^0.15.0
  
  # Code Quality
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/audio/
    - assets/data/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
