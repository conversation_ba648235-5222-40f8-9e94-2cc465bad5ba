# Echo Flutter Mobile Application

[![CI/CD Pipeline](https://github.com/Cap97-ai/echo/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/Cap97-ai/echo/actions/workflows/ci-cd.yml)
[![Coverage](https://codecov.io/gh/Cap97-ai/echo/branch/main/graph/badge.svg)](https://codecov.io/gh/Cap97-ai/echo)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

Echo is a voice-driven investment discovery platform designed for elite investors. It combines AI-powered conversation management with real-time stock analysis and lead qualification through natural voice interactions.

## 🚀 Features

- **Voice-First Experience**: Natural speech-to-text and text-to-speech interactions
- **AI Investment Assistant**: Trained on investment terminology and market insights
- **Stock Context Detection**: Automatic detection and preview of stocks mentioned in conversations
- **Lead Qualification**: Systematic extraction and scoring of lead information
- **Conversation Continuity**: Persistent conversation history with smart context loading
- **Offline-First Architecture**: Full functionality without internet connection
- **Enterprise Security**: OWASP Mobile Top 10 compliance with biometric authentication

## 📱 Platform Support

- **iOS**: 14.0+ with Face ID/Touch ID support
- **Android**: API 21+ (Android 5.0) with fingerprint/face unlock
- **Performance**: 60 FPS UI, <3s cold start, <200ms voice latency

## 🏗️ Architecture

Echo follows Clean Architecture principles with:

- **Presentation Layer**: Flutter widgets with Riverpod state management
- **Domain Layer**: Business logic and use cases
- **Data Layer**: API clients, local storage (Hive), and repositories
- **Security Layer**: AES-256 encryption, certificate pinning, biometric auth

### Tech Stack

- **Framework**: Flutter 3.16.0+ with Dart 3.0+
- **State Management**: Riverpod 2.4.9 with code generation
- **Local Storage**: Hive 2.2.3 with encryption
- **Audio Processing**: Flutter Sound, Speech-to-Text, TTS
- **Security**: Flutter Secure Storage, Local Auth, Certificate Pinning
- **Testing**: flutter_test, mockito, golden_toolkit, patrol

## 🛠️ Development Setup

### Prerequisites

- Flutter 3.16.0 or later
- Dart 3.0 or later
- Android Studio / Xcode
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Cap97-ai/echo.git
   cd echo/flutter_echo
   ```

2. **Run setup script**
   ```bash
   chmod +x scripts/setup_dev_environment.sh
   ./scripts/setup_dev_environment.sh
   ```

3. **Install dependencies**
   ```bash
   flutter pub get
   dart run build_runner build --delete-conflicting-outputs
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Manual Setup

If you prefer manual setup:

```bash
# Install dependencies
flutter pub get

# Generate code
dart run build_runner build --delete-conflicting-outputs

# Setup pre-commit hooks
pip install pre-commit
pre-commit install

# Run tests
flutter test --coverage
./scripts/check_coverage.sh

# Format code
dart format .
```

## 🧪 Testing

Echo maintains high test coverage with comprehensive testing strategy:

### Test Types

- **Unit Tests** (90%+ coverage): Business logic and services
- **Widget Tests** (85%+ coverage): UI components with golden files
- **Integration Tests**: End-to-end user flows
- **Performance Tests**: Memory, CPU, and frame rate validation

### Running Tests

```bash
# Run all tests with coverage
./scripts/run_tests.sh

# Run specific test suites
flutter test test/unit/
flutter test test/widget/
flutter test integration_test/

# Generate coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 🔒 Security

Echo implements enterprise-grade security:

### Security Features

- **Device Security**: Root/jailbreak detection
- **Data Encryption**: AES-256 for sensitive data
- **Secure Storage**: Platform-specific keychain/keystore
- **Certificate Pinning**: Prevents MITM attacks
- **Biometric Auth**: Face ID, Touch ID, fingerprint
- **Security Monitoring**: Real-time threat detection

### Security Compliance

- OWASP Mobile Top 10 compliance
- SOC 2 Type II controls
- GDPR data protection
- Regular security audits

## 📊 Performance Targets

- **Cold Start**: <3 seconds
- **Voice Latency**: <200ms for recording start
- **UI Responsiveness**: 60 FPS sustained
- **Memory Usage**: <100MB baseline
- **AI Response**: <5 seconds

## 🚀 Deployment

### CI/CD Pipeline

The project uses GitHub Actions for automated:

- Code quality checks
- Security scanning
- Automated testing
- Multi-platform builds
- App store deployment

### Build Commands

```bash
# Build for Android
flutter build apk --release
flutter build appbundle --release

# Build for iOS
flutter build ios --release

# Build all platforms
./scripts/build_all.sh
```

## 📁 Project Structure

```
flutter_echo/
├── lib/
│   ├── main.dart                    # App entry point
│   ├── app.dart                     # App configuration
│   ├── core/                        # Core functionality
│   │   ├── api/                     # HTTP client & endpoints
│   │   ├── security/                # Security services
│   │   ├── storage/                 # Local storage
│   │   └── utils/                   # Utilities
│   └── features/                    # Feature modules
│       ├── auth/                    # Authentication
│       ├── chat/                    # Conversations
│       ├── voice/                   # Voice processing
│       └── stocks/                  # Stock detection
├── test/                            # Test files
│   ├── unit/                        # Unit tests
│   ├── widget/                      # Widget tests
│   └── integration_test/            # Integration tests
├── scripts/                         # Development scripts
└── docs/                           # Documentation
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test additions/changes
- `chore:` Maintenance tasks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/Cap97-ai/echo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Cap97-ai/echo/discussions)

## 🎯 Roadmap

### Phase 1: Foundation (Completed)
- ✅ Project setup and architecture
- ✅ Testing infrastructure
- ✅ Security framework
- ✅ CI/CD pipeline

### Phase 2: Core Features (In Progress)
- 🔄 Authentication system
- 🔄 Voice processing
- 🔄 Chat interface
- 🔄 Stock detection

### Phase 3: Advanced Features
- 📋 Lead qualification
- 📋 Conversation history
- 📋 Performance optimization
- 📋 Analytics integration

### Phase 4: Production
- 📋 App store submission
- 📋 Production monitoring
- 📋 User feedback integration
- 📋 Continuous improvement

---

**Built with ❤️ by the Echo Team**
