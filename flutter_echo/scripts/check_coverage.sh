#!/bin/bash

# Echo Coverage Check Script
# Validates that test coverage meets minimum requirements

set -e

# Configuration
MIN_COVERAGE=90
MIN_WIDGET_COVERAGE=85
COVERAGE_FILE="coverage/lcov.info"
COVERAGE_REPORT="coverage/coverage_report.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🔍 Checking test coverage..."

# Check if coverage file exists
if [ ! -f "$COVERAGE_FILE" ]; then
    echo -e "${RED}❌ Coverage file not found: $COVERAGE_FILE${NC}"
    echo "Please run 'flutter test --coverage' first"
    exit 1
fi

# Generate coverage report
echo "📊 Generating coverage report..."
lcov --summary "$COVERAGE_FILE" > "$COVERAGE_REPORT" 2>&1

# Extract overall coverage percentage
OVERALL_COVERAGE=$(grep -E 'lines\.*: [0-9.]+%' "$COVERAGE_REPORT" | grep -oE '[0-9.]+' | head -1)
FUNCTION_COVERAGE=$(grep -E 'functions\.*: [0-9.]+%' "$COVERAGE_REPORT" | grep -oE '[0-9.]+' | head -1)

echo "📈 Coverage Results:"
echo "   Overall Line Coverage: ${OVERALL_COVERAGE}%"
echo "   Function Coverage: ${FUNCTION_COVERAGE}%"

# Check overall coverage
if (( $(echo "$OVERALL_COVERAGE < $MIN_COVERAGE" | bc -l) )); then
    echo -e "${RED}❌ Overall coverage ${OVERALL_COVERAGE}% is below minimum threshold of ${MIN_COVERAGE}%${NC}"
    COVERAGE_FAILED=true
else
    echo -e "${GREEN}✅ Overall coverage ${OVERALL_COVERAGE}% meets minimum threshold${NC}"
fi

# Check function coverage
if (( $(echo "$FUNCTION_COVERAGE < $MIN_COVERAGE" | bc -l) )); then
    echo -e "${RED}❌ Function coverage ${FUNCTION_COVERAGE}% is below minimum threshold of ${MIN_COVERAGE}%${NC}"
    COVERAGE_FAILED=true
else
    echo -e "${GREEN}✅ Function coverage ${FUNCTION_COVERAGE}% meets minimum threshold${NC}"
fi

# Check widget test coverage specifically
echo "🧪 Checking widget test coverage..."
WIDGET_TEST_FILES=$(find test/widget -name "*.dart" 2>/dev/null | wc -l)
WIDGET_SOURCE_FILES=$(find lib -name "*.dart" -path "*/presentation/widgets/*" 2>/dev/null | wc -l)

if [ "$WIDGET_SOURCE_FILES" -gt 0 ]; then
    WIDGET_COVERAGE_RATIO=$(echo "scale=2; $WIDGET_TEST_FILES / $WIDGET_SOURCE_FILES * 100" | bc)
    echo "   Widget Test Coverage: ${WIDGET_COVERAGE_RATIO}% (${WIDGET_TEST_FILES}/${WIDGET_SOURCE_FILES} files)"
    
    if (( $(echo "$WIDGET_COVERAGE_RATIO < $MIN_WIDGET_COVERAGE" | bc -l) )); then
        echo -e "${YELLOW}⚠️  Widget test coverage ${WIDGET_COVERAGE_RATIO}% is below recommended threshold of ${MIN_WIDGET_COVERAGE}%${NC}"
    else
        echo -e "${GREEN}✅ Widget test coverage ${WIDGET_COVERAGE_RATIO}% meets recommended threshold${NC}"
    fi
fi

# Generate detailed coverage report by directory
echo "📁 Coverage by directory:"
lcov --list "$COVERAGE_FILE" | grep -E '^/' | while read -r line; do
    FILE=$(echo "$line" | awk '{print $1}')
    COVERAGE=$(echo "$line" | awk '{print $2}' | tr -d '%')
    
    # Skip test files and generated files
    if [[ "$FILE" == *"/test/"* ]] || [[ "$FILE" == *".g.dart" ]] || [[ "$FILE" == *".freezed.dart" ]]; then
        continue
    fi
    
    # Determine directory
    DIR=$(dirname "$FILE" | sed 's|.*/lib/||' | cut -d'/' -f1)
    
    if [ -n "$DIR" ]; then
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo -e "   ${RED}$DIR: ${COVERAGE}%${NC}"
        elif (( $(echo "$COVERAGE < 90" | bc -l) )); then
            echo -e "   ${YELLOW}$DIR: ${COVERAGE}%${NC}"
        else
            echo -e "   ${GREEN}$DIR: ${COVERAGE}%${NC}"
        fi
    fi
done | sort | uniq

# Check for uncovered critical files
echo "🔍 Checking critical files coverage..."
CRITICAL_FILES=(
    "lib/core/security/"
    "lib/core/services/"
    "lib/features/auth/"
    "lib/features/chat/domain/"
)

for CRITICAL_DIR in "${CRITICAL_FILES[@]}"; do
    if [ -d "$CRITICAL_DIR" ]; then
        UNCOVERED_FILES=$(lcov --list "$COVERAGE_FILE" | grep "$CRITICAL_DIR" | awk '$2 == "0.0%" {print $1}')
        if [ -n "$UNCOVERED_FILES" ]; then
            echo -e "${RED}❌ Uncovered critical files in $CRITICAL_DIR:${NC}"
            echo "$UNCOVERED_FILES" | while read -r file; do
                echo "   - $file"
            done
            COVERAGE_FAILED=true
        fi
    fi
done

# Generate HTML coverage report
echo "📄 Generating HTML coverage report..."
genhtml "$COVERAGE_FILE" -o coverage/html --title "Echo Test Coverage" --show-details --legend

echo "📊 HTML coverage report generated at: coverage/html/index.html"

# Final result
echo ""
if [ "$COVERAGE_FAILED" = true ]; then
    echo -e "${RED}❌ Coverage check failed!${NC}"
    echo "Please add more tests to meet the minimum coverage requirements."
    echo ""
    echo "Tips to improve coverage:"
    echo "1. Add unit tests for business logic in lib/core/ and lib/features/"
    echo "2. Add widget tests for UI components in lib/features/*/presentation/widgets/"
    echo "3. Focus on critical security and authentication code"
    echo "4. Use 'flutter test --coverage' to generate coverage data"
    echo "5. View detailed report at coverage/html/index.html"
    exit 1
else
    echo -e "${GREEN}✅ All coverage checks passed!${NC}"
    echo "Great job maintaining high test coverage! 🎉"
    exit 0
fi
