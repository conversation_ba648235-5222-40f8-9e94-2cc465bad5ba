#!/bin/bash

# Echo Development Environment Setup Script
# Sets up the complete development environment for the Echo Flutter project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Setting up Echo Flutter development environment...${NC}"

# Check if running on macOS or Linux
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "🖥️  Detected OS: $MACHINE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Flutter
install_flutter() {
    echo -e "${YELLOW}📱 Installing Flutter...${NC}"
    
    if [ "$MACHINE" = "Mac" ]; then
        if command_exists brew; then
            brew install --cask flutter
        else
            echo "Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
    elif [ "$MACHINE" = "Linux" ]; then
        # Download Flutter SDK
        cd /tmp
        wget -O flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz
        tar xf flutter.tar.xz
        sudo mv flutter /opt/
        
        # Add to PATH
        echo 'export PATH="$PATH:/opt/flutter/bin"' >> ~/.bashrc
        export PATH="$PATH:/opt/flutter/bin"
    fi
}

# Function to install Android Studio
install_android_studio() {
    echo -e "${YELLOW}🤖 Installing Android Studio...${NC}"
    
    if [ "$MACHINE" = "Mac" ]; then
        if command_exists brew; then
            brew install --cask android-studio
        fi
    elif [ "$MACHINE" = "Linux" ]; then
        echo "Please download and install Android Studio manually from:"
        echo "https://developer.android.com/studio"
    fi
}

# Function to install Xcode (macOS only)
install_xcode() {
    if [ "$MACHINE" = "Mac" ]; then
        echo -e "${YELLOW}🍎 Checking Xcode installation...${NC}"
        if ! command_exists xcodebuild; then
            echo "Please install Xcode from the Mac App Store"
            echo "https://apps.apple.com/us/app/xcode/id497799835"
        else
            echo -e "${GREEN}✅ Xcode is already installed${NC}"
        fi
    fi
}

# Function to install development tools
install_dev_tools() {
    echo -e "${YELLOW}🛠️  Installing development tools...${NC}"
    
    # Install Git (if not already installed)
    if ! command_exists git; then
        if [ "$MACHINE" = "Mac" ]; then
            brew install git
        elif [ "$MACHINE" = "Linux" ]; then
            sudo apt-get update && sudo apt-get install -y git
        fi
    fi
    
    # Install Node.js (for backend development)
    if ! command_exists node; then
        if [ "$MACHINE" = "Mac" ]; then
            brew install node
        elif [ "$MACHINE" = "Linux" ]; then
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
        fi
    fi
    
    # Install Python (for scripts)
    if ! command_exists python3; then
        if [ "$MACHINE" = "Mac" ]; then
            brew install python
        elif [ "$MACHINE" = "Linux" ]; then
            sudo apt-get install -y python3 python3-pip
        fi
    fi
    
    # Install pre-commit
    if ! command_exists pre-commit; then
        pip3 install pre-commit
    fi
    
    # Install lcov for coverage reports
    if ! command_exists lcov; then
        if [ "$MACHINE" = "Mac" ]; then
            brew install lcov
        elif [ "$MACHINE" = "Linux" ]; then
            sudo apt-get install -y lcov
        fi
    fi
    
    # Install bc for calculations
    if ! command_exists bc; then
        if [ "$MACHINE" = "Mac" ]; then
            brew install bc
        elif [ "$MACHINE" = "Linux" ]; then
            sudo apt-get install -y bc
        fi
    fi
}

# Function to setup Flutter project
setup_flutter_project() {
    echo -e "${YELLOW}📦 Setting up Flutter project...${NC}"
    
    # Get Flutter dependencies
    flutter pub get
    
    # Generate code
    dart run build_runner build --delete-conflicting-outputs
    
    # Run Flutter doctor
    flutter doctor -v
}

# Function to setup pre-commit hooks
setup_pre_commit() {
    echo -e "${YELLOW}🔧 Setting up pre-commit hooks...${NC}"
    
    # Install pre-commit hooks
    pre-commit install
    pre-commit install --hook-type commit-msg
    
    # Run pre-commit on all files to test
    pre-commit run --all-files || true
}

# Function to setup IDE configurations
setup_ide_config() {
    echo -e "${YELLOW}⚙️  Setting up IDE configurations...${NC}"
    
    # Create VS Code settings
    mkdir -p .vscode
    cat > .vscode/settings.json << EOF
{
  "dart.flutterSdkPath": null,
  "dart.lineLength": 120,
  "dart.showTodos": true,
  "dart.runPubGetOnPubspecChanges": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.dart": "dart"
  },
  "search.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.dart_tool": true,
    "**/build": true
  }
}
EOF

    # Create VS Code launch configuration
    cat > .vscode/launch.json << EOF
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Echo (Debug)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--flavor", "development"]
    },
    {
      "name": "Echo (Release)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "flutterMode": "release"
    },
    {
      "name": "Echo Tests",
      "request": "launch",
      "type": "dart",
      "program": "test/"
    }
  ]
}
EOF

    echo -e "${GREEN}✅ VS Code configuration created${NC}"
}

# Function to create development scripts
create_dev_scripts() {
    echo -e "${YELLOW}📝 Creating development scripts...${NC}"
    
    # Make scripts executable
    chmod +x scripts/check_coverage.sh
    
    # Create additional helper scripts
    cat > scripts/run_tests.sh << 'EOF'
#!/bin/bash
echo "🧪 Running all tests..."
flutter test --coverage
./scripts/check_coverage.sh
EOF
    chmod +x scripts/run_tests.sh
    
    cat > scripts/build_all.sh << 'EOF'
#!/bin/bash
echo "🏗️  Building all platforms..."
flutter clean
flutter pub get
dart run build_runner build --delete-conflicting-outputs
flutter build apk --release
flutter build ios --release --no-codesign
echo "✅ Build completed!"
EOF
    chmod +x scripts/build_all.sh
    
    cat > scripts/format_code.sh << 'EOF'
#!/bin/bash
echo "🎨 Formatting code..."
dart format .
dart run import_sorter:main --no-comments
echo "✅ Code formatted!"
EOF
    chmod +x scripts/format_code.sh
}

# Main setup process
main() {
    echo -e "${BLUE}Starting Echo development environment setup...${NC}"
    
    # Check and install Flutter
    if ! command_exists flutter; then
        install_flutter
    else
        echo -e "${GREEN}✅ Flutter is already installed${NC}"
    fi
    
    # Install development tools
    install_dev_tools
    
    # Install Android Studio
    if ! command_exists android; then
        install_android_studio
    fi
    
    # Install Xcode (macOS only)
    install_xcode
    
    # Setup Flutter project
    setup_flutter_project
    
    # Setup pre-commit hooks
    setup_pre_commit
    
    # Setup IDE configuration
    setup_ide_config
    
    # Create development scripts
    create_dev_scripts
    
    echo ""
    echo -e "${GREEN}🎉 Echo development environment setup completed!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Open the project in your preferred IDE (VS Code, Android Studio, or IntelliJ)"
    echo "2. Run 'flutter doctor' to verify your setup"
    echo "3. Run 'flutter run' to start the app"
    echo "4. Run './scripts/run_tests.sh' to execute tests"
    echo ""
    echo "Available scripts:"
    echo "- ./scripts/run_tests.sh - Run all tests with coverage"
    echo "- ./scripts/build_all.sh - Build for all platforms"
    echo "- ./scripts/format_code.sh - Format and organize code"
    echo "- ./scripts/check_coverage.sh - Check test coverage"
    echo ""
    echo "Happy coding! 🚀"
}

# Run main function
main "$@"
