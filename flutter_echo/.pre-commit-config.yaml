# Echo Pre-commit Configuration
# Ensures code quality and security before commits

repos:
  # Flutter/Dart specific hooks
  - repo: https://github.com/invertase/dart-pre-commit
    rev: v0.3.0
    hooks:
      - id: dart-format
        name: Format Dart code
        description: Formats Dart code using dart format
        entry: dart format
        language: system
        files: \.dart$
        args: [--set-exit-if-changed]

      - id: dart-analyze
        name: Analyze Dart code
        description: Analyzes Dart code using dart analyze
        entry: dart analyze
        language: system
        files: \.dart$
        args: [--fatal-infos]

  # General code quality hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        name: Trim trailing whitespace
        description: Trims trailing whitespace
        exclude: \.md$

      - id: end-of-file-fixer
        name: Fix end of files
        description: Ensures files end with a newline

      - id: check-yaml
        name: Check YAML syntax
        description: Checks yaml files for parseable syntax

      - id: check-json
        name: Check JSON syntax
        description: Checks json files for parseable syntax

      - id: check-merge-conflict
        name: Check for merge conflicts
        description: Checks for files that contain merge conflict strings

      - id: check-case-conflict
        name: Check for case conflicts
        description: Checks for files that would conflict in case-insensitive filesystems

      - id: check-added-large-files
        name: Check for large files
        description: Prevents giant files from being committed
        args: ['--maxkb=1000']

      - id: detect-private-key
        name: Detect private keys
        description: Detects presence of private keys

  # Security scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: Detect secrets
        description: Detects high entropy strings that are likely to be passwords
        args: ['--baseline', '.secrets.baseline']
        exclude: \.lock$|\.md$

  # Flutter specific checks
  - repo: local
    hooks:
      - id: flutter-test
        name: Run Flutter tests
        description: Runs Flutter unit tests
        entry: bash -c 'cd flutter_echo && flutter test'
        language: system
        files: \.dart$
        pass_filenames: false

      - id: flutter-pub-get
        name: Flutter pub get
        description: Gets Flutter dependencies
        entry: bash -c 'cd flutter_echo && flutter pub get'
        language: system
        files: pubspec\.yaml$
        pass_filenames: false

      - id: build-runner
        name: Build runner
        description: Generates code using build_runner
        entry: bash -c 'cd flutter_echo && dart run build_runner build --delete-conflicting-outputs'
        language: system
        files: \.dart$
        pass_filenames: false

      - id: check-imports
        name: Check import organization
        description: Checks that imports are properly organized
        entry: bash -c 'cd flutter_echo && dart run import_sorter:main --no-comments'
        language: system
        files: \.dart$
        pass_filenames: false

      - id: security-audit
        name: Security audit
        description: Runs security audit on dependencies
        entry: bash -c 'cd flutter_echo && flutter pub deps && echo "Security audit completed"'
        language: system
        files: pubspec\.yaml$
        pass_filenames: false

      - id: check-coverage
        name: Check test coverage
        description: Ensures minimum test coverage
        entry: bash -c 'cd flutter_echo && flutter test --coverage && ./scripts/check_coverage.sh'
        language: system
        files: \.dart$
        pass_filenames: false

  # Documentation checks
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        name: Lint Markdown files
        description: Runs markdownlint on markdown files
        args: ['--fix']

  # Commit message validation
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.0.0
    hooks:
      - id: conventional-pre-commit
        name: Conventional commit
        description: Ensures commit messages follow conventional commit format
        stages: [commit-msg]

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
