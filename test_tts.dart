/// Simple TTS Test Script
/// Test the text-to-speech functionality independently

import 'dart:io';

void main() async {
  print('🎤 Testing TTS Service...');
  
  // Test 1: Check if OpenAI API key is available
  const apiKey = '********************************************************************************************************************************************************************';
  
  if (apiKey.isEmpty) {
    print('❌ OpenAI API key is empty');
    return;
  } else {
    print('✅ OpenAI API key is configured');
  }
  
  // Test 2: Test a simple HTTP request to OpenAI TTS API
  print('🌐 Testing OpenAI TTS API...');
  
  try {
    final result = await Process.run('curl', [
      '-X', 'POST',
      'https://api.openai.com/v1/audio/speech',
      '-H', 'Authorization: Bearer $apiKey',
      '-H', 'Content-Type: application/json',
      '-d', '{"model": "tts-1", "input": "Hello, this is a test", "voice": "alloy"}',
      '--output', 'test_audio.mp3',
      '--silent',
      '--show-error'
    ]);
    
    if (result.exitCode == 0) {
      print('✅ OpenAI TTS API is working');
      
      // Check if file was created
      final audioFile = File('test_audio.mp3');
      if (await audioFile.exists()) {
        final fileSize = await audioFile.length();
        print('✅ Audio file created: ${fileSize} bytes');
        
        // Clean up
        await audioFile.delete();
      } else {
        print('❌ Audio file was not created');
      }
    } else {
      print('❌ OpenAI TTS API failed: ${result.stderr}');
    }
  } catch (e) {
    print('❌ Error testing TTS API: $e');
  }
  
  print('🏁 TTS test completed');
}
