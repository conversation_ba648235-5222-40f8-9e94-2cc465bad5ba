/**
 * Mobile Session Management Middleware
 * Handles device-specific sessions, JWT tokens, and mobile authentication
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Session storage (in production, use Redis or database)
const activeSessions = new Map();
const deviceSessions = new Map();

// Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'echo-mobile-secret-key';
const SESSION_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
const MAX_SESSIONS_PER_DEVICE = 3;

/**
 * Generate secure session ID
 */
function generateSessionId() {
  return 'session_' + crypto.randomBytes(16).toString('hex');
}

/**
 * Generate device fingerprint
 */
function generateDeviceFingerprint(deviceInfo) {
  const fingerprint = crypto
    .createHash('sha256')
    .update(JSON.stringify({
      deviceId: deviceInfo.deviceId,
      platform: deviceInfo.platform,
      appVersion: deviceInfo.appVersion,
      userAgent: deviceInfo.userAgent || '',
    }))
    .digest('hex');
  
  return fingerprint.substring(0, 16);
}

/**
 * Create mobile session
 */
async function createMobileSession(req, res) {
  try {
    const { deviceId, platform, appVersion, userAgent } = req.body;
    
    // Validate required fields
    if (!deviceId || !platform || !appVersion) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceId, platform, appVersion'
      });
    }
    
    // Generate device fingerprint
    const deviceFingerprint = generateDeviceFingerprint({
      deviceId,
      platform,
      appVersion,
      userAgent
    });
    
    // Check existing sessions for this device
    const existingSessions = deviceSessions.get(deviceFingerprint) || [];
    
    // Clean up expired sessions
    const now = Date.now();
    const validSessions = existingSessions.filter(sessionId => {
      const session = activeSessions.get(sessionId);
      return session && session.expiresAt > now;
    });
    
    // Limit sessions per device
    if (validSessions.length >= MAX_SESSIONS_PER_DEVICE) {
      // Remove oldest session
      const oldestSessionId = validSessions[0];
      activeSessions.delete(oldestSessionId);
      validSessions.shift();
    }
    
    // Create new session
    const sessionId = generateSessionId();
    const expiresAt = now + SESSION_EXPIRY;
    
    const sessionData = {
      sessionId,
      deviceId,
      deviceFingerprint,
      platform,
      appVersion,
      userAgent,
      createdAt: now,
      expiresAt,
      lastActivity: now,
      isActive: true,
      userPreferences: {
        voiceEnabled: true,
        notificationsEnabled: false,
        theme: 'system',
        language: 'en'
      }
    };
    
    // Store session
    activeSessions.set(sessionId, sessionData);
    validSessions.push(sessionId);
    deviceSessions.set(deviceFingerprint, validSessions);
    
    // Generate JWT token
    const sessionToken = jwt.sign(
      {
        sessionId,
        deviceId,
        platform,
        iat: Math.floor(now / 1000),
        exp: Math.floor(expiresAt / 1000)
      },
      JWT_SECRET
    );
    
    // Log session creation
    console.log(`Mobile session created: ${sessionId} for device ${deviceId} (${platform})`);
    
    res.status(201).json({
      success: true,
      sessionId,
      sessionToken,
      expiresAt: new Date(expiresAt).toISOString(),
      userPreferences: sessionData.userPreferences
    });
    
  } catch (error) {
    console.error('Error creating mobile session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create mobile session'
    });
  }
}

/**
 * Validate mobile session middleware
 */
function validateMobileSession(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Missing or invalid authorization header'
      });
    }
    
    const token = authHeader.substring(7);
    
    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET);
    const { sessionId, deviceId, platform } = decoded;
    
    // Check if session exists and is active
    const session = activeSessions.get(sessionId);
    
    if (!session) {
      return res.status(401).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    if (!session.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Session is inactive'
      });
    }
    
    if (session.expiresAt <= Date.now()) {
      // Clean up expired session
      activeSessions.delete(sessionId);
      return res.status(401).json({
        success: false,
        error: 'Session expired'
      });
    }
    
    // Update last activity
    session.lastActivity = Date.now();
    
    // Add session info to request
    req.mobileSession = session;
    req.sessionId = sessionId;
    req.deviceId = deviceId;
    req.platform = platform;
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid session token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Session token expired'
      });
    }
    
    console.error('Error validating mobile session:', error);
    res.status(500).json({
      success: false,
      error: 'Session validation failed'
    });
  }
}

/**
 * Get session info
 */
async function getSessionInfo(req, res) {
  try {
    const session = req.mobileSession;
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    res.json({
      success: true,
      session: {
        sessionId: session.sessionId,
        deviceId: session.deviceId,
        platform: session.platform,
        appVersion: session.appVersion,
        createdAt: new Date(session.createdAt).toISOString(),
        expiresAt: new Date(session.expiresAt).toISOString(),
        lastActivity: new Date(session.lastActivity).toISOString(),
        userPreferences: session.userPreferences
      }
    });
    
  } catch (error) {
    console.error('Error getting session info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get session info'
    });
  }
}

/**
 * Update user preferences
 */
async function updateUserPreferences(req, res) {
  try {
    const session = req.mobileSession;
    const { preferences } = req.body;
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    // Validate preferences
    const allowedPreferences = ['voiceEnabled', 'notificationsEnabled', 'theme', 'language'];
    const validPreferences = {};
    
    for (const [key, value] of Object.entries(preferences || {})) {
      if (allowedPreferences.includes(key)) {
        validPreferences[key] = value;
      }
    }
    
    // Update preferences
    session.userPreferences = {
      ...session.userPreferences,
      ...validPreferences
    };
    
    res.json({
      success: true,
      userPreferences: session.userPreferences
    });
    
  } catch (error) {
    console.error('Error updating user preferences:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update preferences'
    });
  }
}

/**
 * Terminate session
 */
async function terminateSession(req, res) {
  try {
    const sessionId = req.sessionId;
    const session = activeSessions.get(sessionId);
    
    if (session) {
      // Mark session as inactive
      session.isActive = false;
      
      // Remove from active sessions after a delay (for cleanup)
      setTimeout(() => {
        activeSessions.delete(sessionId);
      }, 5000);
      
      console.log(`Mobile session terminated: ${sessionId}`);
    }
    
    res.json({
      success: true,
      message: 'Session terminated successfully'
    });
    
  } catch (error) {
    console.error('Error terminating session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to terminate session'
    });
  }
}

/**
 * Get session statistics
 */
function getSessionStats() {
  const now = Date.now();
  const activeSessions = Array.from(activeSessions.values()).filter(
    session => session.isActive && session.expiresAt > now
  );
  
  const platformStats = activeSessions.reduce((stats, session) => {
    stats[session.platform] = (stats[session.platform] || 0) + 1;
    return stats;
  }, {});
  
  return {
    totalActiveSessions: activeSessions.length,
    platformBreakdown: platformStats,
    totalDevices: deviceSessions.size
  };
}

/**
 * Cleanup expired sessions (run periodically)
 */
function cleanupExpiredSessions() {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [sessionId, session] of activeSessions.entries()) {
    if (session.expiresAt <= now) {
      activeSessions.delete(sessionId);
      cleanedCount++;
    }
  }
  
  // Clean up device sessions
  for (const [deviceFingerprint, sessionIds] of deviceSessions.entries()) {
    const validSessionIds = sessionIds.filter(id => activeSessions.has(id));
    if (validSessionIds.length === 0) {
      deviceSessions.delete(deviceFingerprint);
    } else if (validSessionIds.length !== sessionIds.length) {
      deviceSessions.set(deviceFingerprint, validSessionIds);
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`Cleaned up ${cleanedCount} expired mobile sessions`);
  }
}

// Run cleanup every 5 minutes
setInterval(cleanupExpiredSessions, 5 * 60 * 1000);

module.exports = {
  createMobileSession,
  validateMobileSession,
  getSessionInfo,
  updateUserPreferences,
  terminateSession,
  getSessionStats,
  cleanupExpiredSessions
};
