/**
 * WebSocket Service for Real-time Communication
 * Handles voice data streaming, real-time chat, and live updates
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

// Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'echo-mobile-secret-key';
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const CONNECTION_TIMEOUT = 60000; // 60 seconds

class WebSocketService {
  constructor() {
    this.wss = null;
    this.connections = new Map(); // sessionId -> connection info
    this.rooms = new Map(); // roomId -> Set of sessionIds
    this.heartbeatInterval = null;
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    this.wss = new WebSocket.Server({
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();

    console.log('WebSocket service initialized');
  }

  /**
   * Verify client connection
   */
  verifyClient(info) {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('WebSocket connection rejected: No token provided');
        return false;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, JWT_SECRET);
      
      // Store decoded token for use in connection handler
      info.req.decodedToken = decoded;
      
      return true;
    } catch (error) {
      console.log('WebSocket connection rejected: Invalid token', error.message);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, req) {
    try {
      const { sessionId, deviceId, platform } = req.decodedToken;
      const connectionId = uuidv4();

      // Store connection info
      const connectionInfo = {
        ws,
        sessionId,
        deviceId,
        platform,
        connectionId,
        connectedAt: Date.now(),
        lastPing: Date.now(),
        isAlive: true,
        subscriptions: new Set()
      };

      this.connections.set(sessionId, connectionInfo);

      // Set up WebSocket event handlers
      ws.on('message', (data) => this.handleMessage(sessionId, data));
      ws.on('close', () => this.handleDisconnection(sessionId));
      ws.on('error', (error) => this.handleError(sessionId, error));
      ws.on('pong', () => this.handlePong(sessionId));

      // Send connection confirmation
      this.sendToSession(sessionId, {
        type: 'connection_established',
        connectionId,
        timestamp: new Date().toISOString()
      });

      console.log(`WebSocket connected: ${sessionId} (${platform})`);

    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close(1011, 'Connection setup failed');
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(sessionId, data) {
    try {
      const connection = this.connections.get(sessionId);
      if (!connection) return;

      let message;
      try {
        message = JSON.parse(data);
      } catch (error) {
        // Handle binary data (voice audio)
        this.handleVoiceData(sessionId, data);
        return;
      }

      // Update last activity
      connection.lastPing = Date.now();

      // Route message based on type
      switch (message.type) {
        case 'ping':
          this.handlePing(sessionId);
          break;
        case 'subscribe':
          this.handleSubscription(sessionId, message.channel);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(sessionId, message.channel);
          break;
        case 'voice_start':
          this.handleVoiceStart(sessionId, message);
          break;
        case 'voice_end':
          this.handleVoiceEnd(sessionId, message);
          break;
        case 'chat_message':
          this.handleChatMessage(sessionId, message);
          break;
        case 'typing_start':
          this.handleTypingStart(sessionId);
          break;
        case 'typing_stop':
          this.handleTypingStop(sessionId);
          break;
        default:
          console.log(`Unknown message type: ${message.type}`);
      }

    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  /**
   * Handle voice data streaming
   */
  handleVoiceData(sessionId, audioData) {
    try {
      const connection = this.connections.get(sessionId);
      if (!connection) return;

      // Broadcast voice data to subscribers (for real-time transcription)
      this.broadcastToSubscribers(`voice_${sessionId}`, {
        type: 'voice_data',
        sessionId,
        audioData: audioData.toString('base64'),
        timestamp: new Date().toISOString()
      });

      // Here you would typically:
      // 1. Stream to speech-to-text service
      // 2. Process audio chunks
      // 3. Send partial transcriptions back to client

    } catch (error) {
      console.error('Error handling voice data:', error);
    }
  }

  /**
   * Handle ping message
   */
  handlePing(sessionId) {
    this.sendToSession(sessionId, {
      type: 'pong',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle pong response
   */
  handlePong(sessionId) {
    const connection = this.connections.get(sessionId);
    if (connection) {
      connection.isAlive = true;
      connection.lastPing = Date.now();
    }
  }

  /**
   * Handle channel subscription
   */
  handleSubscription(sessionId, channel) {
    const connection = this.connections.get(sessionId);
    if (!connection) return;

    connection.subscriptions.add(channel);

    // Add to room
    if (!this.rooms.has(channel)) {
      this.rooms.set(channel, new Set());
    }
    this.rooms.get(channel).add(sessionId);

    this.sendToSession(sessionId, {
      type: 'subscribed',
      channel,
      timestamp: new Date().toISOString()
    });

    console.log(`Session ${sessionId} subscribed to ${channel}`);
  }

  /**
   * Handle channel unsubscription
   */
  handleUnsubscription(sessionId, channel) {
    const connection = this.connections.get(sessionId);
    if (!connection) return;

    connection.subscriptions.delete(channel);

    // Remove from room
    const room = this.rooms.get(channel);
    if (room) {
      room.delete(sessionId);
      if (room.size === 0) {
        this.rooms.delete(channel);
      }
    }

    this.sendToSession(sessionId, {
      type: 'unsubscribed',
      channel,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle voice recording start
   */
  handleVoiceStart(sessionId, message) {
    this.broadcastToSubscribers(`session_${sessionId}`, {
      type: 'voice_recording_started',
      sessionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle voice recording end
   */
  handleVoiceEnd(sessionId, message) {
    this.broadcastToSubscribers(`session_${sessionId}`, {
      type: 'voice_recording_ended',
      sessionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle chat message
   */
  handleChatMessage(sessionId, message) {
    // Broadcast to conversation participants
    this.broadcastToSubscribers(`conversation_${message.conversationId}`, {
      type: 'new_message',
      sessionId,
      message: message.content,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle typing indicators
   */
  handleTypingStart(sessionId) {
    this.broadcastToSubscribers(`session_${sessionId}`, {
      type: 'typing_start',
      sessionId,
      timestamp: new Date().toISOString()
    });
  }

  handleTypingStop(sessionId) {
    this.broadcastToSubscribers(`session_${sessionId}`, {
      type: 'typing_stop',
      sessionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle connection disconnection
   */
  handleDisconnection(sessionId) {
    const connection = this.connections.get(sessionId);
    if (!connection) return;

    // Remove from all rooms
    for (const channel of connection.subscriptions) {
      const room = this.rooms.get(channel);
      if (room) {
        room.delete(sessionId);
        if (room.size === 0) {
          this.rooms.delete(channel);
        }
      }
    }

    // Remove connection
    this.connections.delete(sessionId);

    console.log(`WebSocket disconnected: ${sessionId}`);
  }

  /**
   * Handle connection error
   */
  handleError(sessionId, error) {
    console.error(`WebSocket error for session ${sessionId}:`, error);
    this.handleDisconnection(sessionId);
  }

  /**
   * Send message to specific session
   */
  sendToSession(sessionId, message) {
    const connection = this.connections.get(sessionId);
    if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      connection.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Error sending message to session:', error);
      return false;
    }
  }

  /**
   * Broadcast message to channel subscribers
   */
  broadcastToSubscribers(channel, message) {
    const room = this.rooms.get(channel);
    if (!room) return;

    let sentCount = 0;
    for (const sessionId of room) {
      if (this.sendToSession(sessionId, message)) {
        sentCount++;
      }
    }

    return sentCount;
  }

  /**
   * Broadcast to all connected clients
   */
  broadcastToAll(message) {
    let sentCount = 0;
    for (const sessionId of this.connections.keys()) {
      if (this.sendToSession(sessionId, message)) {
        sentCount++;
      }
    }
    return sentCount;
  }

  /**
   * Start heartbeat to detect dead connections
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      
      for (const [sessionId, connection] of this.connections.entries()) {
        if (!connection.isAlive || (now - connection.lastPing) > CONNECTION_TIMEOUT) {
          console.log(`Terminating dead connection: ${sessionId}`);
          connection.ws.terminate();
          this.handleDisconnection(sessionId);
        } else {
          connection.isAlive = false;
          connection.ws.ping();
        }
      }
    }, HEARTBEAT_INTERVAL);
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      totalRooms: this.rooms.size,
      connectionsByPlatform: Array.from(this.connections.values()).reduce((stats, conn) => {
        stats[conn.platform] = (stats[conn.platform] || 0) + 1;
        return stats;
      }, {})
    };
  }

  /**
   * Shutdown WebSocket service
   */
  shutdown() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    if (this.wss) {
      this.wss.close();
    }

    console.log('WebSocket service shutdown');
  }
}

module.exports = new WebSocketService();
