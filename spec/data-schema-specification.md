# Echo Data Schema Specification

## Overview

This document defines the comprehensive data schema, models, validation rules, and storage patterns for the Echo (ShareFlix) voice-driven investment discovery platform. It covers both the current Node.js backend implementation and the target Flutter mobile application data structures.

## Table of Contents

1. [Core Data Models](#core-data-models)
2. [Database Schema](#database-schema)
3. [Validation Rules](#validation-rules)
4. [Encryption Patterns](#encryption-patterns)
5. [Storage Architecture](#storage-architecture)
6. [Migration Strategy](#migration-strategy)
7. [API Data Contracts](#api-data-contracts)

## Core Data Models

### 1. Conversation Exchange Model

**Current Implementation (Node.js)**
```json
{
  "id": "string (16-char secure token)",
  "timestamp": "ISO-8601 datetime",
  "userMessage": "string (encrypted, max 2000 chars)",
  "aiResponse": "string (encrypted, max 5000 chars)",
  "leadData": "object (encrypted, optional)",
  "userId": "string (user identifier)",
  "sessionId": "string (session identifier)"
}
```

**Target Implementation (Flutter/Dart)**
```dart
@HiveType(typeId: 0)
class ConversationEntity extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String sessionId;
  
  @HiveField(2)
  List<MessageEntity> messages;
  
  @HiveField(3)
  DateTime timestamp;
  
  @HiveField(4)
  bool synced;
  
  @HiveField(5)
  Map<String, dynamic>? leadData;
  
  @HiveField(6)
  String? userId;
  
  @HiveField(7)
  ConversationMetadata metadata;
}
```

### 2. Message Entity Model

**Flutter/Dart Implementation**
```dart
@HiveType(typeId: 1)
class MessageEntity extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String content;
  
  @HiveField(2)
  MessageType type; // user, ai, system
  
  @HiveField(3)
  DateTime timestamp;
  
  @HiveField(4)
  bool encrypted;
  
  @HiveField(5)
  List<StockMention>? stockMentions;
  
  @HiveField(6)
  MessageMetadata? metadata;
}

@HiveType(typeId: 2)
enum MessageType {
  @HiveField(0)
  user,
  
  @HiveField(1)
  ai,
  
  @HiveField(2)
  system
}
```

### 3. Stock Data Model

**Current Implementation**
```json
{
  "id": "string (ticker symbol)",
  "ticker": "string (3-5 chars, uppercase)",
  "companyName": "string (max 200 chars)",
  "price": "number (decimal, 2 places)",
  "changePercent": "number (decimal, 2 places)",
  "marketCap": "string (formatted with suffix)",
  "priceTarget": "number (decimal, 2 places)",
  "sector": "string (max 100 chars)",
  "industry": "string (max 100 chars)",
  "thesis": "string (max 1000 chars)",
  "logo": "string (file path)",
  "keywords": "array of strings",
  "lastUpdated": "ISO-8601 datetime",
  "analystRating": "enum (Buy|Hold|Sell|Speculative Buy)",
  "peRatio": "number (decimal, 2 places, nullable)",
  "dividendYield": "number (decimal, 2 places)"
}
```

**Flutter/Dart Implementation**
```dart
@HiveType(typeId: 3)
class StockEntity extends HiveObject {
  @HiveField(0)
  String ticker;
  
  @HiveField(1)
  String companyName;
  
  @HiveField(2)
  double price;
  
  @HiveField(3)
  double changePercent;
  
  @HiveField(4)
  String marketCap;
  
  @HiveField(5)
  double priceTarget;
  
  @HiveField(6)
  String sector;
  
  @HiveField(7)
  String industry;
  
  @HiveField(8)
  String thesis;
  
  @HiveField(9)
  List<String> keywords;
  
  @HiveField(10)
  DateTime lastUpdated;
  
  @HiveField(11)
  AnalystRating analystRating;
  
  @HiveField(12)
  double? peRatio;
  
  @HiveField(13)
  double dividendYield;
  
  @HiveField(14)
  String? logoUrl;
}

@HiveType(typeId: 4)
enum AnalystRating {
  @HiveField(0)
  buy,
  
  @HiveField(1)
  hold,
  
  @HiveField(2)
  sell,
  
  @HiveField(3)
  speculativeBuy
}
```

### 4. Lead Information Model

**Current Implementation**
```json
{
  "name": "string (max 100 chars, optional)",
  "email": "string (email format, optional)",
  "phone": "string (mobile format, optional)",
  "company": "string (max 200 chars, optional)",
  "investmentExperience": "enum (beginner|intermediate|advanced|professional)",
  "portfolioSize": "number (integer, optional)",
  "investmentGoals": "string (max 500 chars, optional)",
  "riskTolerance": "enum (conservative|moderate|aggressive)",
  "timeline": "enum (short-term|medium-term|long-term)",
  "hasAdvisor": "boolean (optional)",
  "qualificationScore": "number (0-100, calculated)"
}
```

**Flutter/Dart Implementation**
```dart
@HiveType(typeId: 5)
class LeadEntity extends HiveObject {
  @HiveField(0)
  String? name;
  
  @HiveField(1)
  String? email;
  
  @HiveField(2)
  String? phone;
  
  @HiveField(3)
  String? company;
  
  @HiveField(4)
  InvestmentExperience? investmentExperience;
  
  @HiveField(5)
  int? portfolioSize;
  
  @HiveField(6)
  String? investmentGoals;
  
  @HiveField(7)
  RiskTolerance? riskTolerance;
  
  @HiveField(8)
  InvestmentTimeline? timeline;
  
  @HiveField(9)
  bool? hasAdvisor;
  
  @HiveField(10)
  double qualificationScore;
  
  @HiveField(11)
  DateTime extractedAt;
  
  @HiveField(12)
  String conversationId;
}
```

### 5. User Account Model

**Current Implementation**
```json
{
  "id": "string (timestamp-based unique ID)",
  "username": "string (3-30 chars, alphanumeric + _-)",
  "email": "string (encrypted, email format)",
  "password": "string (bcrypt hash)",
  "role": "enum (user|admin)",
  "isActive": "boolean",
  "createdAt": "ISO-8601 datetime",
  "lastLogin": "ISO-8601 datetime (optional)"
}
```

**Flutter/Dart Implementation**
```dart
@HiveType(typeId: 6)
class UserEntity extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String username;
  
  @HiveField(2)
  String email; // Encrypted in storage
  
  @HiveField(3)
  UserRole role;
  
  @HiveField(4)
  bool isActive;
  
  @HiveField(5)
  DateTime createdAt;
  
  @HiveField(6)
  DateTime? lastLogin;
  
  @HiveField(7)
  UserPreferences preferences;
  
  @HiveField(8)
  String? profileImageUrl;
}

@HiveType(typeId: 7)
enum UserRole {
  @HiveField(0)
  user,
  
  @HiveField(1)
  admin
}
```

## Database Schema

### File-Based Storage (Current)

**Directory Structure**
```
data/
├── conversation_history.json      # Encrypted conversation exchanges
├── conversation_history.txt       # Human-readable backup
├── users.json                     # User accounts with bcrypt passwords
├── encryption.key                 # AES-256 encryption key
└── uploads/                       # Voice recordings (temporary)
    └── *.wav, *.mp3, *.webm
```

**Static Data Files**
```
stockData.json                     # Stock information database
conversations/
├── Collective Chat.json           # Community discussions
└── Spec Folder/                   # Documentation conversations
```

### Mobile Storage (Target)

**Hive Database Structure**
```dart
// Box names and type IDs
const String conversationsBox = 'conversations';     // TypeId: 0
const String messagesBox = 'messages';               // TypeId: 1
const String stocksBox = 'stocks';                   // TypeId: 3
const String leadsBox = 'leads';                     // TypeId: 5
const String userBox = 'user';                       // TypeId: 6
const String settingsBox = 'settings';               // TypeId: 8
```

**Encryption Configuration**
```dart
// AES-256 encryption for sensitive data
final encryptionKey = await FlutterSecureStorage().read(key: 'hive_key');
final encryptedBox = await Hive.openBox<ConversationEntity>(
  conversationsBox,
  encryptionCipher: HiveAesCipher(base64Decode(encryptionKey!)),
);
```

## Validation Rules

### 1. Message Validation

**Input Constraints**
- **Length**: 1-2000 characters for user messages, 1-5000 for AI responses
- **Content Security**: XSS prevention, script tag filtering, SSML injection protection
- **Encoding**: UTF-8 with proper sanitization
- **Rate Limiting**: Max 100 requests per 15-minute window per IP

**Validation Implementation**
```javascript
// Current Node.js validation
body('message')
  .notEmpty()
  .withMessage('Message is required')
  .isLength({ min: 1, max: 2000 })
  .withMessage('Message must be between 1 and 2000 characters')
  .custom((value) => {
    const suspiciousPatterns = [
      /<script/i, /javascript:/i, /on\w+\s*=/i,
      /data:text\/html/i, /vbscript:/i
    ];
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(value)) {
        throw new Error('Message contains potentially malicious content');
      }
    }
    return true;
  })
```

**Flutter Validation**
```dart
class MessageValidator {
  static String? validateUserMessage(String? value) {
    if (value == null || value.isEmpty) {
      return 'Message is required';
    }
    if (value.length > 2000) {
      return 'Message must be less than 2000 characters';
    }
    if (_containsSuspiciousContent(value)) {
      return 'Message contains invalid content';
    }
    return null;
  }

  static bool _containsSuspiciousContent(String value) {
    final suspiciousPatterns = [
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
    ];
    return suspiciousPatterns.any((pattern) => pattern.hasMatch(value));
  }
}
```

### 2. Lead Data Validation

**Field Constraints**
- **Name**: Optional, max 100 characters, alphanumeric + spaces
- **Email**: Optional, valid email format, normalized
- **Phone**: Optional, valid mobile format (international)
- **Company**: Optional, max 200 characters
- **Investment Experience**: Enum validation (beginner|intermediate|advanced|professional)
- **Portfolio Size**: Optional, positive integer, max 999,999,999
- **Investment Goals**: Optional, max 500 characters
- **Risk Tolerance**: Enum validation (conservative|moderate|aggressive)

**Qualification Score Calculation**
```javascript
function calculateQualificationScore(leadData) {
  let score = 0;

  // Contact information completeness (30 points)
  if (leadData.name) score += 10;
  if (leadData.email) score += 15;
  if (leadData.phone) score += 5;

  // Investment profile completeness (40 points)
  if (leadData.investmentExperience) {
    const expScores = { beginner: 5, intermediate: 10, advanced: 15, professional: 20 };
    score += expScores[leadData.investmentExperience] || 0;
  }
  if (leadData.portfolioSize) {
    if (leadData.portfolioSize >= 1000000) score += 20;
    else if (leadData.portfolioSize >= 100000) score += 15;
    else if (leadData.portfolioSize >= 10000) score += 10;
    else score += 5;
  }

  // Investment goals and risk profile (30 points)
  if (leadData.investmentGoals) score += 10;
  if (leadData.riskTolerance) score += 10;
  if (leadData.timeline) score += 10;

  return Math.min(score, 100); // Cap at 100
}
```

### 3. Stock Data Validation

**Field Constraints**
- **Ticker**: 1-5 characters, uppercase letters only
- **Company Name**: 1-200 characters, required
- **Price**: Positive decimal, max 2 decimal places
- **Change Percent**: Decimal, can be negative, max 2 decimal places
- **Market Cap**: Formatted string with suffix (K, M, B, T)
- **Keywords**: Array of 1-20 strings, each 1-50 characters
- **Thesis**: 1-1000 characters, required
- **Last Updated**: ISO-8601 datetime, required

**Stock Detection Validation**
```dart
class StockDetectionValidator {
  static bool isValidTicker(String ticker) {
    return RegExp(r'^[A-Z]{1,5}$').hasMatch(ticker);
  }

  static bool isValidPrice(double price) {
    return price > 0 && price < 1000000;
  }

  static bool isValidMarketCap(String marketCap) {
    return RegExp(r'^\d+(\.\d{1,2})?[KMBT]$').hasMatch(marketCap);
  }
}
```

### 4. User Account Validation

**Registration Constraints**
- **Username**: 3-30 characters, alphanumeric + underscore/hyphen
- **Email**: Valid email format, normalized, unique
- **Password**: Min 8 characters, must contain uppercase, lowercase, number, special character

**Security Validation**
```javascript
// Password strength validation
body('password')
  .isLength({ min: 8 })
  .withMessage('Password must be at least 8 characters long')
  .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .withMessage('Password must contain uppercase, lowercase, number, and special character')
```

## Encryption Patterns

### 1. Current Node.js Implementation

**AES-256-GCM Encryption**
```javascript
class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 16; // 128 bits
    this.tagLength = 16; // 128 bits
  }

  encrypt(plaintext) {
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);

    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      algorithm: this.algorithm
    };
  }
}
```

**Field-Level Encryption**
```javascript
// Encrypt sensitive conversation fields
function encryptConversation(conversation) {
  const sensitiveFields = ['userMessage', 'aiResponse', 'leadData'];
  return encryptSensitiveFields(conversation, sensitiveFields);
}

// Encrypt sensitive user fields
function encryptUserData(userData) {
  const sensitiveFields = ['email', 'phone', 'personalInfo'];
  return encryptSensitiveFields(userData, sensitiveFields);
}
```

### 2. Flutter Mobile Implementation

**Flutter Secure Storage + Hive Encryption**
```dart
class EncryptionService {
  static const String _keyAlias = 'hive_encryption_key';
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  static Future<Uint8List> getOrCreateEncryptionKey() async {
    String? keyString = await _secureStorage.read(key: _keyAlias);

    if (keyString == null) {
      final key = Hive.generateSecureKey();
      await _secureStorage.write(
        key: _keyAlias,
        value: base64Encode(key),
      );
      return key;
    }

    return base64Decode(keyString);
  }
}
```

**Selective Field Encryption**
```dart
class ConversationEncryption {
  static ConversationEntity encryptSensitiveFields(ConversationEntity conversation) {
    return conversation.copyWith(
      messages: conversation.messages.map((message) =>
        message.copyWith(
          content: message.type == MessageType.user || message.type == MessageType.ai
            ? _encryptString(message.content)
            : message.content,
        ),
      ).toList(),
      leadData: conversation.leadData != null
        ? _encryptMap(conversation.leadData!)
        : null,
    );
  }
}
```

## Storage Architecture

### 1. Current File-Based System

**Conversation Storage Pattern**
- **Primary**: `data/conversation_history.json` (encrypted, system use)
- **Backup**: `data/conversation_history.txt` (readable, human review)
- **Retention**: Last 100 conversations maximum
- **Format**: Array of encrypted conversation exchanges

**User Data Storage**
- **Location**: `data-x/users.json`
- **Security**: bcrypt password hashing, email encryption
- **Structure**: Array of user objects with role-based access

**Static Data Management**
- **Stock Database**: `stockData.json` (10 stocks, metadata tracking)
- **Community Chats**: `conversations/Collective Chat.json`
- **Documentation**: `conversations/Spec Folder/`

### 2. Target Mobile Storage Architecture

**Hive Database Strategy**
```dart
// Database initialization
class DatabaseService {
  static Future<void> initialize() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocDir.path);

    // Register adapters
    Hive.registerAdapter(ConversationEntityAdapter());
    Hive.registerAdapter(MessageEntityAdapter());
    Hive.registerAdapter(StockEntityAdapter());
    Hive.registerAdapter(LeadEntityAdapter());
    Hive.registerAdapter(UserEntityAdapter());

    // Open encrypted boxes
    final encryptionKey = await EncryptionService.getOrCreateEncryptionKey();

    await Hive.openBox<ConversationEntity>(
      'conversations',
      encryptionCipher: HiveAesCipher(encryptionKey),
    );

    await Hive.openBox<StockEntity>('stocks');
    await Hive.openBox<UserEntity>(
      'user',
      encryptionCipher: HiveAesCipher(encryptionKey),
    );
  }
}
```

**Data Synchronization Strategy**
```dart
class SyncService {
  static Future<void> syncConversations() async {
    final localBox = Hive.box<ConversationEntity>('conversations');
    final unsyncedConversations = localBox.values
        .where((conv) => !conv.synced)
        .toList();

    for (final conversation in unsyncedConversations) {
      try {
        await ApiService.uploadConversation(conversation);
        conversation.synced = true;
        await conversation.save();
      } catch (e) {
        // Handle sync failure, retry later
        print('Sync failed for conversation ${conversation.id}: $e');
      }
    }
  }
}
```

## Migration Strategy

### 1. Data Format Migration

**Conversation Data Migration**
```dart
class ConversationMigrator {
  static Future<List<ConversationEntity>> migrateFromJson(String jsonData) async {
    final List<dynamic> rawConversations = jsonDecode(jsonData);
    final List<ConversationEntity> migratedConversations = [];

    for (final rawConv in rawConversations) {
      // Decrypt legacy format
      final decrypted = await LegacyEncryption.decrypt(rawConv);

      // Convert to new format
      final conversation = ConversationEntity(
        id: decrypted['id'],
        sessionId: decrypted['sessionId'],
        timestamp: DateTime.parse(decrypted['timestamp']),
        synced: false,
        leadData: decrypted['leadData'],
        userId: decrypted['userId'],
        messages: [
          MessageEntity(
            id: '${decrypted['id']}_user',
            content: decrypted['userMessage'],
            type: MessageType.user,
            timestamp: DateTime.parse(decrypted['timestamp']),
            encrypted: true,
          ),
          MessageEntity(
            id: '${decrypted['id']}_ai',
            content: decrypted['aiResponse'],
            type: MessageType.ai,
            timestamp: DateTime.parse(decrypted['timestamp']).add(Duration(seconds: 1)),
            encrypted: true,
          ),
        ],
        metadata: ConversationMetadata(
          version: '2.0',
          migrated: true,
          originalFormat: 'node_js_v1',
        ),
      );

      migratedConversations.add(conversation);
    }

    return migratedConversations;
  }
}
```

### 2. Schema Versioning

**Version Management**
```dart
class SchemaVersion {
  static const String current = '2.0';
  static const Map<String, String> migrations = {
    '1.0': '2.0', // Node.js to Flutter migration
  };

  static Future<void> migrateIfNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final currentVersion = prefs.getString('schema_version') ?? '1.0';

    if (currentVersion != SchemaVersion.current) {
      await _performMigration(currentVersion, SchemaVersion.current);
      await prefs.setString('schema_version', SchemaVersion.current);
    }
  }
}
```

## API Data Contracts

### 1. REST API Endpoints

**Conversation Management**
```typescript
// POST /api/conversations
interface CreateConversationRequest {
  message: string;
  sessionId?: string;
  leadData?: LeadData;
}

interface CreateConversationResponse {
  id: string;
  response: string;
  leadData?: LeadData;
  stockMentions?: StockMention[];
  sessionId: string;
  timestamp: string;
}

// GET /api/conversations
interface GetConversationsResponse {
  conversations: ConversationSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}
```

**Stock Data API**
```typescript
// GET /api/stocks/search
interface StockSearchRequest {
  query: string;
  limit?: number;
}

interface StockSearchResponse {
  stocks: StockData[];
  matches: {
    ticker: StockMatch[];
    company: StockMatch[];
    keywords: StockMatch[];
  };
}

interface StockMatch {
  stock: StockData;
  confidence: number;
  matchType: 'ticker' | 'company' | 'keyword';
  matchedText: string;
}
```

### 2. WebSocket Events

**Real-time Communication**
```typescript
// Client to Server
interface VoiceDataEvent {
  type: 'voice_data';
  data: {
    audioData: string; // base64 encoded
    format: 'wav' | 'mp3' | 'webm';
    sessionId: string;
  };
}

// Server to Client
interface TranscriptionEvent {
  type: 'transcription';
  data: {
    text: string;
    confidence: number;
    sessionId: string;
  };
}

interface AIResponseEvent {
  type: 'ai_response';
  data: {
    response: string;
    stockMentions: StockMention[];
    leadData?: LeadData;
    sessionId: string;
  };
}
```

## Data Integrity & Backup

### 1. Validation Checksums

**Data Integrity Verification**
```dart
class DataIntegrityService {
  static String calculateChecksum(ConversationEntity conversation) {
    final data = jsonEncode(conversation.toJson());
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  static Future<bool> verifyIntegrity(ConversationEntity conversation) async {
    final storedChecksum = conversation.metadata?.checksum;
    if (storedChecksum == null) return false;

    final calculatedChecksum = calculateChecksum(conversation);
    return storedChecksum == calculatedChecksum;
  }
}
```

### 2. Backup Strategy

**Automated Backup System**
```dart
class BackupService {
  static Future<void> createBackup() async {
    final conversations = Hive.box<ConversationEntity>('conversations');
    final backup = {
      'version': SchemaVersion.current,
      'timestamp': DateTime.now().toIso8601String(),
      'conversations': conversations.values.map((c) => c.toJson()).toList(),
    };

    final backupData = jsonEncode(backup);
    final encryptedBackup = await EncryptionService.encrypt(backupData);

    // Store locally and optionally upload to cloud
    await _storeLocalBackup(encryptedBackup);
    await _uploadToCloud(encryptedBackup);
  }
}
```

---

## Implementation Checklist

### Phase 1: Documentation Complete ✅
- [x] Core data models defined
- [x] Validation rules specified
- [x] Encryption patterns documented
- [x] Migration strategy outlined

### Phase 2: Backend API Enhancement (Week 6-7)
- [ ] Implement mobile-optimized endpoints
- [ ] Add conversation grouping and search APIs
- [ ] Create lead extraction and stock detection endpoints
- [ ] Add mobile session management

### Phase 3: Flutter Implementation (Week 8-17)
- [ ] Set up Hive database with encryption
- [ ] Implement data models and adapters
- [ ] Create validation services
- [ ] Build sync and backup systems
- [ ] Add data integrity verification

### Phase 4: Testing & Validation (Week 16-17)
- [ ] Unit tests for all data models
- [ ] Integration tests for sync operations
- [ ] Performance tests for large datasets
- [ ] Security validation for encryption
